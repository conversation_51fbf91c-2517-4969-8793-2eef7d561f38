package net.bioknow.cdtms.studyClose;


import net.bioknow.uap.dbcp.DBSessFactory;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.webio.dbmanage.DBConnPool;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

public class TimerClouseStudy extends TimerTask {

    public void run() {

            try {

//                DAODataMng daoDataMng = new DAODataMng("cdtmsen_val");
                DAODataMng daoDataMng = new DAODataMng("cdtmsen_val");
                long currentTime = new Date().getTime();

                List<Map> stuydList = daoDataMng.listRecord("xsht", "obj.closed is null  and (obj.zt in ('50','60','40'))", null, 10000);

                ArrayList<Map> StudyToCloseList = new ArrayList<>();


                for (Map stuydMap : stuydList) {


                    Long studyId = (Long) stuydMap.get("id");
                    String studyCode = (String) stuydMap.get("studyid");
                    String studyStatus = (String) stuydMap.get("zt");


                        List<Map> studyProcessList = daoDataMng.listRecord("spmxb", "obj.sjb ='xsht' and obj.jl='" + studyId + "'", "obj.blsj desc", 1);

                        if (CollectionUtils.isNotEmpty(studyProcessList)) {
                            Map studyProcessMap = studyProcessList.get(0);
                            Date ProcessDate = (Date) studyProcessMap.get("blsj");

                            long diffInMillies = currentTime - ProcessDate.getTime();
                            long diffDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);

                            if (diffDays >= 90 && (StringUtils.equals("50", String.valueOf(studyStatus)) || StringUtils.equals("60", String.valueOf(studyStatus)))) {
                                HashMap<Object, Object> StudySaveMap = new HashMap<>();
                                StudySaveMap.put("id", studyId);
                                StudySaveMap.put("closed", "1");
                                StudyToCloseList.add(StudySaveMap);

                                continue;
                            }

                            if (diffDays >= 180  && StringUtils.equals("40", String.valueOf(studyStatus))) {
                                HashMap<Object, Object> StudySaveMap = new HashMap<>();
                                StudySaveMap.put("id", studyId);
                                StudySaveMap.put("closed", "1");
                                StudyToCloseList.add(StudySaveMap);

                                continue;
                            }

                        }




//                    if (StringUtils.equals("2", (String) stuydMap.get("active"))) {
//                        List<Map> stuydDiaryList = daoDataMng.listRecord("xmrznr", "obj.studyid='" + studyCode + "'", "obj.createtime desc", 1);
//
//                        if (CollectionUtils.isNotEmpty(stuydDiaryList)) {
//
//                            Map stuydDiaryMap = stuydDiaryList.get(0);
//                            Date createDate = (Date) stuydDiaryMap.get("createtime");
//
//                            long diffInMillies = currentTime - createDate.getTime();
//                            long diffDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
//
//                            if (diffDays >= 210) {
//                                HashMap<Object, Object> StudySaveMap = new HashMap<>();
//                                StudySaveMap.put("id", studyId);
//                                StudySaveMap.put("closed", "1");
//                                StudyToCloseList.add(StudySaveMap);
//
//                                continue;
//                            }
//
//                        }
//
//                    }


                }


                if (CollectionUtils.isNotEmpty(StudyToCloseList)) {
                    daoDataMng.saveBatch("xsht",StudyToCloseList,2l,null);
                }


            } catch (Exception e) {
                Log.info("TimerNotice 2: " + e.getMessage());
                Log.error("", e);
            } finally {
                try {
                    DBConnPool.close();
                    DBSessFactory.close();
                } catch (Exception ee) {
                    Log.info("TimerNotice 3: " + ee.getMessage());
                }
            }

    }

}
