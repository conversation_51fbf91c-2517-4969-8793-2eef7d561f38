package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.services.core.ApiResult;
import net.bioknow.services.uap.dbdatamng.function.DTRecordFuncActionNew;
import net.bioknow.services.uap.dbdatamng.function.FuncInfoBeanNew;
import net.bioknow.services.uap.dbdatamng.function.FuncParamBeanNew;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class DTRFlightpdfSetpositionVue extends DTRecordFuncActionNew {


	public boolean canUse(int auth, String tableid, Long recordid) {

		try {

			if (!StringUtils.equals(tableid,"esign_instance")) {
				return false;
			}
			String projectId = SessUtil.getSessInfo().getProjectid();

			DAODataMng daoDataMng=new DAODataMng(projectId);
			Map esignInstanceMap = daoDataMng.getRecord(tableid, recordid);

			if (!StringUtils.equals(SessUtil.getSessInfo().getUserid(),String.valueOf(esignInstanceMap.get("userid")))) {

				return false;
			}

				String esignInstanceStatus = (String) esignInstanceMap.get("status");
			if (StringUtils.equals(esignInstanceStatus,"1")) {
				return true;
			}

		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}

	@Override
	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBeanNew fpb) {
		try {

			String projectid = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng = new DAODataMng(projectid);

			List esignFileList = daoDataMng.listRecord("esign_file", "obj.esign_instance_id=" + fpb.getRecordid(), null, 1);

			Map esignInstanceRecordMap = daoDataMng.getRecord(fpb.getTableid(), Long.valueOf(fpb.getRecordid()));
			String signFlowId = (String) esignInstanceRecordMap.get("sign_flow_id");
			Map esignFileMap = (Map) esignFileList.get(0);
			String eSignfileKey = (String) esignFileMap.get("esign_file_key");
			List<Map<String, String>> listR = DAOLightpdfSignIntegrate.listRule(projectid, "1");
			String esignUrl = listR.get(0).get(DAOLightpdfSignIntegrate.esignUrl);

			String redirectUrl = esignUrl+"/set-sign?file_id="+eSignfileKey+"&task_id="+signFlowId;


			HashMap map = new HashMap();
			map.put("url", redirectUrl);
			response.getOutputStream().write(ApiResult.ok("ok", map).getBytes(StandardCharsets.UTF_8));
			response.getOutputStream().close();

//			this.forwardByUri(request,response,redirectUrl);
//			this.redirectByUrl(request,response,redirectUrl);
//			this.forward();
		} catch (Exception e) {
			Log.error("",e);
		}
	}

	@Override
	public FuncInfoBeanNew getFIB(String tableid) {
		FuncInfoBeanNew fib = new FuncInfoBeanNew("cdtms_DTRFlightpdfSetpositionVue");


		fib.setName("签字位置设置");
		fib.setType(FuncInfoBeanNew.FUNCTYPE_TOPMASKDIV);




		return fib;
	}

}
