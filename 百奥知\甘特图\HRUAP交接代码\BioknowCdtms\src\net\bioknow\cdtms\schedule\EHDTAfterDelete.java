package net.bioknow.cdtms.schedule;

import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbdatamng.face.AfterDeleteFace;
import net.bioknow.uap.entitymng.EntityUtil;

import java.util.List;
import java.util.Map;

public class EHDTAfterDelete implements AfterDeleteFace {

    @Override
    public boolean isTableTrigEvent(String tableid, String projectId) {
        DAODbApi apidao = new DAODbApi(projectId);
        Map mapT = apidao.getMapTable(EntityUtil.getTableId(EntityScheduleTemplate.class));
        if (mapT == null) return false;
        List<EntityScheduleTemplate> listentity = EntityUtil.listEntity(projectId, "obj.tableid = '" + tableid + "'", "", EntityScheduleTemplate.class);
        return listentity.size() > 0;
    }

    @Override
    public void onDelete(String tableid, Map valueMap, String projectId) {
        TaskGeneratorUtil.setScheduleFinishTime(projectId, tableid, valueMap);
    }
}
