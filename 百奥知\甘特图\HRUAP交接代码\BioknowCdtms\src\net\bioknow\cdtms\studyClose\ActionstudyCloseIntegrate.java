package net.bioknow.cdtms.studyClose;


import com.google.gson.Gson;
import net.bioknow.mvc.RootAction;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.util.StringUtil;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class ActionstudyCloseIntegrate extends RootAction {

    public void showOpen(HttpServletRequest request, HttpServletResponse response) throws IOException {
            try {
                String projectId = SessUtil.getSessInfo().getProjectid();
                String tableid = (String) request.getParameter("tableid");
                String authwhere = DAODataMng.getWhere(request);
                String where= "obj.closed ='1'";
                if (StringUtils.isNotEmpty(authwhere)) {
                    where +=" and ("+authwhere+ ")";
                }
                DAODataMng daoDataMng = new DAODataMng(projectId);
                List<Map<String,Object>> stuydList = daoDataMng.listRecord(tableid, where, null, 10000);
                ArrayList<Object> stuydJsonList = new ArrayList<>();
                for (Map studyMap : stuydList) {
                    String studyid = (String) studyMap.get("studyid");
                    Long id = (Long) studyMap.get("id");
                    HashMap<Object, Object> studyJsonMap = new HashMap<>();
                    studyJsonMap.put("name",studyid);
                    studyJsonMap.put("value",id);
                    stuydJsonList.add(studyJsonMap);

                }
                Gson gson = new Gson();
                String stuydJson = gson.toJson(stuydJsonList);


                request.setAttribute("stuydList",stuydJson);
                request.setAttribute("tableId",tableid);

                this.forward(request, response, "showOpen");
            } catch (Exception e) {
                Log.error("",e);
            }



    }


    public void Open(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {

            String projectId = SessUtil.getSessInfo().getProjectid();
            Long userid = Long.valueOf(SessUtil.getSessInfo().getUserid());
            String selids = request.getParameter("StudyIds");
            String tableId = request.getParameter("tableId");
            DAODataMng daoDataMng=new DAODataMng(projectId);
            int sum = 0;
            ArrayList<Map> StudyList = new ArrayList<>();
            if (StringUtils.isNotEmpty(selids)) {
                String[] selidMap = selids.split(",");
                for (String selid : selidMap) {
                    HashMap<Object, Object> StudySaveMap = new HashMap<>();
                    StudySaveMap.put("id",selid);
                    StudySaveMap.put("closed","0");
                    StudyList.add(StudySaveMap);
                }
                sum=selidMap.length;

            }

            daoDataMng.saveBatch(tableId,StudyList,userid,null);


            response.getOutputStream().write(("已成功恢复"+sum+"个项目").getBytes("UTF-8"));


        } catch (Exception e) {
            Log.error("",e);
        }



    }







}
