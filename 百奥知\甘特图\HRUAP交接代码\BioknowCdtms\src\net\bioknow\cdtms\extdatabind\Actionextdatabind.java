package net.bioknow.cdtms.extdatabind;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.sun.org.apache.xml.internal.security.encryption.EncryptionMethod;
import net.bioknow.cdtms.formMail.DAOTransemail;
import net.bioknow.cdtms.lightpdfSign.HttpStatus;
import net.bioknow.cdtms.lightpdfSign.LightpdfSignIntegrateUtil;
import net.bioknow.cdtms.lightpdfSign.MapDeserializerDoubleAsIntFix;
import net.bioknow.cdtms.lightpdfSign.ResponseUtils;
import net.bioknow.dbplug.emailsend.DAOEmailsendCFG;
import net.bioknow.dbplug.wordreport.UtilAsposeword;
import net.bioknow.mvc.RootAction;
import net.bioknow.mvc.tools.WebPath;
import net.bioknow.passport.datamng.DAOPPData;
import net.bioknow.passport.validate.ValidateUtil;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbcore.schema.Schema;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.schemaplug.attach.AttachDAO;
import net.bioknow.webutil.fileup.DAOFileup;
import net.bioknow.webutil.session.SessInfo;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.session.tokensession.BeanSessUser;
import net.bioknow.webutil.session.tokensession.UtilTokenSession;
import net.bioknow.webutil.timercache.TimerCacheUtil;
import net.bioknow.webutil.tools.FileUtil;
import net.bioknow.webutil.tools.Log;
import net.bioknow.webutil.tools.URLUtil;
import net.bioknow.webutil.tools.UUIDUtil;
import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.util.Zip4jConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static net.bioknow.cdtms.lightpdfSign.PdfHelper.getKeyWords;


public class Actionextdatabind extends RootAction {


    public void checktoken(HttpServletRequest request, HttpServletResponse response) {
        try {


            String token = request.getParameter("token");

            BeanSessUser su = null;
            if (token != null && !token.equals("")) {
                su = (BeanSessUser) TimerCacheUtil.get(token);
            }
            String ret = "";
            if (su == null) {
                ret = "{\"status\":\"404\",\"note\":\"rtoken invalid.\"}";
            } else {
                ret = new Gson().toJson(su);
            }
            response.getOutputStream().write(ret.getBytes("UTF8"));
            response.getOutputStream().close();


        } catch (Exception e) {
            Log.error("", e);
        }

    }


    public static void main(String[] args) throws Exception {

//        String dateString = "2023-12-19T09:16:39.000+00:00";
//
//        // 使用 SimpleDateFormat 解析日期
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
////        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT+0"));
////        TimeZone.set(TimeZone.getTimeZone("GMT")); //设置时区
//
////        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
//
//        Date parsedDate = simpleDateFormat.parse(dateString);
//
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
//        String GMT_Time = dateFormat.format(new Date());
//
//
//        System.out.println(parsedDate);
//        System.out.println(GMT_Time);

//        String where = "1=1";


//        String studycode = "CDTMS手册优化测试项目";
////            String  studycode="CDTMS手册优化测试项目";
//        String where = "obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='" + studycode + "') and obj.status in ('10','30')";
//
//        String url = "https://cdtms-tst.hengrui.com:82/tableapp.ajaxgetfieldvalue.do?tableid=outboard_data_manager&orderby=" + URLUtil.urlEncodeUTF8("obj.id desc") + "&method=all&projectid=edm_uap&fieldid=outboard_data_doc,outboard_data_department,upload_time&where=" + URLUtil.urlEncodeUTF8(where);
//
//
//        System.out.println(url);
//
//
//        String ret = URLUtil.getContent(url, 3000, "utf8", null);
//
//        System.out.println(ret);
//        Gson gson = new Gson();
//
//        ArrayList<Object> objects = new ArrayList<>();
//
//        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
//
//
//        objectObjectHashMap.put("name","111");
//        objectObjectHashMap.put("id","111");
//
//
//        objects.add(objectObjectHashMap);
//
//
//        System.out.println(gson.toJson(objects));




        String zipFile = "D:\\CDTMS手册优化测试项目_hrs8807_plasma_20240108_dft_rev1-zyn-2_coding.csv.zip";
        String md5 = calculateMD5("D:\\CDTMS手册优化测试项目_hrs8807_plasma_20240108_dft_rev1-zyn-2_coding.csv");

        // 1. 压缩文件
        compressFile("D:\\CDTMS手册优化测试项目_hrs8807_plasma_20240108_dft_rev1-zyn-2_coding.csv", zipFile,md5.substring(0, 6));

        // 2. 获取文件的MD5

        // 3. 将MD5设置为压缩包密码
//        setZipPassword(zipFile, md5.substring(0, 6));

        // 4. 将MD5写入新的MD5文件
        String md5File ="D:\\CDTMS手册优化测试项目_hrs8807_plasma_20240108_dft_rev1-zyn-2_coding.csv_password.txt";

        writeMD5ToFile(md5File, md5.substring(0, 6));


    }

    public static String getContent(String urlStr, int timeout, String encode, String cookie) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
        httpConn.setRequestMethod("GET");
        httpConn.setConnectTimeout(timeout);
        httpConn.setReadTimeout(timeout);
        if (cookie != null && !cookie.equals("")) {
            httpConn.setRequestProperty("Cookie", cookie);
        }
        ByteArrayOutputStream os = new ByteArrayOutputStream();

        InputStream is = httpConn.getInputStream();
        byte[] bA = new byte[1024];
        int len = is.read(bA);
        while (len > 0) {
            os.write(bA, 0, len);
            len = is.read(bA);
        }
        os.close();
        is.close();
        String ret = null;
        if (encode != null && encode.equalsIgnoreCase("UTF8")) {
            ret = new String(os.toByteArray(), "UTF-8");
        } else {
            ret = os.toString();
        }
        return ret;
    }

    public void loadBindRecord(HttpServletRequest request, HttpServletResponse response) {
        try {
            String id = request.getParameter("id");
            String zq = request.getParameter("zq");
            Long studyid = Long.valueOf(request.getParameter("studyid"));

            String url = "https://externalblind-tst.hengrui.com/external_data/OperateAndEmailRecords/?id=" + id;


            String projectid = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectid);

//            List targetList = daoDataMng.listRecord("ext_data_bind", "obj.id=" + id, null, 1000);

            String RecordJson = getContent(url, 3000, "UTF8", null);
//            System.out.println(RecordJson);
            Map<String, Object> RecordMap = JSON.parseObject(RecordJson, new TypeReference<Map<String, Object>>() {
            });


//            Map<String,Object>  RecordMap = new Gson().fromJson(RecordJson, Map.class);

            Integer code = (Integer) RecordMap.get("code");
            if (code != 200) {
//                this.redirectByUrl(request, response, "/tableapp.list.do?tableid=ext_data_bind&refinfo=ext_data_id__" + id);

                response.getOutputStream().write("访问数据失败".getBytes("GBK"));
                return;
            }

            List<Map> bindRecordList = (List) RecordMap.get("data");

//            utcFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
            if (CollectionUtils.isEmpty(bindRecordList)) {
                response.getOutputStream().write("无遮盲记录".getBytes("GBK"));
                return;
            }

//            bindRecordList.sort(Comparator.comparing(bindRecordMap -> (String) bindRecordMap.get("create_time")));


//            daoDataMng.ListRecord("ext_data_bind", "obj.ext_data_id=" + id);
            List<Map> extDataBindList = daoDataMng.listRecord("ext_data_bind", "obj.ext_data_id=" + id, null, 1000);



            ArrayList<Map> exDateBindListToSave = new ArrayList<>();
            String[] blindMethodLabels = {"", "二次编码", "变量不传输", "掩码", "二次编码，变量不传输", "二次编码，掩码", "变量不传输，掩码", "二次编码，变量不传输，掩码"};
            SimpleDateFormat utcFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
//            TimeZone.setDefault(TimeZone.getTimeZone("GMT")); //设置时区
//            utcFormat.setTimeZone(TimeZone.getTimeZone("GMT"));


            for (Map bindRecordMap : bindRecordList) {


                String fileId = (String) bindRecordMap.get("file_id");

                boolean exists = extDataBindList.stream()
//                        .filter(extDataBindMap -> extDataBindMap.containsKey(specifiedKey))
                        .anyMatch(extDataBindMap -> StringUtils.equals((String) extDataBindMap.get("file_id"), fileId));

                if (exists) continue;


                Map<Object, Object> exDateBindToSave = new HashMap<>();


                String fileName = null;


                String filePathSource = (String) bindRecordMap.get("file_path");
                fileName = null;

                if (StringUtils.isNotEmpty(filePathSource)) {
                    int lastSlashIndex = filePathSource.lastIndexOf("/");

                    if (lastSlashIndex != -1) {
                        fileName = filePathSource.substring(lastSlashIndex + 1);
                    } else {
                        fileName = filePathSource;
                    }
                }

                if (StringUtils.isNotEmpty(fileId)) {


                    String outputUrl = "https://externalblind-tst.hengrui.com/external_data/getBlindedFile/?fileId=" + fileId;


                    String filePath = WebPath.getRootPath() + DAOFileup.tempfolder + "/externalBlind";

                    LightpdfSignIntegrateUtil.downloadByNIO(outputUrl, filePath, fileName);
                    AttachDAO attachDAO = new AttachDAO(projectid);

                    String fileUuid = attachDAO.saveFile(new File(filePath + "/" + fileName), "ext_data_bind");


                    String strAttachToSave = "" + fileName.toString().split("\\*")[0] + "*" + fileUuid + "|";
                    exDateBindToSave.put("bind_file", strAttachToSave);
                }


                exDateBindListToSave.add(exDateBindToSave);
                exDateBindToSave.put("file_id", fileId);
                exDateBindToSave.put("bind_file_name", fileName);
                String getVenderLink = "https://externalblind-tst.hengrui.com/external_data/getMemberInfoByFileId/?fileId=" + fileId;

                String VenderRecordJson = getContent(getVenderLink, 3000, "UTF8", null);
//            System.out.println(RecordJson);
                Map<String, Object> VenderRecordMap = JSON.parseObject(VenderRecordJson, new TypeReference<Map<String, Object>>() {
                });

                Integer VenderCode = (Integer) VenderRecordMap.get("code");
                if (VenderCode == 200) {
//                this.redirectByUrl(request, response, "/tableapp.list.do?tableid=ext_data_bind&refinfo=ext_data_id__" + id);
                    exDateBindToSave.put("file_receive_emails", VenderRecordMap.get("data"));

                }


                String createTimeStr = (String) bindRecordMap.get("create_time");
                exDateBindToSave.put("ext_data_id", Long.valueOf(id));
                exDateBindToSave.put("bind_date", utcFormat.parse(createTimeStr));
                exDateBindToSave.put("study_id", studyid);


                String type = (String) bindRecordMap.get("type");
                String blind_status = String.valueOf(bindRecordMap.get("blind_status"));


//                if (StringUtils.isNotEmpty(blind_status)) {
//
//                    exDateBindToSave.put("blind_status",
//
//                            StringUtils.equals("-1", blind_status) ? "草稿版" : (StringUtils.equals("1", blind_status) ? "定稿版":"审核版")
//
//
//                    );
//
//
//                }

                exDateBindToSave.put("blind_status", "0");

                String is_approve = (String) bindRecordMap.get("is_approve");
                String qcDateStr = (String) bindRecordMap.get("qc_time");
                String qc_user_id = (String) bindRecordMap.get("qc_user_id");

                String receive_emails = (String) bindRecordMap.get("receive_emails");
                String operate_name = (String) bindRecordMap.get("operate_name");
                String sendDateStr = (String) bindRecordMap.get("send_time");

                exDateBindToSave.put("file_send_date", ObjectUtils.isNotEmpty(sendDateStr) ? utcFormat.parse(sendDateStr) : null);
//                exDateBindToSave.put("file_receive_emails", receive_emails);
                exDateBindToSave.put("operate_name", operate_name);
                exDateBindToSave.put("qc_comments", bindRecordMap.get("qc_comments"));
                exDateBindToSave.put("preview_url", bindRecordMap.get("preview_url"));


                exDateBindToSave.put("qc_date", ObjectUtils.isNotEmpty(qcDateStr) ? utcFormat.parse(qcDateStr) : null);

                exDateBindToSave.put("qc_user_mail", qc_user_id);
//                exDateBindToSave.put("is_approve", is_approve);
                exDateBindToSave.put("role_name", bindRecordMap.get("role"));
                exDateBindToSave.put("type", type);
                exDateBindToSave.put("user_name", bindRecordMap.get("user_id"));

                String blind_records = (String) bindRecordMap.get("blind_records");
//                    String blind_records = "{\"fileName\":\"hrs8807_i_101_hrs8807_plasma_20231201_dft_rev1.csv\",\"dataType\":\"PKPD\",\"role\":\"EDM\",\"del_si\":\"0\",\"sensitiveForm\":[{\"delvar\":\"\",\"delop\":\"\",\"delval\":[]}],\"blind_method\":\"1\",\"rule\":\"2\",\"rand\":\"\",\"corresponding\":\"\",\"randvalues\":[],\"clearvar_s\":[],\"blindCodeForm\":[{\"maskval\":[],\"maskop\":\"\",\"maskvar\":\"\"}],\"mask_range\":\"\",\"mask_method\":\"\",\"maskvar_s\":[],\"doubleCodeForm\":[{\"value\":\"PCSPEC\",\"replvalue\":\"PCTEST\",\"replvar\":\"\",\"replace_var_type\":\"0\",\"is_range\":\"\",\"upper\":\"\",\"lower\":\"\"},{\"value\":\"PCLLOQ\",\"replvalue\":\"\",\"replvar\":\"1234\",\"replace_var_type\":\"0\",\"is_range\":\"\",\"upper\":\"\",\"lower\":\"\"}]}";
                Map<String, Object> blindRecordsMap = JSON.parseObject(blind_records, new TypeReference<Map<String, Object>>() {
                });
                String blindRecordsStr = "";
                List<Map> sensitiveFormList = (List) blindRecordsMap.get("sensitiveForm");


                String sensitiveFormStr = "";
                int sensitiveFormCount = 1;

                String yn_blindcode = (String) blindRecordsMap.get("yn_blindcode");

                if (StringUtils.isNotEmpty(yn_blindcode)) {
                    blindRecordsStr += "保持随机替换规则：" + (StringUtils.equals(yn_blindcode, "1") ? "是" : "否") + "\r\n";

                }


                if (!CollectionUtils.isEmpty(sensitiveFormList)) {
                    for (Map sensitiveFormMap : sensitiveFormList) {

                        String delvar = (String) sensitiveFormMap.get("delvar");
                        if (StringUtils.isEmpty(delvar)) {
                            continue;
                        }

                        String delop = (String) sensitiveFormMap.get("delop");
//                            String[] delval = (String[]) sensitiveFormMap.get("delval");

                        JSONArray delval = (JSONArray) sensitiveFormMap.get("delval");
                        sensitiveFormStr += "敏感信息删除" + sensitiveFormCount + ":" + delvar + delop + delval.toString() + "\r\n";
                        sensitiveFormCount++;
                    }
                }
                String del_si = (String) blindRecordsMap.get("del_si");

                if (StringUtils.isNotEmpty(del_si)) {
                    blindRecordsStr += (StringUtils.equals(del_si, "1") ? sensitiveFormStr : "敏感信息删除：否" + "\r\n");

                }
                String blind_method = (String) blindRecordsMap.get("blind_method");


                if (StringUtils.isNotEmpty(blind_method)) {
                    blindRecordsStr += "遮盲方法：" + blindMethodLabels[Integer.parseInt(blind_method)] + "\r\n";
                    ;
                }

                String rand = (String) blindRecordsMap.get("rand");

                if (StringUtils.isNotEmpty(rand)) {
                    blindRecordsStr += "是否剂量组内随机：" + (StringUtils.equals(rand, "1") ? "是" : "否") + "\r\n";
                }


                String corresponding = (String) blindRecordsMap.get("corresponding");

                if (StringUtils.isNotEmpty(corresponding)) {
                    blindRecordsStr += "是否保持对应关系：" + (StringUtils.equals(corresponding, "1") ? "是" : "否") + "\r\n";
                }

//                    String[] Randvalues = (String[]) blindRecordsMap.get("Randvalues");
                JSONArray Randvalues = (JSONArray) blindRecordsMap.get("randvalues");

                if (ObjectUtils.isNotEmpty(Randvalues)) {
                    blindRecordsStr += "随机已选择的变量：" + Randvalues.toString() + "\r\n";

                }

                List<Map> doubleCodeFormList = (List) blindRecordsMap.get("doubleCodeForm");


                if (!CollectionUtils.isEmpty(doubleCodeFormList)) {

//                        String[] doubleCodeFormArr = new String[doubleCodeFormList.size()];
                    ArrayList<String> doubleCodeFormArr = new ArrayList<>();

                    int doubleCodeFormCount = 1;

                    for (Map doubleCodeForm : doubleCodeFormList) {

                        String value = (String) doubleCodeForm.get("value");
                        if (StringUtils.isEmpty(value)) {
                            continue;
                        }

                        String replvalue = (String) doubleCodeForm.get("replvalue");
                        String replvar = (String) doubleCodeForm.get("replvar");
                        String replace_var_type = (String) doubleCodeForm.get("replace_var_type");
                        String is_range = (String) doubleCodeForm.get("is_range");
                        String upper = (String) doubleCodeForm.get("upper");
                        String lower = (String) doubleCodeForm.get("lower");


//                            String[] delval = (String[]) sensitiveFormMap.get("delval");

//                            JSONArray delval = (JSONArray) doubleCodeForm.get("replvar");

                        String doubleCodeFormStr = "非真实值替换" + doubleCodeFormCount + "：";
                        doubleCodeFormCount++;

                        if (StringUtils.isNotEmpty(replace_var_type)) {

                            doubleCodeFormStr += (StringUtils.equals(replace_var_type, "1") ? "数值型" : "非数值型") + ",";
                        }
                        doubleCodeFormStr += "变量1:" + value + ",";


                        if (!StringUtils.equals(replace_var_type, "1") && (StringUtils.equals(value, "SUBJID") || StringUtils.equals(value, "RANDNUM"))) {


                            doubleCodeFormStr = doubleCodeFormStr.substring(0, doubleCodeFormStr.length() - 1);

                            doubleCodeFormArr.add(doubleCodeFormStr);
                            continue;

                        }

                        if (StringUtils.isNotEmpty(replvalue)) {
                            doubleCodeFormStr += "变量2:" + replvalue + ",";
                        }

                        if (StringUtils.equals(replace_var_type, "0")) {
                            doubleCodeFormStr += "替换为:" + (StringUtils.isNotEmpty(replvar) ? replvar : "空值") + ",";
                        }


                        if (StringUtils.isNotEmpty(is_range)) {
                            if (StringUtils.equals(is_range, "1")) {
                                doubleCodeFormStr += "范围内生成:" + lower + "~" + upper + ",";
                            }
                        }


                        doubleCodeFormStr = doubleCodeFormStr.substring(0, doubleCodeFormStr.length() - 1);

                        doubleCodeFormArr.add(doubleCodeFormStr);
//                            doubleCodeFormArr[doubleCodeFormCount]=value;
//                            doubleCodeFormCount++;

                    }
                    if (doubleCodeFormArr.size() > 0) {

                        blindRecordsStr += String.join("\r\n", doubleCodeFormArr) + "\r\n";
                        ;

                    }

                }


                JSONArray clearvar_s = (JSONArray) blindRecordsMap.get("clearvar_s");

//                    String clearvar_s = clearvar_sArr.toString();
                if (CollectionUtils.isNotEmpty(clearvar_s)) {

                    blindRecordsStr += "变量不传输-已选择变量：" + clearvar_s.toString() + "\r\n";
                    ;
                }

                String mask_range = (String) blindRecordsMap.get("mask_range");

                if (StringUtils.isNotEmpty(mask_range)) {
                    blindRecordsStr += "掩码范围：" + (StringUtils.equals(mask_range, "1") ? "全部" : "非全部") + "\r\n";
                    ;
                }


                String mask_method = (String) blindRecordsMap.get("mask_method");

                if (StringUtils.isNotEmpty(mask_method)) {
                    blindRecordsStr += "掩码方式：" + (StringUtils.equals(mask_method, "1") ? "盲态" : "9999") + "\r\n";
                    ;
                }


                List<Map> blindCodeFormList = (List) blindRecordsMap.get("blindCodeForm");


                String blindCodeFormStr = "";
                int blindCodeFormCount = 0;
                if (!CollectionUtils.isEmpty(blindCodeFormList)) {
                    for (Map blindCodeFormMap : blindCodeFormList) {

                        String maskvar = (String) blindCodeFormMap.get("maskvar");
                        if (StringUtils.isEmpty(maskvar)) {
                            continue;
                        }


                        String maskop = (String) blindCodeFormMap.get("maskop");
//                            String[] delval = (String[]) sensitiveFormMap.get("delval");

                        JSONArray maskval = (JSONArray) blindCodeFormMap.get("maskval");

                        blindCodeFormStr += "掩码-前置变量" + (blindCodeFormCount + 1) + "：" + maskvar + maskop + maskval.toString() + "\r\n";

                        blindCodeFormCount++;

                    }
                }

                if (blindCodeFormCount >= 1) {

                    blindRecordsStr += blindCodeFormStr;


                }

                JSONArray maskvar_s = (JSONArray) blindRecordsMap.get("maskvar_s");

                if (CollectionUtils.isNotEmpty(maskvar_s)) {

                    blindRecordsStr += "掩码-已选择变量：" + maskvar_s.toString() + "\r\n";
                }


//                    System.out.println(blindRecordsStr);
                exDateBindToSave.put("blind_info", blindRecordsStr);


            }


            daoDataMng.saveBatch("ext_data_bind", exDateBindListToSave, 2L, null);

                this.redirectByUrl(request, response, "/tableapp.list.do?tableid=ext_data_bind&refinfo=ext_data_id__" + id);


            return;

        } catch (Exception e) {
            Log.error("", e);
        }
    }


    public void ajaxmenu(HttpServletRequest request, HttpServletResponse response) {
        try {
            String id = request.getParameter("id");
            String tableid = request.getParameter("tableid");
            String refinfo = request.getParameter("refinfo");
            Gson gson = new Gson();

            String projectId = SessUtil.getSessInfo().getProjectid();

            String loginid = SessUtil.getSessInfo().getUserloginid();

            DAODataMng daoDataMng = new DAODataMng(projectId);

            Map exdataMap = daoDataMng.getRecord(tableid, Long.valueOf(id));
            DAODbApi dadao = new DAODbApi(projectId);

            Schema studyidSchema = dadao.getFieldType(tableid, "studyid");
            Map studyidField = dadao.getMapFieldCopy(tableid, "studyid");
            String studyCode = studyidSchema.formatToOutput(tableid, studyidField, exdataMap);

            String type = (String) exdataMap.get("outboard_data_category");
            String filename = (String) exdataMap.get("filename");
            Long studyid = (Long) exdataMap.get("studyid");
            Long phase = (Long) exdataMap.get("zq");
            String venderName = (String) exdataMap.get("outboard_data_department");
            Map userMap = SessUtil.getSessInfo().getUser();

            List StudyUserList = daoDataMng.listRecord("roles", "obj.studyid=" + studyid + " and obj.member=" + (String) userMap.get("id"), null, 1);
            String role = "";
            if (!CollectionUtils.isEmpty(StudyUserList)) {
                Map StudyUserMap = (Map) StudyUserList.get(0);

                role = (String) StudyUserMap.get("limitnum");

            }

            List<Map> DTAList = daoDataMng.listRecord("ext_data", "obj.studyid=" + studyid + " and obj.lab_org='" + venderName + "' and obj.ext_data_type='" + type + "' and obj.crf_zt='10' and obj.crf_zt='10' and obj.ext_version like '%.0'", "obj.ext_date desc nulls last", 1);
            Log.info(gson.toJson(DTAList));
            List<Map> DTABMList = null;

            List<Map> DTABMList2 = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(DTAList)) {
                Map DTAMap = DTAList.get(0);

                Long DTAId = (Long) DTAMap.get("id");

                DTABMList = daoDataMng.listRecord("dta_bind_method", "obj.dta_id=" + DTAId, null, 100);
                Log.info(gson.toJson(DTABMList));

                for (Map DTABMMap : DTABMList) {
                    Schema methodSchema = dadao.getFieldType("dta_bind_method", "method");
                    Map methodFiledMap = dadao.getMapFieldCopy("dta_bind_method", "method");

                    Map<Object, Object> DTABMMap2 = new HashMap<>();

                    DTABMMap2.put("method",methodSchema.formatToOutput("dta_bind_method", methodFiledMap, DTABMMap));
                    DTABMMap2.put("role",DTABMMap.get("role"));

                    DTABMList2.add(DTABMMap2);


                }


            }


            Long EDBId = null;



            if(phase == 6) {
                List<Map> EDBList = daoDataMng.listRecord("ext_data_bind", "obj.is_team_approve='" + 1 + "' and obj.ext_data_id=(select obj2.id from Otboard_data_manager as obj2 where obj2.zq=5 and obj2.studyid="+studyid+" and obj2.outboard_data_department="+venderName+" and obj2.outboard_data_category="+type+")", "obj.id desc", 1);


                if (CollectionUtils.isNotEmpty(EDBList)) {
                    Map EDBMap = EDBList.get(0);

                    EDBId = (Long) EDBMap.get("id");


                }
            }


            HashMap<String, String> BlindPrmMap = new HashMap<>();

            BlindPrmMap.put("projectName", studyCode);
            BlindPrmMap.put("fileName", filename);
            BlindPrmMap.put("role", role);
            BlindPrmMap.put("account", loginid);
            BlindPrmMap.put("dataType", type);
            BlindPrmMap.put("id", id);
            BlindPrmMap.put("type", phase == 6 ? "p" : "t");
            BlindPrmMap.put("venderName", venderName);
            BlindPrmMap.put("testFileID", String.valueOf(EDBId));
            BlindPrmMap.put("DTABMList",gson.toJson(DTABMList2));


            String token = UtilTokenSession.initToken(projectId, loginid, UUIDUtil.get(), BlindPrmMap);

            request.setAttribute("url", "https://externalblind-tst.hengrui.com/?token=" + URLUtil.urlEncodeUTF8(token));
            this.forward(request, response, "ajaxmenu");
//
//            this.redirectByUrl(request,response,"https://externalblind-tst.hengrui.com/?projectName="+studyCode+"&fileName="+filename+"&role="+role+"&account="+loginid+"&dataType="+type+"token="+URLUtil.urlEncodeUTF8(token));
//			this.forward();
//			this.redirectByUrl(request,response,"https://externalblind-tst.hengrui.com/?token="+URLUtil.urlEncodeUTF8(token));


        } catch (Exception e) {
            Log.error("", e);
        }

    }

    public void sendMail(HttpServletRequest request, HttpServletResponse response) {
        try {

            String tableid = request.getParameter("tableid");

            String signersJson = request.getParameter("receiver");
//            GsonBuilder gsonBuilder = new GsonBuilder();
//            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
//            }.getType(), new MapDeserializerDoubleAsIntFix());
//            Gson gson = gsonBuilder.create();
//
//
//
//            List<Map<String, String>> signersList = gson.fromJson(signersJson, new TypeToken<List<Map<String, String>>>() {
//            }.getType());

            String id = request.getParameter("id");

            String projectId = SessUtil.getSessInfo().getProjectid();

            String loginid = SessUtil.getSessInfo().getUserloginid();
            DAOTransemail daoTransemail = new DAOTransemail(projectId);


            String fileSelected = request.getParameter("fileSelected");
            AttachDAO attachDAO = new AttachDAO(projectId);

            File[] attachFiles = null;
            String[] attachFileNames = null;
            if (StringUtils.isNotEmpty(fileSelected)) {
                String[] fileSelectedArr = fileSelected.split(",");
                attachFiles = new File[fileSelectedArr.length];
                attachFileNames = new String[fileSelectedArr.length];
                if (!org.apache.commons.lang3.ArrayUtils.isEmpty(fileSelectedArr)) {
                    for (int i = 0; i < fileSelectedArr.length; ++i) {
                        attachFiles[i] = attachDAO.getFile(fileSelectedArr[i].split("\\|\\|")[0],tableid);
                        attachFileNames[i] = fileSelectedArr[i].split("\\|\\|")[1];
                    }
                }
            }



            String filePath = WebPath.getRootPath() + DAOFileup.tempfolder + "/externalBlind";

            String inputFile = filePath + "/" + attachFileNames[0];
            String zipFile = filePath + "/" + attachFileNames[0]+".zip";
            String md5 = calculateMD5(attachFiles[0].getAbsolutePath());

            // 1. 压缩文件
            compressFile(attachFiles[0].getAbsolutePath(), zipFile,md5.substring(0, 6));

            // 2. 获取文件的MD5

            // 3. 将MD5设置为压缩包密码
//            setZipPassword(zipFile, md5.substring(0, 6));

            // 4. 将MD5写入新的MD5文件
            String md5File = filePath + "/" + attachFileNames[0]+".txt";

            writeMD5ToFile(md5File, md5.substring(0, 6));
            File fileLocal = new File(zipFile);
            File fileMD5Local = new File(md5File);
            File[] attachFiles2={fileLocal};
            String[] attachFileNames2={attachFileNames[0]+".zip"};
            File[] attachFiles3={fileMD5Local};
            String[] attachFileNames3={attachFileNames[0]+"_password.txt"};

            for (String signer : signersJson.split(",")) {


//                String name = String.valueOf(signerMap.get("name")).split("\\(")[0];


//                HashMap<Object, Object> coordinateMap = new HashMap<>();
                Map signerInfosMap = new HashMap<>();
                String email = signer;

                    String emailSender = request.getParameter("email_from");
                    String subject = request.getParameter("subject");
//                    File[] attachFile =request.getParameter("attachFile");
//                    String attachFileName = request.getParameter("attachFileName");
                    String currEmail = request.getParameter("currEmail");
                    String content = request.getParameter("content");


                    String sendMailResult = daoTransemail.sendmail(emailSender,subject, content, email, null, currEmail, attachFiles2, attachFileNames2);

                String sendMailResult2 = daoTransemail.sendmail(emailSender,"这是密码", "这是密码", email, null, currEmail, attachFiles3, attachFileNames3);


            }

            ResponseUtils.response(response, HttpStatus.OK);


        } catch (Exception e) {
            Log.error("", e);
        }

    }



    public void qcajaxmenu(HttpServletRequest request, HttpServletResponse response) {
        try {
            String id = request.getParameter("id");
            String type = request.getParameter("type");
//            String tableid = request.getParameter("tableid");
//            String refinfo = request.getParameter("refinfo");

            request.setAttribute("id",id);
            request.setAttribute("type",type);
//
            this.forward(request, response, "qcajaxmenu");
//
//            this.redirectByUrl(request,response,"https://externalblind-tst.hengrui.com/?projectName="+studyCode+"&fileName="+filename+"&role="+role+"&account="+loginid+"&dataType="+type+"token="+URLUtil.urlEncodeUTF8(token));
//			this.forward();
//			this.redirectByUrl(request,response,"https://externalblind-tst.hengrui.com/?token="+URLUtil.urlEncodeUTF8(token));


        } catch (Exception e) {
            Log.error("", e);
        }

    }


    public void ajaxgetfilename(HttpServletRequest request, HttpServletResponse response) {
        try {


//            request.getParameter("CDTMS手册优化测试项目")


            String studycode = request.getParameter("studycode");
//            String  studycode="CDTMS手册优化测试项目";
            String where = "obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='" + studycode + "') and obj.status in ('10','30')";

            String url = "https://cdtms-tst.hengrui.com:82/tableapp.ajaxgetfieldvalue.do?tableid=outboard_data_manager&orderby=" + URLUtil.urlEncodeUTF8("obj.id desc") + "&method=all&projectid=edm_uap&fieldid=outboard_data_doc,outboard_data_department,upload_time&where=" + URLUtil.urlEncodeUTF8(where);


            System.out.println(url);


            String ret = httpGet(url);

            String[] retArr = ret.split("\\,\\|\\,");

            Map<Object, Object> optionMap = new HashMap<>();
            for (int i = 0; i < retArr.length; i++) {

                if(i==20)break;

                String[] ret2 = retArr[i].split(",;,");
                optionMap.put(ret2[0],ret2[1]+",;,"+ret2[2]);

            }



//            if (retArr.length > 20) {
//                retArr = Arrays.copyOfRange(retArr, 0, 20);
//            }


            Gson gson = new Gson();

            String retJson = gson.toJson(optionMap);


            System.out.println(retJson);

            response.getOutputStream().write(retJson.getBytes("UTF-8"));

            response.getOutputStream().close();


        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }


    public void saveQc(HttpServletRequest request, HttpServletResponse response) {
        try {


//            request.getParameter("CDTMS手册优化测试项目")


            String id = request.getParameter("id");
            String result = request.getParameter("result");
            String reason = request.getParameter("reason");
            String type = request.getParameter("type");
            String mail = request.getParameter("mail");

            String projectid = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectid);

            Map edbMap = daoDataMng.getRecord("ext_data_bind", Long.valueOf(id));


            if (MapUtils.isEmpty(edbMap)) {
                response.getOutputStream().write("记录不存在".getBytes("UTF-8"));

            }

            String strAttachToSave="";

            if (StringUtils.isNotEmpty(mail)) {
                DAOFileup fdao = new DAOFileup();

                List<File> mailList = fdao.parseToFile(mail);
                List<String> mailFileNameList = fdao.parseToFileName(mail);
                AttachDAO attachDAO = new AttachDAO(projectid);
                for (int i = 0; i < mailFileNameList.size(); i++) {
                    File file = mailList.get(i);
                    String filename = mailFileNameList.get(i);
                    String fileLocalUuid = attachDAO.saveFile(file, "ext_data_bind");
                    strAttachToSave += "" + filename + "*" + fileLocalUuid + "|";
                }
            }





            HashMap<Object, Object> edbToSave = new HashMap<>();
            Map currUserMap = SessUtil.getSessInfo().getUser();
            edbToSave.put("id", edbMap.get("id"));

            if (StringUtils.equals(type,"2")) {

                edbToSave.put("is_team_approve", result);
                edbToSave.put("team_qc_mail_file", strAttachToSave);

                if (StringUtils.equals(result, "1")) {
                    edbToSave.put("blind_status", "2");

                }

                edbToSave.put("team_qc_comments", reason);


            }else {
                edbToSave.put("is_approve", result);

                if (StringUtils.equals(result, "1")) {
                    edbToSave.put("blind_status", "2");

                }
                edbToSave.put("qc_date", new Date());
                edbToSave.put("qc_comments", reason);
                edbToSave.put("qc_user_mail", currUserMap.get("loginid"));
            }
            daoDataMng.saveRecord("ext_data_bind",edbToSave);


            response.getOutputStream().write("200".getBytes("UTF-8"));



        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    public static String httpGet(String url) {
        // 获取连接客户端工具
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse httpResponse = null;
        String finalString = null;
        HttpGet httpGet = new HttpGet(url);
        /**公共参数添加至httpGet*/
        /**header中通用属性*/
        httpGet.setHeader("Accept", "*/*");
        httpGet.setHeader("Accept-Encoding", "gzip, deflate");
        httpGet.setHeader("Cache-Control", "no-cache");
        httpGet.setHeader("Connection", "keep-alive");
        httpGet.setHeader("Content-Type", "application/json;charset=UTF-8");
        /**业务参数*/

        try {
            httpResponse = httpClient.execute(httpGet);
            HttpEntity entity = httpResponse.getEntity();
            finalString = EntityUtils.toString(entity, "UTF-8");
            try {
                httpResponse.close();
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return finalString;
    }
    public static ZipParameters setParam(String password){
        //设置压缩文件参数
        ZipParameters parameters = new ZipParameters();
        //设置压缩方法
        parameters.setCompressionMethod(Zip4jConstants.COMP_DEFLATE);
        //设置压缩级别
        parameters.setCompressionLevel(Zip4jConstants.DEFLATE_LEVEL_NORMAL);
        //设置压缩文件是否加密
        parameters.setEncryptFiles(true);
        //设置aes加密强度
        parameters.setAesKeyStrength(Zip4jConstants.AES_STRENGTH_256);
        //设置加密方法
        parameters.setEncryptionMethod(Zip4jConstants.ENC_METHOD_AES);
        //设置密码
        parameters.setPassword(password.toCharArray());
        return parameters;
    }

    private static void compressFile(String sourceFilePath, String destinationFilePath,String password) throws IOException {
        // 1. 定义源文件路径、目标ZIP文件路径和密码
//        String sourceFilePath = "path/to/source/file"; // 要加密的源文件路径
//        String destinationFilePath = "path/to/encrypted/file.zip"; // 加密后的ZIP文件保存路径
//        String password = "myPassword"; // 用于加密ZIP文件的密码

        try {
            ZipParameters parameters = setParam(password);
            //压缩文件,并生成压缩文件
            ArrayList<File> filesToAdd = new ArrayList<File>();
            File file = new File(sourceFilePath);
            filesToAdd.add(file);

            ZipFile zipFile = new ZipFile(destinationFilePath);
            zipFile.addFiles(filesToAdd, parameters);//this line does works
            System.err.println("end");
        } catch (ZipException e) {
            e.printStackTrace();
        }

    }

    private static String calculateMD5(String inputFile) throws IOException, NoSuchAlgorithmException {
        try (InputStream is = Files.newInputStream(Paths.get(inputFile))) {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[8192];
            int read;
            while ((read = is.read(buffer)) > 0) {
                md.update(buffer, 0, read);
            }
            byte[] md5Bytes = md.digest();
            StringBuilder md5 = new StringBuilder();
            for (byte md5Byte : md5Bytes) {
                md5.append(Integer.toString((md5Byte & 0xff) + 0x100, 16).substring(1));
            }
            return md5.toString();
        }
    }

    private static void setZipPassword(String zipFile, String password) throws IOException {
        try (RandomAccessFile raf = new RandomAccessFile(zipFile, "rw")) {
            raf.seek(6); // Move to the position where the CRC-32 and file size fields are located
            raf.writeShort(0); // Clear the CRC-32 field
            raf.writeShort(0); // Clear the file size field

            // Set password as the CRC-32 value
            raf.writeBytes(password);
        }
    }

    private static void writeMD5ToFile(String md5File, String md5) throws IOException {
        try (PrintWriter writer = new PrintWriter(md5File)) {
            writer.println(md5);
        }
    }


}