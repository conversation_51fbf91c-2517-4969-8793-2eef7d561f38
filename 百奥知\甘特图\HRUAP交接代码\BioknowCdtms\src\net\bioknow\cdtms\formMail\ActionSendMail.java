//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package net.bioknow.cdtms.formMail;


import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


import net.bioknow.mvc.RootAction;
import net.bioknow.passport.webvar.ServerType;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbcore.schema.CNT_Schema;
import net.bioknow.uap.dbcore.schema.Schema;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.schemaplug.attach.AttachDAO;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.uapplug.dbview.DAODbview;
import net.bioknow.uapplug.dbview.DbviewUtil;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import net.bioknow.webutil.tools.UUIDUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;


import static org.apache.commons.lang3.StringUtils.contains;
import static org.apache.commons.lang3.StringUtils.substringBefore;

public class ActionSendMail extends RootAction {


    public void getemail(HttpServletRequest request, HttpServletResponse response) {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            String tableid;
            Map templateMap;
            String id = request.getParameter("id");
            String recordid = request.getParameter("recordid");
            String receiver = request.getParameter("receiver");
            String ccer = request.getParameter("ccer");
//            HttpServletRequest newRequest = (HttpServletRequest) request.getAttribute("javax.servlet.forward.request_uri");

            String isSign = request.getParameter("isSign");

            if (StringUtils.isEmpty(isSign)) {
                isSign= (String) request.getAttribute("isSign");
            }



            if (StringUtils.isNotEmpty(id)) {
                templateMap=daoDataMng.getRecord("email_template", Long.valueOf(id));
                tableid= (String) templateMap.get("tableid");

            }else {

                tableid= request.getParameter("tableid");
                String where = request.getParameter("where");
                String whereE = "obj.tableid='" + tableid + "'";
                if (where != null && !"".equals(where)) {
                    whereE = whereE + " and (" + where + ")";
                }


                List<Map> templateList = daoDataMng.listRecord("email_template", whereE, "", 100);

                if (CollectionUtils.isEmpty(templateList)) {
                    response.getOutputStream().write("配置错误".getBytes("GBK"));
                    response.getOutputStream().close();
                }
                templateMap = (Map) templateList.get(0);
            }



            DtrefDAO dtrefDAO = new DtrefDAO(projectId);
            String RefField = dtrefDAO.getRefField("xsht", tableid);
            String email_template_content = (String)templateMap.get("content");
            String reply = (String)templateMap.get("reply");
            String email_template_subject = (String)templateMap.get("subject");
            String email_from = (String)templateMap.get("from");
            String email_template_receiver_role = "," + (String)templateMap.get("receiver_role") + ",";
            String email_template_cc_role = "," + (String)templateMap.get("cc_role") + ",";
            String files = (String)templateMap.get("files");

            email_template_content = email_template_content.replaceAll("----CLOBHTMLSPLITOR----CLOBHTMLSPLITOR----", "");
            Map valueMap = daoDataMng.getRecord(tableid, Long.parseLong(recordid));

            Long studyid = (Long) valueMap.get(RefField);
            String content = replaceTemplate(email_template_content, projectId, tableid, "", valueMap, "2", "zh", "0");
            String subject = replaceTemplate(email_template_subject, projectId, tableid, "", valueMap, "2", "zh", "0");
            List<String> listFN = new ArrayList();
            String fileCheckbox = "";

            if (StringUtils.isNotEmpty(files)) {
                File[] fA = getFilesFromTemplate(files, projectId, tableid, valueMap, listFN, "0");
                if (fA != null && fA.length > 0) {
                    for(int i = 0; i < fA.length; ++i) {
                        String fileName = (String)listFN.get(i);
                        String fileUri = String.valueOf(fA[i]);
                        String fileKey = fileUri.substring(fileUri.lastIndexOf("\\") + 1);
                        fileCheckbox +=  "<input type=\"checkbox\" name=\"files\"  value=\""+fileKey+"||"+fileName.replace(",","$")+"\" checked>" + fileName+"<br>";

                    }
                }
            }


            String whereC = "studyid='" + studyid + "' or studyid is null";
            DAODbview dbview = new DAODbview(projectId);
            int index = dbview.getRuleIndexByName("项目参与人");
            List listC = DbviewUtil.getDataList(projectId, index + "", whereC, "", 999, 1);
            String rec = "";
            String cc = "";
            if (listC != null && listC.size() > 0) {
                for(int i = 0; i < listC.size(); ++i) {
                    Map map = (Map)listC.get(i);
                    String name = (String)map.get("name");
                    String email = (String)map.get("email");
                    String role = (String)map.get("role");
                    String recselected = (String)map.get("recselected");
                    String ccselected = (String)map.get("ccselected");
                    rec = rec + "{name:'" + name + "',value:'" + email + "'";

                    if ("1".equals(recselected) || email_template_receiver_role.contains("," + role + ",")||contains(","+receiver+",",","+email+",") ) {

                        rec = rec + ",selected: true";
                    }

                    rec = rec + "},";
                    cc = cc + "{name:'" + name + "',value:'" + email + "'";
                    if ("1".equals(ccselected) || email_template_cc_role.contains(role)||contains(","+ccer+",",","+email+",")) {
                        cc = cc + ",selected: true";
                    }

                    cc = cc + "},";
                }
            }

            request.setAttribute("tableid", tableid);
            request.setAttribute("recordid", recordid);
            request.setAttribute("content", content);
            request.setAttribute("subject", subject);
            request.setAttribute("reply", reply);
            request.setAttribute("email_from", email_from);
            request.setAttribute("fileCheckbox", fileCheckbox);
            request.setAttribute("studyid", studyid);
            request.setAttribute("listRec", rec);
            request.setAttribute("listCc", cc);
            request.setAttribute("isSign", isSign);


            request.setAttribute("email_template_content", email_template_content);
            this.forward(request, response, "email");
        } catch (Exception var40) {
            Log.error(var40.getMessage(), var40);
        }

    }

    public void ajaxmenu(HttpServletRequest request, HttpServletResponse response){
        request.setAttribute("uuid", UUIDUtil.get());
        this.forward(request,response,"ajaxmenu");
    }

    public void sendMail(HttpServletRequest request, HttpServletResponse response) {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng dmdao = new DAODataMng(projectId);
            String receiver = request.getParameter("receiver");
            String tableid = request.getParameter("tableid");
            String reply = request.getParameter("reply");
            String emailSender = request.getParameter("email_from");
            String recordid = request.getParameter("recordid");
            String cc = request.getParameter("cc");
            String subject = request.getParameter("subject");
            String content = request.getParameter("content");
            String fileSelected = request.getParameter("fileSelected");
            String studyid = request.getParameter("studyid");


            File[] attachFiles = null;
            String[] attachFileNames = null;
            if (StringUtils.isNotEmpty(fileSelected)) {
                AttachDAO attachDAO = new AttachDAO(projectId);
                String[] fileSelectedArr = fileSelected.split(",");
                attachFiles = new File[fileSelectedArr.length];
                attachFileNames = new String[fileSelectedArr.length];
                if (!ArrayUtils.isEmpty(fileSelectedArr)) {
                    for (int i = 0; i < fileSelectedArr.length; ++i) {
                        attachFiles[i] = attachDAO.getFile(fileSelectedArr[i].split("\\|\\|")[0],tableid);
                        attachFileNames[i] = fileSelectedArr[i].split("\\|\\|")[1].replace("$",",");
                    }
                }
            }


            Map sendLogToSave = new HashMap();
            sendLogToSave.put("tableid", tableid);
            sendLogToSave.put("recordid", Long.parseLong(recordid));
            sendLogToSave.put("receiver", receiver);
            sendLogToSave.put("subject", subject);
            sendLogToSave.put("file_name", ArrayUtils.isNotEmpty(attachFileNames)?Arrays.toString(attachFileNames):"");
            sendLogToSave.put("cc", cc);
            sendLogToSave.put("content", content);
            sendLogToSave.put("study_id", Long.parseLong(studyid));
            DAOTransemail daoTransemail = new DAOTransemail(projectId);
            String loginid = !"supersa".equals(SessUtil.getSessInfo().getUserloginid()) && !"sa".equals(SessUtil.getSessInfo().getUserloginid()) && !"public".equals(reply) ? SessUtil.getSessInfo().getUserloginid() : "";
            dmdao.save("email_send_log", sendLogToSave);



            String isSign = request.getParameter("isSign");
            String needSigin = request.getParameter("needSigin");

            if (StringUtils.equals("1",isSign) && StringUtils.equals("1",needSigin)) {
                this.forwardByUri(request,response,"LightpdfSignIntergrate.eSignRegister.do");
                return;
            }

            if (StringUtils.equals("2",isSign)) {
                this.forwardByUri(request,response,"extdatabind.sendMail.do");
                return;
            }




            String sendMailResult = daoTransemail.sendmail(emailSender, subject, content, receiver, cc, loginid, attachFiles, attachFileNames);

            if (StringUtils.equals(sendMailResult,"ERR")) {
                response.getOutputStream().write("发送失败，请检查您的收件人邮箱是否准确或者联系管理员".getBytes(StandardCharsets.UTF_8));
                response.getOutputStream().close();

                return;

            }








            response.getOutputStream().write("发送成功".getBytes(StandardCharsets.UTF_8));
            response.getOutputStream().close();

        } catch (Exception var24) {
            Log.error(var24.getMessage(), var24);
            var24.printStackTrace();
        }

    }



    private static File[] getFilesFromTemplate(String template,String projectId,String tableid,Map mapV,List<String> listFN,String debug) {

        DAODbApi dadao = new DAODbApi(projectId);
        AttachDAO attdao = new AttachDAO(projectId);
        List<File> listF = new ArrayList<File>();
        while(true){
            int p = template.indexOf("{");
            if(p<0)break;
            int p2 = template.indexOf("}",p);
            String fid = template.substring(p+1,p2);
            Map mapF = dadao.getMapFieldCopy(tableid, fid);
            if(mapF!=null) {
                String type = (String)mapF.get(CNT_Schema.type);
                if(type.equals("attach")){
                    String value = (String)mapV.get(fid);
                    if(value!=null && !value.equals("")){
                        try {
                            File[] fA = attdao.getFiles(value, tableid);
                            String[] fNameA = attdao.getFileNames(value, tableid);
                            for(int i=0;i<fA.length;i++) {
                                if(debug!=null && debug.equals("1")){
                                    Log.info("Attachment:"+fA[i].getAbsolutePath());
                                }
                                listF.add(fA[i]);
                                listFN.add(fNameA[i]);
                            }
                        }catch(Exception e) {
                            Log.error("",e);
                        }
                    }
                }
            }
            template = template.substring(0,p)+template.substring(p2+1);
        }
        File[] fA = new File[listF.size()];
        for(int i=0;i<listF.size();i++) {
            fA[i] = listF.get(i);
        }

        return fA;
    }


    private static String replaceTemplate(String template,String projectId, String tableid,
                                          String username,Map mapV,String attachtype,String language,String debug) throws Exception{
        DAODbApi dadao = new DAODbApi(projectId);
        DAODataMng dmdao = new DAODataMng(projectId);

        while(true){
            int p = template.indexOf("{");
            if(p<0)break;
            int p2 = template.indexOf("}",p);
            String fid = template.substring(p+1,p2);
            if(debug!=null && debug.equals("1")){
                Log.info("{"+fid+"}:"+fid);
            }
            String v = "";
            if(fid.equalsIgnoreCase("_username")){
                v = username;
            }else if(fid.equalsIgnoreCase("_loginpage")){
                String addr = ServerType.getCurrentAddr(projectId);
                v = addr +"/"+projectId;
            }else if(fid.equalsIgnoreCase("_serveraddr")){
                v = ServerType.getCurrentAddr(projectId);
            }else if(fid.equalsIgnoreCase("_projectid")){
                v = projectId;
            }else if(fid.equalsIgnoreCase("id")){
                v = String.valueOf(mapV.get(CNT_Schema.id));
            }else if(fid.substring(0,1).equals("$")){
                String fidTemp = fid.substring(1);
                v = (mapV.get(fidTemp)==null?"":String.valueOf(mapV.get(fidTemp)));
            }else if(fid.indexOf(",")>0){//tid,fid,where  where中用 [fid]表示替换本记录字段值
                String[] fidA = fid.split(",");
                if(fidA.length==3){
                    String tidLink = fidA[0];
                    String fidLink = fidA[1];
                    String whereLink = fidA[2];
                    while(true){
                        int pp = whereLink.indexOf("[");
                        if(pp<0) break;
                        int pp2 = whereLink.indexOf("]",pp);
                        if(pp2<0)break;
                        String fidInner = whereLink.substring(pp+1,pp2);
                        whereLink = whereLink.substring(0,pp)+mapV.get(fidInner)+whereLink.substring(pp2+1);
                    }
                    if(debug!=null && debug.equals("1")){
                        Log.info("whereLink:"+tidLink+" "+whereLink);
                    }
                    List listInner = dmdao.listRecord(tidLink, whereLink, "obj.id desc", 1);
                    if(debug!=null && debug.equals("1")){
                        Log.info(" find:"+listInner.size());
                    }
                    if(listInner.size()>0){
                        Map mapVLink = (Map)listInner.get(0);
                        if(debug!=null && debug.equals("1")){
                            Log.info(" mapVLink:"+mapVLink);
                        }
                        if(fidLink.equals("id")){
                            v = String.valueOf(mapVLink.get("id"));
                        }else{
                            Schema schema = dadao.getFieldType(tidLink, fidLink);
                            Map mapF = dadao.getMapFieldCopy(tidLink, fidLink);
                            v = schema.formatToOutput(tidLink, mapF, mapVLink);
                        }
                    }
                    if(debug!=null && debug.equals("1")){
                        Log.info(" value:"+v);
                    }
                }
            }else if(fid.indexOf(".")>0){
                String[] fidA = fid.split("\\.");
                String fidRef = fidA[0];
                String fidParent = fidA[1];
                Long ridParent = (Long)mapV.get(fidRef);
                if(ridParent!=null){
                    Map mapFRef = dadao.getMapFieldCopy(tableid, fidRef);
                    String tidParent = (String)mapFRef.get(CNT_Schema.refclassname);
                    Map mapVRef = dmdao.getRecord(tidParent, ridParent);
                    Schema schema = dadao.getFieldType(tidParent, fidParent);
                    Map mapFParent = dadao.getMapFieldCopy(tidParent, fidParent);
                    v = schema.formatToOutput(tidParent, mapFParent, mapVRef);
                }else{
                    v = "";
                }
            }else{

                Schema schema = dadao.getFieldType(tableid, fid);
                Map mapF = dadao.getMapFieldCopy(tableid, fid);
                if(mapF==null) {
                    v = projectId + " "+tableid+"."+fid+" is not exist!";
                }else {

                v = schema.formatToOutput(tableid, mapF, mapV);

                }
            }
            if(debug!=null && debug.equals("1")){
                Log.info("{"+fid+"}:"+v);
            }
            v = DAODataMng.whereStrReplace(v);
            template = template.substring(0,p)+v+template.substring(p2+1);
        }
        return template;
    }


}
