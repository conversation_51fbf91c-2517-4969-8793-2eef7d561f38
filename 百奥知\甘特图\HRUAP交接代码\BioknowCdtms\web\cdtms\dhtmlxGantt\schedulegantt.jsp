﻿<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=utf-8" %>
<%@ page language="java" pageEncoding="utf-8" %>

<html>
<head>
    <title>甘特图初始化设置</title>
    <script type="text/javascript" src="/public/js/jquery-3.2.1.min.js"></script>
    <script src="/cdtms/dhtmlxGantt/dhtmlx/dhtmlx.js"></script>
    <script src="/cdtms/dhtmlxGantt/codebase/dhtmlxgantt.js"></script>
    <script src="/cdtms/dhtmlxGantt/chosen/chosen.jquery.js"></script>
    <script type="text/javascript" src="public/js/base64.min.js"></script>
    <script src="/webutil/js/encode.js" type="text/javascript"></script>
    <script src="/webutil/js/popupdiv.js" type="text/javascript"></script>
    <script src="/uap/schemaplug/datetime/schema.js" type="text/javascript"></script>
    <link rel=stylesheet type="text/css" href="/public/icon/iconfont.css"/>
    <link rel="stylesheet" type="text/css" href="/public/css/pt.css">

    <style>
        #first, #second, #third{
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 50px;
        }
        .blue-circle {
            width: 25px;
            height: 25px;
            background-color: blue;
            border-radius: 50%; /* 使div变成圆形 */
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 20px;
            text-decoration: none;
        }
    </style>

</head>
<body class="pt-page" >
<!--日期选择DIV-->
<div id="div_datesel" class="datesel-p" style="display: none">
    <div id="div_datesel_head" class="datesel-head">
        <img src="uap/schemaplug/datetime/close.gif" class="datesel-close" onclick="$('#div_datesel').hide();" style="float: right"/>
    </div>
    <iframe id="frm_datesel" class="datesel-iframe"></iframe>
</div>


<div id="outer" style="">
    <div id="header" class="pt-page__head" style="width:30%; text-align: center; margin: 0 auto">
        <span id="firstspan" class="blue-circle">1</span><span>设置里程碑计划日期</span>
        <span id="secondspan">2</span><span>生成任务列表</span>
        <span id="thirdspan">3</span><span>设置任务负责人</span>
    </div>
    <div id="first">
        <c:forEach var="name" items="${schedulenamelist}" varStatus="i">
                <div class="pt-input__default pt-input-margin pt-input-block">
                    <span class="pt-input__default-label" style="width: 150px;"><span style="color:red">*</span> ${name.name}:</span>

                    <div class="pt-input__inner">
                        <c:if test="${name.planstarttime != null &&name.planstarttime != ''}">
                            <input type="text" id="${name.num}" placeholder="请输入或选择时间" value="${name.planstarttime}" readonly/>
                            <span class="pt-input__inner-after bio-font bio-icon__changguirenwu" ></span>
                        </c:if>
                        <c:if test="${name.planstarttime == null ||name.planstarttime == ''}">
                            <input type="text" id="${name.num}" placeholder="请输入或选择时间"/>
                            <span class="pt-input__inner-after bio-font bio-icon__changguirenwu" style="cursor: pointer" onclick="showDateSelectDiv(this,'${name.num}',100,10,1,21)"></span>
                        </c:if>

                    </div>
                </div>
        </c:forEach>
        <div>
            <button class="pt-btn pt-btn__primary" id="firstbtn" type="button">下一步</button>
        </div>
    </div>
    <div id="second" style="display: none">
        <table class="pt-table pt-table__hover pt-table__striped">
            <thead>
                <tr>
                    <th></th>
                    <th>任务名称</th>
                    <th>任务类型</th>
                    <th>前置任务</th>
                    <th>计划开始日期</th>
                    <th>计划结束日期</th>
                    <th>计划时长（自然日）</th>
                    <th>任务周期</th>
                </tr>
            </thead>
            <tbody id="tbody01" style="width:1200px !important;"></tbody>
        </table>
        <div>
            <button class="pt-btn pt-btn__primary" id="secondbeforebtn" type="button">上一步</button>
            <button class="pt-btn pt-btn__primary" id="secondafterbtn" type="button">下一步</button>
        </div>
    </div>
    <div id="third" style="display: none">
        <table class="pt-table pt-table__hover pt-table__striped">
            <thead>
            <tr>
                <th></th>
                <th>任务名称</th>
                <th>负责人</th>
                <th>计划开始日期</th>
                <th>计划结束日期</th>
                <th>计划时长（自然日）</th>
            </tr>
            </thead>
            <tbody id="tbody02" style="width:1200px !important;"></tbody>
        </table>
        <div>
            <button class="pt-btn pt-btn__primary" id="thirdbtn" type="button">完成</button>
        </div>
    </div>
</div>
<script>
    $(function (){
        $("#firstbtn").on("click", function (event){
            var time = "";
            var goto = true;
            $("#first input").each(function(){
                var fieldid = $(this).attr("id");
                var value = $(this).val();
                if(value === ""){

                    goto = "false";
                    return false;
                }
                if(time !== "" && time !== undefined) time +=";";
                time += fieldid+","+value;
            })
            if(goto === "false"){
                alert("日期必填");
                return false;
            }
            $.ajax({
                url: "/scheduleGantt.getScheduleTime.do",
                type: "GET",
                data: {"time":time},
                success: function(data){
                    $("#tbody01").html(data);
                }
            })
            $("#first").hide();
            $("#secondspan").addClass("blue-circle");
            $("#second").show();
        });
        $("#secondbeforebtn").on("click", function (){
            $("#second").hide();
            $("#secondspan").removeClass("blue-circle");
            $("#first").show();
        });
        $("#secondafterbtn").on("click", function (){
            var time = "";
            $("#first input").each(function(){
                var fieldid = $(this).attr("id");
                var value = $(this).val();
                if(time !== "" && time !== undefined) time +=";";
                time += fieldid+","+value;
            })
            $.ajax({
                url: "/scheduleGantt.getRespon.do",
                type: "GET",
                data: {"time":time, "studyid":"${studyid}"},
                success: function(data){
                    $("#tbody02").html(data);
                }
            })
            $("#second").hide();
            $("#third").show();
            $("#thirdspan").addClass("blue-circle");
        });
        $("#thirdbtn").on("click", function (){
            var time = "";
            $("#first input").each(function(){
                var fieldid = $(this).attr("id");
                var value = $(this).val();
                if(time !== "" && time !== undefined) time +=";";
                time += fieldid+","+value;
            })
            var per = "";
            $(".fre").each(function(){
                var fieldid = $(this).attr("id");
                var value = $(this).val();
                if(per !== "" && per !== undefined) per +=";";
                per += fieldid+","+value;
            })
            var respo = "";
            $(".role").each(function(){
                var fieldid = $(this).attr("id");
                var value = $(this).val();
                if($(this).is(":checked")){
                    if(respo !== "" && respo !== undefined) respo +=";";
                    respo += fieldid+","+value;
                }
            })

            $.ajax({
                url: "/scheduleGantt.finishSchedule.do",
                type: "GET",
                data: {"time":time,"studyid":"${studyid}","per":per,"respo":respo},
                success: function(data){
                    window.location.href="/scheduleGantt.show.do?studyId=" + ${studyid};
                }
            })
        });
    })

</script>
</body>
</html>
