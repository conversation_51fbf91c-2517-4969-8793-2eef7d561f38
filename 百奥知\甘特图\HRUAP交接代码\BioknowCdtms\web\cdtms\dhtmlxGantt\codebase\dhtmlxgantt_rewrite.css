/* border */
.gantt_grid .gantt_grid_scale,
.gantt_grid .gantt_row,
.gantt_container,
.gantt_grid_scale,
.gantt_task_scale,
.gantt_scale_line,
.gantt_grid_head_cell,
.gantt_drag_marker.gantt_grid_resize_area,
.gantt_drag_marker.gantt_row_grid_resize_area,
.gantt_cell,
.gantt_scale_cell,
.gantt_task_cell,
.gantt-info div,
.gantt_cal_light,
.gantt_cal_light select,
.gantt_cal_ltext textarea,
.gantt_cal_larea,
.gantt_cal_light_wide .gantt_cal_larea,
.gantt_cal_light_wide .gantt_wrap_section,
.gantt_section_time .gantt_time_selects select,
.gantt_duration .gantt_duration_dec,
.gantt_duration .gantt_duration_inc,
.gantt_duration .gantt_duration_value,
.gantt_resource_row,
.gantt_cal_qi_title,
.gantt_layout_cell_border_left
.gantt_layout_cell_border_right
.gantt_layout_cell_border_top
.gantt_layout_cell_border_bottom {
  border-color: var(--border-base-color);
  font-family: inherit;
}

/* Tree */
.gantt_cell,
.gantt_grid_head_cell {
  padding: 0 12px;
}

.gantt_tree_icon.gantt_open,
.gantt_tree_icon.gantt_close {
  background-size: 9px;
}
 

.gantt_grid_head_cell.gantt_grid_head_text,
.gantt_grid_head_cell.gantt_grid_head_owners,
.gantt_grid_head_cell.gantt_grid_head_planned_start_date,
.gantt_grid_head_cell.gantt_grid_head_actual_start_date,
.gantt_grid_head_cell.gantt_grid_head_actual_end_date {
  text-align: left;
}

.gantt_grid_head_cell.gantt_grid_head_duration_formatted {
  text-align: right;
}

.gantt_grid .gantt_grid_scale .gantt_grid_head_cell,
.gantt_grid_data .gantt_cell {
  color: var(--text-primary-color);
  font-size: 12px;
}

.gantt_grid .gantt_grid_scale .gantt_grid_head_cell {
  font-weight: bold;
}

.gantt_grid .gantt_grid_scale .gantt_grid_head_cell,
.gantt_grid_data .gantt_cell .gantt_tree_content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.gantt_row {
  border-bottom: 1px solid var(--border-base-color);
}

div.gantt_scale_line {
  border-top: none;
}

div.gantt_grid_scale:after, div.gantt_scale_line:last-child:after {
  box-shadow: none;
}
.gantt_grid .gantt_grid_scale, .gantt_grid .gantt_row {
  border-right: none;
}

.gantt_layout_cell_border_right {
  box-shadow: 4px 0 8px -4px rgba(0, 0, 0, .2);
  z-index: 1;
}

.gantt_grid_head_add {
  display: none;
}

/* hover */
.gridHoverStyle,
.gridSelection,
.timelineSelection,
.gantt_grid_data
.gantt_row.odd:hover,
.gantt_grid_data .gantt_row:hover,
.gantt_grid_data .gantt_row.gantt_selected,
.gantt_grid_data .gantt_row.odd.gantt_selected,
.gantt_task_row.gantt_selected {
  background-color: var(--background-base-color);
}

/* scale */
.gantt_task .gantt_task_scale {
  position: relative;
  z-index: 10;
}
.gantt_task .gantt_task_scale .gantt_scale_cell {
  color: var(--text-regular-color);
  font-size: 12px;
  border-right: none;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
}
.gantt_task .gantt_task_scale .gantt_scale_line.gantt_scale_year .gantt_scale_cell {
  font-size: 14px;
  font-weight: bold;
}

/* line */
.gantt_task_link .gantt_link_arrow_right {
  border-width: 4px 6px;
  margin-left: -1px;
  margin-top: -1px;
}

.gantt_grid_target_marker,
.gantt_line_wrapper div {
  background-color: var(--gantt-link-color);
}

.gantt_task_link:hover .gantt_line_wrapper div {
  box-shadow: 0 0 5px 0 var(--gantt-link-color);
}

.gantt_link_arrow_right {
  border-left-color: var(--gantt-link-color);
}

.gantt_link_arrow_left {
  border-right-color: var(--gantt-link-color);
}

.gantt_link_arrow_up {
  border-bottom-color: var(--gantt-link-color);
}

.gantt_link_arrow_down {
  border-top-color: var(--gantt-link-color);
}


/* task */
.gantt_bar_task {
  height: 16px !important;
  min-width: 2px;
  transform: translateY(4px);
  border: none;
}
.gantt_task_line {
  background-color: var(--task-base-color);
  border-color: var(--task-base-color);
  border-radius: 2px;
}

.gantt_task_content {
  text-align: left;
  padding: 0 16px;
  font-weight: normal;
  color: var(--text-primary-color);
}

.gantt_task_line .gantt_task_drag {
  background: none;
  top: 20%;
  width: 3px;
  height: 60%;
  border-left: 1px solid var(--text-primary-color);
  border-right: 1px solid var(--text-primary-color);
  opacity: 1;
}

.gantt_task_line .gantt_task_drag.task_left {
  left: 3px;
}

.gantt_task_line .gantt_task_drag.task_right {
  right: 3px;
}

/* project */
.gantt_task_line.gantt_project {
  background-color: transparent;
  border: none;
  border-color: var(--task-project-color);
}

.gantt_task_line.gantt_project .gantt_task_progress_wrapper {
  overflow: hidden;
  /* height: 8px;
  top: 5px;
  background-color: var(--task-project-color);
  overflow: visible; */
}
.gantt_task_line.gantt_project .gantt_task_progress_wrapper::before {
  content: '';
  display: block;
  height: 8px;
  width: 100%;
  position: relative;
  top: 4px;
  background-color: var(--task-project-color);
}

.gantt_task_line.gantt_project .gantt_task_progress_wrapper .gantt_task_progress {
  background: transparent;
}

.gantt_task_line.gantt_project .gantt_task_progress_wrapper .gantt_task_progress::before,
.gantt_task_line.gantt_project .gantt_task_progress_wrapper .gantt_task_progress::after
 {
  content: '';
  display: block;
  position: absolute;
  top: 4px;
  width: 16px;
  height: 16px;
  box-sizing: border-box;
  border: 8px solid transparent;
}

.gantt_task_line.gantt_project .gantt_task_progress_wrapper .gantt_task_progress::before {
  left: 0;
  border-left-color: var(--task-project-color);
}

.gantt_task_line.gantt_project .gantt_task_progress_wrapper .gantt_task_progress::after {
  right: 0;
  border-right-color: var(--task-project-color);
}

.gantt_task_progress_drag {
  display: none !important;
}

.gantt_task_progress.start {
  display: none !important;
}


/* milestone */
.gantt_task_line.gantt_milestone .gantt_task_content {
  background-color: transparent;
  border: none;
  transform: none;
  background-image: url(/cdtms/dhtmlxGantt/codebase/images/milestone.png);
  background-size: 100% 100%;
  position: relative;
  top: 2px;
  left: 1px;
  width: 16px !important;
  height: 16px !important;
}

/* task */
.gantt_task_line.doing-task {
  background-color: var(--task-doing-color);
  border-color: var(--task-doing-color);
}

.gantt_task_line.future-task {
  background-color: var(--task-future-color);
  border-color: var(--task-future-color);
}

.gantt_task_line.completed-task {
  background-color: var(--task-completed-color);
  border-color: var(--task-completed-color);
}

.gantt_task_line.overdue-task {
  background-color: var(--task-overdue-color);
  border-color: var(--task-overdue-color);
}
.gantt_task_line.hidden-task {
  display: none;
}

.gantt_task_row:not(.gantt_selected) .gantt_task_cell.week_end {
  background-color: #EFF5FD;
}

/* tooltip */
.gantt_tooltip {
  padding: 0;
  border-radius: 2px;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.40);
  min-width: 240px;
}

.gantt_tooltip_title {
  padding: 0 16px;
  font-size: 16px;
  font-weight: bold;
  line-height: 47px;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-base-color);
}

.gantt_tooltip_list {
  list-style: none;
  padding: 8px 16px;
  color: var(--text-regular-color);
  line-height: 2;
  font-size: 12px;
}


.gantt_add {
  background-image: url(/cdtms/dhtmlxGantt/codebase/images/plus.png);
  background-size: 15px;
  -moz-opacity: 1;
  opacity: 1;
}

/* Modal */
.gantt_cal_light .gantt_btn_set.gantt_save_btn_set, .gantt_popup_button.gantt_ok_button, .gantt_qi_big_icon.icon_edit {
  background-color: var(--theme-primary-color);
}

.gantt_cal_light .gantt_btn_set.gantt_cancel_btn_set,
.gantt_cal_light .gantt_btn_set.gantt_cancel_btn_set:hover, .gantt_popup_button.gantt_cancel_button:hover {
  color: var(--theme-primary-color);
}

div.gantt_cal_light select:focus {
  outline-color: var(--theme-primary-color);
}

.gantt_grid .gantt_grid_scale .gantt_grid_head_cell ,.gantt_task .gantt_task_scale .gantt_scale_cell{
  text-transform: none;
}
