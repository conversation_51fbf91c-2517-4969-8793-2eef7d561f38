<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta charset="UTF-8" />

  <link href="data:image/x-icon;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQEAYAAABPYyMiAAAABmJLR0T///////8JWPfcAAAACXBIWXMAAABIAAAASABGyWs+AAAAF0lEQVRIx2NgGAWjYBSMglEwCkbBSAcACBAAAeaR9cIAAAAASUVORK5CYII=" rel="icon" type="image/x-icon" />

  <script type="text/javascript" src="lib/sockjs.js"></script>
  <script type="text/javascript" src="static/jquery.min.js"></script>

  <script type="text/javascript" src="config.js"></script>
</head>
<body>
<form>
  <select id="transport">
  <option value="">- any - </option>
  <option value="websocket">websocket</option>
  <option value="xdr-streaming">xdr-streaming</option>
  <option value="xhr-streaming">xhr-streaming</option>
  <option value="iframe-eventsource">iframe-eventsource</option>
  <option value="iframe-htmlfile">iframe-htmlfile</option>
  <option value="xdr-polling">xdr-polling</option>
  <option value="xhr-polling">xhr-polling</option>
  <option value="iframe-xhr-polling">iframe-xhr-polling</option>
  <option value="jsonp-polling">jsonp-polling</option>
  </select>
  <input type="checkbox" id="sameOrigin" checked>Same Origin?
  <input type="button" value="Start" id="connect">
  <input type="button" value="Stop" id="disconnect" disabled="yes">
</form>

  Connected: <code id="out"></code><br>
  <code id="logs" style="height:200px; overflow:auto; display: block; border: 1px gray solid;">
  </code>

<script>
    function log(a) {
            if ('console' in window && 'log' in window.console) {
                console.log(a);
            }
            $('#logs').append($("<code>").text(a));
            $('#logs').append($("<br>"));
            $('#logs').scrollTop($('#logs').scrollTop()+10000);
      }

    var started = false;
    var sjs;
    var i = 0;
    var transport;
    var t0;
    function onopen() {
      $('#sameOrigin').attr('disabled', true);
       var td = (new Date()).getTime() - t0;
       i += 1;
       $('#out').text(''+i +'  ' + td + ' ms');
       sjs.close();
    };
    function onclose(e) {
        if (started && e.code === 1000) {
            t0 = (new Date()).getTime();
            var url;
            if ($('#sameOrigin').prop('checked')) {
              if (window.location.origin) url = window.location.origin;
              else {
                url = window.location.protocol + '//' + window.location.hostname +
                  (window.location.port ? ':' + window.location.port : '');
              }
            } else {
              url = clientOptions.url;
            }
            sjs = new SockJS(clientOptions.url + '/echo', null, { transports: transport });
            sjs.onopen = onopen
            sjs.onclose = onclose;
        } else {
            log('[stopped] ' + e.code + ', ' + e.reason);
            $('#connect').each(function(_,e){e.disabled='';});
            $('#disconnect').attr('disabled', true);
            $('#sameOrigin').attr('disabled', false);
        }
    };

    $('#connect').click(function() {
        started = true;
        transport = $('#transport').val() || undefined;
        log('[starting] ' + transport);
        onclose({code:1000});
        $('#connect').attr('disabled', true);
        $('#disconnect').each(function(_,e){e.disabled='';});
    });
    $('#disconnect').click(function() {
        $('#disconnect').attr('disabled', true);
        log('[stopping...]');
        started = false;
    });
</script>
</body>
</html>
