package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.mvc.ioc.AppStartFace;
import net.bioknow.passport.uiframe.FuncUtil;
import net.bioknow.passport.validate.PageIgnore;
import net.bioknow.services.uap.dbdatamng.function.FuncFactoryNew;
import net.bioknow.uap.dbdatamng.function.FuncFactory;

import javax.servlet.ServletConfig;

public class EHAppStart implements AppStartFace{

    public void onStart(ServletConfig arg) throws Exception {
        FuncFactory.addRecordFunc(new DTRFlightpdfSign());
        FuncFactory.addRecordFunc(new DTRFlightpdfSetposition());
        FuncFactory.addRecordFunc(new DTRFlightpdfSetsigner());
        FuncFactory.addRecordFunc(new DTRFlightpdfCancel());
        FuncFactory.addRecordFunc(new DTRFlightpdfSignView());
        FuncFactory.addRecordFunc(new DTRFlightpdfReplenish());
        FuncFactory.addTableFunc(new DTTFActionSign());
        FuncFactory.addTableFunc(new DTTFSignCancel());

        FuncFactoryNew.addRecordFunc(new DTRFlightpdfSignVue());
        FuncFactoryNew.addRecordFunc(new DTRFlightpdfSetpositionVue());
        FuncFactoryNew.addRecordFunc(new DTRFlightpdfSetsignerVue());
        FuncFactoryNew.addRecordFunc(new DTRFlightpdfCancelVue());
        FuncFactoryNew.addRecordFunc(new DTRFlightpdfSignViewVue());
        FuncFactoryNew.addRecordFunc(new DTRFlightpdfReplenishVue());
        FuncFactoryNew.addTableFunc(new DTTFActionSignVue());

        PageIgnore.addIgnore("/lightpdfSign.callback.do");
        PageIgnore.addIgnore("/lightpdfSign.checkIsLogin.do");
        PageIgnore.addIgnore("/lightpdfSign.idVerify.do");
        PageIgnore.addIgnore("/lightpdfSign.resetpwdsubmit.do");
        PageIgnore.addIgnore("/lightpdfSign.sendcheckcode.do");
        PageIgnore.addIgnore("/lightpdfSign.changepassword.do");
        PageIgnore.addIgnore("/lightpdfSign.checktoken.do");

//        FuncUtil.addFunction("sysfunc", "sysfunc.param", "系统参数设定", "Login Parameters Set", "doc.gif", "authparam.list.do");

        FuncUtil.addFunction(FuncUtil.FuncFolder_prjfunc,"LightpdfSign","LightpdfSign集成",null,null,"configdb.listcfg.do?xmlpath="+ DAOLightpdfSignIntegrate.xmlpath);
//        ConfigDbUtil.registerCFGFile(DAOeSignIntegrate.xmlpath);










    }

}
