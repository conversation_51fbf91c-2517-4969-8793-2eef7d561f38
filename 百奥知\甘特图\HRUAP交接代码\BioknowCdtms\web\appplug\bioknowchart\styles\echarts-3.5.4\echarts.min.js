!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.echarts=e():t.echarts=e()}(this,function(){return function(t){function e(i){if(n[i])return n[i].exports;var r=n[i]={exports:{},id:i,loaded:!1};return t[i].call(r.exports,r,r.exports,e),r.loaded=!0,r.exports}var n={};return e.m=t,e.c=n,e.p="",e(0)}([function(t,e,n){t.exports=n(2),n(112),n(106),n(116),n(196),n(212),n(239),n(57),n(220),n(213),n(230),n(223),n(222),n(221),n(202),n(231),n(246)},function(t,e){function n(t){if(null==t||"object"!=typeof t)return t;var e=t,i=F.call(t);if("[object Array]"===i){e=[];for(var r=0,o=t.length;r<o;r++)e[r]=n(t[r])}else if(V[i]){var a=t.constructor;if(t.constructor.from)e=a.from(t);else{e=new a(t.length);for(var r=0,o=t.length;r<o;r++)e[r]=n(t[r])}}else if(!B[i]&&!z(t)&&!T(t)){e={};for(var s in t)t.hasOwnProperty(s)&&(e[s]=n(t[s]))}return e}function i(t,e,r){if(!S(e)||!S(t))return r?n(e):t;for(var o in e)if(e.hasOwnProperty(o)){var a=t[o],s=e[o];!S(s)||!S(a)||_(s)||_(a)||T(s)||T(a)||M(s)||M(a)||z(s)||z(a)?!r&&o in t||(t[o]=n(e[o],!0)):i(a,s,r)}return t}function r(t,e){for(var n=t[0],r=1,o=t.length;r<o;r++)n=i(n,t[r],e);return n}function o(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function a(t,e,n){for(var i in e)e.hasOwnProperty(i)&&(n?null!=e[i]:null==t[i])&&(t[i]=e[i]);return t}function s(){return document.createElement("canvas")}function l(){return N||(N=$.createCanvas().getContext("2d")),N}function u(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function h(t,e){function n(){}var i=t.prototype;n.prototype=e.prototype,t.prototype=new n;for(var r in i)t.prototype[r]=i[r];t.prototype.constructor=t,t.superClass=e}function c(t,e,n){t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,a(t,e,n)}function d(t){if(t)return"string"!=typeof t&&"number"==typeof t.length}function f(t,e,n){if(t&&e)if(t.forEach&&t.forEach===H)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function p(t,e,n){if(t&&e){if(t.map&&t.map===q)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}}function g(t,e,n,i){if(t&&e){if(t.reduce&&t.reduce===j)return t.reduce(e,n,i);for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function m(t,e,n){if(t&&e){if(t.filter&&t.filter===W)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}}function v(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]}function x(t,e){var n=Z.call(arguments,2);return function(){return t.apply(e,n.concat(Z.call(arguments)))}}function y(t){var e=Z.call(arguments,1);return function(){return t.apply(this,e.concat(Z.call(arguments)))}}function _(t){return"[object Array]"===F.call(t)}function b(t){return"function"==typeof t}function w(t){return"[object String]"===F.call(t)}function S(t){var e=typeof t;return"function"===e||!!t&&"object"==e}function M(t){return!!B[F.call(t)]}function T(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function I(t){return t!==t}function A(t){for(var e=0,n=arguments.length;e<n;e++)if(null!=arguments[e])return arguments[e]}function C(t,e){return null!=t?t:e}function P(t,e,n){return null!=t?t:null!=e?e:n}function D(){return Function.call.apply(Z,arguments)}function k(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function L(t,e){if(!t)throw new Error(e)}function O(t){t[X]=!0}function z(t){return t[X]}function E(t){t&&f(t,function(t,e){this.set(e,t)},this)}function R(t){return new E(t)}var N,B={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},V={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},F=Object.prototype.toString,G=Array.prototype,H=G.forEach,W=G.filter,Z=G.slice,q=G.map,j=G.reduce,X="__ec_primitive__",Y="_ec_",U=4;E.prototype={constructor:E,get:function(t){return this[Y+t]},set:function(t,e){return this[Y+t]=e,e},each:function(t,e){void 0!==e&&(t=x(t,e));for(var n in this)this.hasOwnProperty(n)&&t(this[n],n.slice(U))},removeKey:function(t){delete this[Y+t]}};var $={inherits:h,mixin:c,clone:n,merge:i,mergeAll:r,extend:o,defaults:a,getContext:l,createCanvas:s,indexOf:u,slice:D,find:v,isArrayLike:d,each:f,map:p,reduce:g,filter:m,bind:x,curry:y,isArray:_,isString:w,isObject:S,isFunction:b,isBuiltInObject:M,isDom:T,eqNaN:I,retrieve:A,retrieve2:C,retrieve3:P,assert:L,setAsPrimitive:O,createHashMap:R,normalizeCssArray:k,noop:function(){}};t.exports=$},function(t,e,n){function i(t){return function(e,n,i){e=e&&e.toLowerCase(),V.prototype[t].call(this,e,n,i)}}function r(){V.call(this)}function o(t,e,n){function i(t,e){return t.prio-e.prio}n=n||{},"string"==typeof e&&(e=ut[e]),this.id,this.group,this._dom=t;var o=this._zr=R.init(t,{renderer:n.renderer||"canvas",devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height});this._throttledZrFlush=E.throttle(N.bind(o.flush,o),17);var e=N.clone(e);e&&C(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new I,this._api=b(this),V.call(this),this._messageCenter=new r,this._initEvents(),this.resize=N.bind(this.resize,this),this._pendingActions=[],F(lt,i),F(ot,i),o.animation.on("frame",this._onframe,this),N.setAsPrimitive(this)}function a(t,e,n){var i,r=this._model,o=this._coordSysMgr.getCoordinateSystems();e=z.parseFinder(r,e);for(var a=0;a<o.length;a++){var s=o[a];if(s[t]&&null!=(i=s[t](r,e,n)))return i}}function s(t,e,n,i,r){function o(i){i&&i.__alive&&i[e]&&i[e](i.__model,a,t._api,n)}var a=t._model;if(!i)return void G(t._componentsViews.concat(t._chartsViews),o);var s={};s[i+"Id"]=n[i+"Id"],s[i+"Index"]=n[i+"Index"],s[i+"Name"]=n[i+"Name"];var l={mainType:i,query:s};r&&(l.subType=r),a&&a.eachComponent(l,function(e,n){o(t["series"===i?"_chartsMap":"_componentsMap"][e.__viewId])},t)}function l(t,e){var n=t.type,i=t.escapeConnect,r=it[n],o=r.actionInfo,a=(o.update||"update").split(":"),l=a.pop();a=null!=a[0]&&H(a[0]),this[$]=!0;var u=[t],h=!1;t.batch&&(h=!0,u=N.map(t.batch,function(e){return e=N.defaults(N.extend({},e),t),e.batch=null,e}));var c,d=[],f="highlight"===n||"downplay"===n;G(u,function(t){c=r.action(t,this._model,this._api),c=c||N.extend({},t),c.type=o.event||c.type,d.push(c),f?s(this,l,t,"series"):a&&s(this,l,t,a.main,a.sub)},this),"none"===l||f||a||(this[Q]?(et.prepareAndUpdate.call(this,t),this[Q]=!1):et[l].call(this,t)),c=h?{type:o.event||n,escapeConnect:i,batch:d}:d[0],this[$]=!1,!e&&this._messageCenter.trigger(c.type,c)}function u(t){for(var e=this._pendingActions;e.length;){var n=e.shift();l.call(this,n,t)}}function h(t){!t&&this.trigger("updated")}function c(t,e,n){var i=this._api;G(this._componentsViews,function(r){var o=r.__model;r[t](o,e,i,n),_(o,r)},this),e.eachSeries(function(r,o){var a=this._chartsMap[r.__viewId];a[t](r,e,i,n),_(r,a),y(r,a)},this),x(this._zr,e),G(st,function(t){t(e,i)})}function d(t,e){for(var n="component"===t,i=n?this._componentsViews:this._chartsViews,r=n?this._componentsMap:this._chartsMap,o=this._zr,a=0;a<i.length;a++)i[a].__alive=!1;e[n?"eachComponent":"eachSeries"](function(t,a){if(n){if("series"===t)return}else a=t;var s="_ec_"+a.id+"_"+a.type,l=r[s];if(!l){var u=H(a.type),h=n?k.getClass(u.main,u.sub):L.getClass(u.sub);if(!h)return;l=new h,l.init(e,this._api),r[s]=l,i.push(l),o.add(l.group)}a.__viewId=l.__id=s,l.__alive=!0,l.__model=a,l.group.__ecComponentInfo={mainType:a.mainType,index:a.componentIndex}},this);for(var a=0;a<i.length;){var s=i[a];s.__alive?a++:(o.remove(s.group),s.dispose(e,this._api),i.splice(a,1),delete r[s.__id],s.__id=s.group.__ecComponentInfo=null)}}function f(t,e){G(ot,function(n){n.func(t,e)})}function p(t){var e={};t.eachSeries(function(t){var n=t.get("stack"),i=t.getData();if(n&&"list"===i.type){var r=e[n];e.hasOwnProperty(n)&&r&&(i.stackedOn=r),e[n]=i}})}function g(t,e){var n=this._api;G(lt,function(i){i.isLayout&&i.func(t,n,e)})}function m(t,e,n){var i=this._api;t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()}),G(lt,function(r){(!n||!r.isLayout)&&r.func(t,i,e)})}function v(t,e){var n=this._api;G(this._componentsViews,function(i){var r=i.__model;i.render(r,t,n,e),_(r,i)},this),G(this._chartsViews,function(t){t.__alive=!1},this),t.eachSeries(function(i,r){var o=this._chartsMap[i.__viewId];o.__alive=!0,o.render(i,t,n,e),o.group.silent=!!i.get("silent"),_(i,o),y(i,o)},this),x(this._zr,t),G(this._chartsViews,function(e){e.__alive||e.remove(t,n)},this)}function x(t,e){var n=t.storage,i=0;n.traverse(function(t){t.isGroup||i++}),i>e.get("hoverLayerThreshold")&&!S.node&&n.traverse(function(t){t.isGroup||(t.useHoverLayer=!0)})}function y(t,e){var n=0;e.group.traverse(function(t){"group"===t.type||t.ignore||n++});var i=+t.get("progressive"),r=n>t.get("progressiveThreshold")&&i&&!S.node;r&&e.group.traverse(function(t){t.isGroup||(t.progressive=r?Math.floor(n++/i):-1,r&&t.stopAnimation(!0))});var o=t.get("blendMode")||null;e.group.traverse(function(t){t.isGroup||t.setStyle("blend",o)})}function _(t,e){var n=t.get("z"),i=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=i&&(t.zlevel=i))})}function b(t){var e=t._coordSysMgr;return N.extend(new T(t),{getCoordinateSystems:N.bind(e.getCoordinateSystems,e),getComponentByElement:function(e){for(;e;){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}}})}function w(t){function e(t,e){for(var n=0;n<t.length;n++){var i=t[n];i[o]=e}}var n=0,i=1,r=2,o="__connectUpdateStatus";N.each(rt,function(a,s){t._messageCenter.on(s,function(a){if(dt[t.group]&&t[o]!==n){if(a&&a.escapeConnect)return;var s=t.makeActionFromEvent(a),l=[];N.each(ct,function(e){e!==t&&e.group===t.group&&l.push(e)}),e(l,n),G(l,function(t){t[o]!==i&&t.dispatchAction(s)}),e(l,r)}})})}/*!
	 * ECharts, a javascript interactive chart library.
	 *
	 * Copyright (c) 2015, Baidu Inc.
	 * All rights reserved.
	 *
	 * LICENSE
	 * https://github.com/ecomfe/echarts/blob/master/LICENSE.txt
	 */
var S=n(10),M=n(143),T=n(105),I=n(26),A=n(144),C=n(151),P=n(13),D=n(17),k=n(67),L=n(30),O=n(3),z=n(5),E=n(37),R=n(92),N=n(1),B=n(22),V=n(23),F=n(52),G=N.each,H=P.parseClassType,W=1e3,Z=5e3,q=1e3,j=2e3,X=3e3,Y=4e3,U=5e3,$="__flagInMainProcess",K="__hasGradientOrPatternBg",Q="__optionUpdated",J=/^[a-zA-Z0-9_]+$/;r.prototype.on=i("on"),r.prototype.off=i("off"),r.prototype.one=i("one"),N.mixin(r,V);var tt=o.prototype;tt._onframe=function(){if(this[Q]){var t=this[Q].silent;this[$]=!0,et.prepareAndUpdate.call(this),this[$]=!1,this[Q]=!1,u.call(this,t),h.call(this,t)}},tt.getDom=function(){return this._dom},tt.getZr=function(){return this._zr},tt.setOption=function(t,e,n){var i;if(N.isObject(e)&&(n=e.lazyUpdate,i=e.silent,e=e.notMerge),this[$]=!0,!this._model||e){var r=new A(this._api),o=this._theme,a=this._model=new M(null,null,o,r);a.init(null,null,o,r)}this._model.setOption(t,at),n?(this[Q]={silent:i},this[$]=!1):(et.prepareAndUpdate.call(this),this._zr.flush(),this[Q]=!1,this[$]=!1,u.call(this,i),h.call(this,i))},tt.setTheme=function(){console.log("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},tt.getModel=function(){return this._model},tt.getOption=function(){return this._model&&this._model.getOption()},tt.getWidth=function(){return this._zr.getWidth()},tt.getHeight=function(){return this._zr.getHeight()},tt.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},tt.getRenderedCanvas=function(t){if(S.canvasSupported){t=t||{},t.pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor");var e=this._zr,n=e.storage.getDisplayList();return N.each(n,function(t){t.stopAnimation(!0)}),e.painter.getRenderedCanvas(t)}},tt.getDataURL=function(t){t=t||{};var e=t.excludeComponents,n=this._model,i=[],r=this;G(e,function(t){n.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)})});var o=this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return G(i,function(t){t.group.ignore=!1}),o},tt.getConnectedDataURL=function(t){if(S.canvasSupported){var e=this.group,n=Math.min,i=Math.max,r=1/0;if(dt[e]){var o=r,a=r,s=-r,l=-r,u=[],h=t&&t.pixelRatio||1;N.each(ct,function(r,h){if(r.group===e){var c=r.getRenderedCanvas(N.clone(t)),d=r.getDom().getBoundingClientRect();o=n(d.left,o),a=n(d.top,a),s=i(d.right,s),l=i(d.bottom,l),u.push({dom:c,left:d.left,top:d.top})}}),o*=h,a*=h,s*=h,l*=h;var c=s-o,d=l-a,f=N.createCanvas();f.width=c,f.height=d;var p=R.init(f);return G(u,function(t){var e=new O.Image({style:{x:t.left*h-o,y:t.top*h-a,image:t.dom}});p.add(e)}),p.refreshImmediately(),f.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},tt.convertToPixel=N.curry(a,"convertToPixel"),tt.convertFromPixel=N.curry(a,"convertFromPixel"),tt.containPixel=function(t,e){var n,i=this._model;return t=z.parseFinder(i,t),N.each(t,function(t,i){i.indexOf("Models")>=0&&N.each(t,function(t){var r=t.coordinateSystem;if(r&&r.containPoint)n|=!!r.containPoint(e);else if("seriesModels"===i){var o=this._chartsMap[t.__viewId];o&&o.containPoint&&(n|=o.containPoint(e,t))}},this)},this),!!n},tt.getVisual=function(t,e){var n=this._model;t=z.parseFinder(n,t,{defaultMainType:"series"});var i=t.seriesModel,r=i.getData(),o=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?r.indexOfRawIndex(t.dataIndex):null;return null!=o?r.getItemVisual(o,e):r.getVisual(e)},tt.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},tt.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var et={update:function(t){var e=this._model,n=this._api,i=this._coordSysMgr,r=this._zr;if(e){e.restoreData(),i.create(this._model,this._api),f.call(this,e,n),p.call(this,e),i.update(e,n),m.call(this,e,t),v.call(this,e,t);var o=e.get("backgroundColor")||"transparent",a=r.painter;if(a.isSingleCanvas&&a.isSingleCanvas())r.configLayer(0,{clearColor:o});else{if(!S.canvasSupported){var s=B.parse(o);o=B.stringify(s,"rgb"),0===s[3]&&(o="transparent")}o.colorStops||o.image?(r.configLayer(0,{clearColor:o}),this[K]=!0,this._dom.style.background="transparent"):(this[K]&&r.configLayer(0,{clearColor:null}),this[K]=!1,this._dom.style.background=o)}G(st,function(t){t(e,n)})}},updateView:function(t){var e=this._model;e&&(e.eachSeries(function(t){t.getData().clearAllVisual()}),m.call(this,e,t),c.call(this,"updateView",e,t))},updateVisual:function(t){var e=this._model;e&&(e.eachSeries(function(t){t.getData().clearAllVisual()}),m.call(this,e,t,!0),c.call(this,"updateVisual",e,t))},updateLayout:function(t){var e=this._model;e&&(g.call(this,e,t),c.call(this,"updateLayout",e,t))},prepareAndUpdate:function(t){var e=this._model;d.call(this,"component",e),d.call(this,"chart",e),et.update.call(this,t)}};tt.resize=function(t){this[$]=!0,this._zr.resize(t);var e=this._model&&this._model.resetOption("media"),n=e?"prepareAndUpdate":"update";et[n].call(this),this._loadingFX&&this._loadingFX.resize(),this[$]=!1;var i=t&&t.silent;u.call(this,i),h.call(this,i)},tt.showLoading=function(t,e){if(N.isObject(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),ht[t]){var n=ht[t](this._api,e),i=this._zr;this._loadingFX=n,i.add(n)}},tt.hideLoading=function(){this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},tt.makeActionFromEvent=function(t){var e=N.extend({},t);return e.type=rt[t.type],e},tt.dispatchAction=function(t,e){if(N.isObject(e)||(e={silent:!!e}),it[t.type]&&this._model){if(this[$])return void this._pendingActions.push(t);l.call(this,t,e.silent),e.flush?this._zr.flush(!0):e.flush!==!1&&S.browser.weChat&&this._throttledZrFlush(),u.call(this,e.silent),h.call(this,e.silent)}},tt.on=i("on"),tt.off=i("off"),tt.one=i("one");var nt=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];tt._initEvents=function(){G(nt,function(t){this._zr.on(t,function(e){var n,i=this.getModel(),r=e.target;if("globalout"===t)n={};else if(r&&null!=r.dataIndex){var o=r.dataModel||i.getSeriesByIndex(r.seriesIndex);n=o&&o.getDataParams(r.dataIndex,r.dataType)||{}}else r&&r.eventData&&(n=N.extend({},r.eventData));n&&(n.event=e,n.type=t,this.trigger(t,n))},this)},this),G(rt,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},tt.isDisposed=function(){return this._disposed},tt.clear=function(){this.setOption({series:[]},!0)},tt.dispose=function(){if(!this._disposed){this._disposed=!0;var t=this._api,e=this._model;G(this._componentsViews,function(n){n.dispose(e,t)}),G(this._chartsViews,function(n){n.dispose(e,t)}),this._zr.dispose(),delete ct[this.id]}},N.mixin(o,V);var it={},rt={},ot=[],at=[],st=[],lt=[],ut={},ht={},ct={},dt={},ft=new Date-0,pt=new Date-0,gt="_echarts_instance_",mt={version:"3.7.1",dependencies:{zrender:"3.6.1"}};mt.init=function(t,e,n){var i=mt.getInstanceByDom(t);if(i)return i;var r=new o(t,e,n);return r.id="ec_"+ft++,ct[r.id]=r,t.setAttribute?t.setAttribute(gt,r.id):t[gt]=r.id,w(r),r},mt.connect=function(t){if(N.isArray(t)){var e=t;t=null,N.each(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+pt++,N.each(e,function(e){e.group=t})}return dt[t]=!0,t},mt.disConnect=function(t){dt[t]=!1},mt.disconnect=mt.disConnect,mt.dispose=function(t){"string"==typeof t?t=ct[t]:t instanceof o||(t=mt.getInstanceByDom(t)),t instanceof o&&!t.isDisposed()&&t.dispose()},mt.getInstanceByDom=function(t){var e;return e=t.getAttribute?t.getAttribute(gt):t[gt],ct[e]},mt.getInstanceById=function(t){return ct[t]},mt.registerTheme=function(t,e){ut[t]=e},mt.registerPreprocessor=function(t){at.push(t)},mt.registerProcessor=function(t,e){"function"==typeof t&&(e=t,t=W),ot.push({prio:t,func:e})},mt.registerPostUpdate=function(t){st.push(t)},mt.registerAction=function(t,e,n){"function"==typeof e&&(n=e,e="");var i=N.isObject(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,N.assert(J.test(i)&&J.test(e)),it[i]||(it[i]={action:n,actionInfo:t}),rt[e]=i},mt.registerCoordinateSystem=function(t,e){I.register(t,e)},mt.getCoordinateSystemDimensions=function(t){var e=I.get(t);if(e)return e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice()},mt.registerLayout=function(t,e){"function"==typeof t&&(e=t,t=q),lt.push({prio:t,func:e,isLayout:!0})},mt.registerVisual=function(t,e){"function"==typeof t&&(e=t,t=X),lt.push({prio:t,func:e})},mt.registerLoading=function(t,e){ht[t]=e},mt.extendComponentModel=function(t){return P.extend(t)},mt.extendComponentView=function(t){return k.extend(t)},mt.extendSeriesModel=function(t){return D.extend(t)},mt.extendChartView=function(t){return L.extend(t)},mt.setCanvasCreator=function(t){N.createCanvas=t},mt.registerVisual(j,n(157)),mt.registerPreprocessor(C),mt.registerLoading("default",n(142)),mt.registerAction({type:"highlight",event:"highlight",update:"highlight"},N.noop),mt.registerAction({type:"downplay",event:"downplay",update:"downplay"},N.noop),mt.zrender=R,mt.List=n(14),mt.Model=n(11),mt.Axis=n(33),mt.graphic=n(3),mt.number=n(4),mt.format=n(7),mt.throttle=E.throttle,mt.matrix=n(19),mt.vector=n(6),mt.color=n(22),mt.util={},G(["map","each","filter","indexOf","inherits","reduce","filter","bind","curry","isArray","isString","isObject","isFunction","extend","defaults","clone","merge"],function(t){mt.util[t]=N[t]}),mt.helper=n(141),mt.PRIORITY={PROCESSOR:{FILTER:W,STATISTIC:Z},VISUAL:{LAYOUT:q,GLOBAL:j,CHART:X,COMPONENT:Y,BRUSH:U}},t.exports=mt},function(t,e,n){"use strict";function i(t){return null!=t&&"none"!=t}function r(t){return"string"==typeof t?T.lift(t,-.1):t}function o(t){if(t.__hoverStlDirty){var e=t.style.stroke,n=t.style.fill,o=t.__hoverStl;o.fill=o.fill||(i(n)?r(n):null),o.stroke=o.stroke||(i(e)?r(e):null);var a={};for(var s in o)null!=o[s]&&(a[s]=t.style[s]);t.__normalStl=a,t.__hoverStlDirty=!1}}function a(t){if(!t.__isHover){if(o(t),t.useHoverLayer)t.__zr&&t.__zr.addHover(t,t.__hoverStl);else{var e=t.style,n=e.insideRollbackOpt;n&&_(e),e.extendFrom(t.__hoverStl),n&&(y(e,e.insideOriginalTextPosition,n),null==e.textFill&&(e.textFill=n.autoColor)),t.dirty(!1),t.z2+=1}t.__isHover=!0}}function s(t){if(t.__isHover){var e=t.__normalStl;t.useHoverLayer?t.__zr&&t.__zr.removeHover(t):(e&&t.setStyle(e),t.z2-=1),t.__isHover=!1}}function l(t){"group"===t.type?t.traverse(function(t){"group"!==t.type&&a(t)}):a(t)}function u(t){"group"===t.type?t.traverse(function(t){"group"!==t.type&&s(t)}):s(t)}function h(t,e){t.__hoverStl=t.hoverStyle||e||{},t.__hoverStlDirty=!0,t.__isHover&&o(t)}function c(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasis&&l(this)}function d(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasis&&u(this)}function f(){this.__isEmphasis=!0,l(this)}function p(){this.__isEmphasis=!1,u(this)}function g(t,e,n,i){if(n=n||O,n.isRectText){var r=e.getShallow("position")||(i?null:"inside");"outside"===r&&(r="top"),t.textPosition=r,t.textOffset=e.getShallow("offset");var o=e.getShallow("rotate");null!=o&&(o*=Math.PI/180),t.textRotation=o,t.textDistance=w.retrieve2(e.getShallow("distance"),i?null:5)}var a,s=e.ecModel,l=s&&s.option.textStyle,u=m(e);if(u){a={};for(var h in u)if(u.hasOwnProperty(h)){var c=e.getModel(["rich",h]);v(a[h]={},c,l,n,i)}}return t.rich=a,v(t,e,l,n,i,!0),n.forceRich&&!n.textStyle&&(n.textStyle={}),t}function m(t){for(var e;t&&t!==t.ecModel;){var n=(t.option||O).rich;if(n){e=e||{};for(var i in n)n.hasOwnProperty(i)&&(e[i]=1)}t=t.parentModel}return e}function v(t,e,n,i,r,o){if(n=!r&&n||O,t.textFill=x(e.getShallow("color"),i)||n.color,t.textStroke=x(e.getShallow("textBorderColor"),i)||n.textBorderColor,t.textLineWidth=w.retrieve2(e.getShallow("textBorderWidth"),n.textBorderWidth),!r){if(o){var a=t.textPosition;t.insideRollback=y(t,a,i),t.insideOriginalTextPosition=a,t.insideRollbackOpt=i}null==t.textFill&&(t.textFill=i.autoColor)}t.fontStyle=e.getShallow("fontStyle")||n.fontStyle,t.fontWeight=e.getShallow("fontWeight")||n.fontWeight,t.fontSize=e.getShallow("fontSize")||n.fontSize,t.fontFamily=e.getShallow("fontFamily")||n.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),o&&i.disableBox||(t.textBackgroundColor=x(e.getShallow("backgroundColor"),i),t.textPadding=e.getShallow("padding"),t.textBorderColor=x(e.getShallow("borderColor"),i),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||n.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||n.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||n.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||n.textShadowOffsetY}function x(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function y(t,e,n){var i,r=n.useInsideStyle;return null==t.textFill&&r!==!1&&(r===!0||n.isRectText&&e&&"string"==typeof e&&e.indexOf("inside")>=0)&&(i={textFill:null,textStroke:t.textStroke,textLineWidth:t.textLineWidth},t.textFill="#fff",null==t.textStroke&&(t.textStroke=n.autoColor,null==t.textLineWidth&&(t.textLineWidth=2))),i}function _(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textLineWidth=e.textLineWidth)}function b(t,e,n,i,r,o){"function"==typeof r&&(o=r,r=null);var a=i&&i.isAnimationEnabled();if(a){var s=t?"Update":"",l=i.getShallow("animationDuration"+s),u=i.getShallow("animationEasing"+s),h=i.getShallow("animationDelay"+s);"function"==typeof h&&(h=h(r,i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null)),"function"==typeof l&&(l=l(r)),l>0?e.animateTo(n,l,h||0,u,o,!!o):(e.stopAnimation(),e.attr(n),o&&o())}else e.stopAnimation(),e.attr(n),o&&o()}var w=n(1),S=n(185),M=n(8),T=n(22),I=n(19),A=n(6),C=n(60),P=n(12),D=Math.round,k=Math.max,L=Math.min,O={},z={};z.Group=n(36),z.Image=n(55),z.Text=n(90),z.Circle=n(176),z.Sector=n(182),z.Ring=n(181),z.Polygon=n(178),z.Polyline=n(179),z.Rect=n(180),z.Line=n(177),z.BezierCurve=n(175),z.Arc=n(174),z.CompoundPath=n(170),z.LinearGradient=n(104),z.RadialGradient=n(171),z.BoundingRect=P,z.extendShape=function(t){return M.extend(t)},z.extendPath=function(t,e){return S.extendFromString(t,e)},z.makePath=function(t,e,n,i){var r=S.createFromString(t,e),o=r.getBoundingRect();if(n){var a=o.width/o.height;if("center"===i){var s,l=n.height*a;l<=n.width?s=n.height:(l=n.width,s=l/a);var u=n.x+n.width/2,h=n.y+n.height/2;n.x=u-l/2,n.y=h-s/2,n.width=l,n.height=s}z.resizePath(r,n)}return r},z.mergePath=S.mergePath,z.resizePath=function(t,e){if(t.applyTransform){var n=t.getBoundingRect(),i=n.calculateTransform(e);t.applyTransform(i)}},z.subPixelOptimizeLine=function(t){var e=t.shape,n=t.style.lineWidth;return D(2*e.x1)===D(2*e.x2)&&(e.x1=e.x2=E(e.x1,n,!0)),D(2*e.y1)===D(2*e.y2)&&(e.y1=e.y2=E(e.y1,n,!0)),t},z.subPixelOptimizeRect=function(t){var e=t.shape,n=t.style.lineWidth,i=e.x,r=e.y,o=e.width,a=e.height;return e.x=E(e.x,n,!0),e.y=E(e.y,n,!0),e.width=Math.max(E(i+o,n,!1)-e.x,0===o?0:1),e.height=Math.max(E(r+a,n,!1)-e.y,0===a?0:1),t};var E=z.subPixelOptimize=function(t,e,n){var i=D(2*t);return(i+D(e))%2===0?i/2:(i+(n?1:-1))/2};z.setHoverStyle=function(t,e,n){t.__hoverSilentOnTouch=n&&n.hoverSilentOnTouch,"group"===t.type?t.traverse(function(t){"group"!==t.type&&h(t,e)}):h(t,e),t.on("mouseover",c).on("mouseout",d),t.on("emphasis",f).on("normal",p)},z.setLabelStyle=function(t,e,n,i,r,o,a){r=r||O;var s=r.labelFetcher,l=r.labelDataIndex,u=r.labelDimIndex,h=n.getShallow("show"),c=i.getShallow("show"),d=h||c?w.retrieve2(s?s.getFormattedLabel(l,"normal",null,u):null,r.defaultText):null,f=h?d:null,p=c?w.retrieve2(s?s.getFormattedLabel(l,"emphasis",null,u):null,d):null;null==f&&null==p||(R(t,n,o,r),R(e,i,a,r,!0)),t.text=f,e.text=p};var R=z.setTextStyle=function(t,e,n,i,r){return g(t,e,i,r),n&&w.extend(t,n),t.host&&t.host.dirty&&t.host.dirty(!1),t};z.setText=function(t,e,n){var i,r={isRectText:!0};n===!1?i=!0:r.autoColor=n,g(t,e,r,i),t.host&&t.host.dirty&&t.host.dirty(!1)},z.getFont=function(t,e){var n=e||e.getModel("textStyle");return[t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" ")},z.updateProps=function(t,e,n,i,r){b(!0,t,e,n,i,r)},z.initProps=function(t,e,n,i,r){b(!1,t,e,n,i,r)},z.getTransform=function(t,e){for(var n=I.identity([]);t&&t!==e;)I.mul(n,t.getLocalTransform(),n),t=t.parent;return n},z.applyTransform=function(t,e,n){return e&&!w.isArrayLike(e)&&(e=C.getLocalTransform(e)),n&&(e=I.invert([],e)),A.applyTransform([],t,e)},z.transformDirection=function(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),o=["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0];return o=z.applyTransform(o,e,n),Math.abs(o[0])>Math.abs(o[1])?o[0]>0?"right":"left":o[1]>0?"bottom":"top"},z.groupTransition=function(t,e,n,i){function r(t){var e={};return t.traverse(function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}function o(t){var e={position:A.clone(t.position),rotation:t.rotation};return t.shape&&(e.shape=w.extend({},t.shape)),e}if(t&&e){var a=r(t);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=a[t.anid];if(e){var i=o(t);t.attr(o(e)),z.updateProps(t,i,n,t.dataIndex)}}})}},z.clipPointsByRect=function(t,e){return w.map(t,function(t){var n=t[0];n=k(n,e.x),n=L(n,e.x+e.width);var i=t[1];return i=k(i,e.y),i=L(i,e.y+e.height),[n,i]})},z.clipRectByRect=function(t,e){var n=k(t.x,e.x),i=L(t.x+t.width,e.x+e.width),r=k(t.y,e.y),o=L(t.y+t.height,e.y+e.height);if(i>=n&&o>=r)return{x:n,y:r,width:i-n,height:o-r}},z.createIcon=function(t,e,n){e=w.extend({rectHover:!0},e);var i=e.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),w.defaults(i,n),new z.Image(e)):z.makePath(t.replace("path://",""),e,n,"center")},t.exports=z},function(t,e,n){function i(t){return t.replace(/^\s+/,"").replace(/\s+$/,"")}function r(t){return Math.floor(Math.log(t)/Math.LN10)}var o=n(1),a={},s=1e-4;a.linearMap=function(t,e,n,i){var r=e[1]-e[0],o=n[1]-n[0];if(0===r)return 0===o?n[0]:(n[0]+n[1])/2;if(i)if(r>0){if(t<=e[0])return n[0];if(t>=e[1])return n[1]}else{if(t>=e[0])return n[0];if(t<=e[1])return n[1]}else{if(t===e[0])return n[0];if(t===e[1])return n[1]}return(t-e[0])/r*o+n[0]},a.parsePercent=function(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?i(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t},a.round=function(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t},a.asc=function(t){return t.sort(function(t,e){return t-e}),t},a.getPrecision=function(t){if(t=+t,isNaN(t))return 0;for(var e=1,n=0;Math.round(t*e)/e!==t;)e*=10,n++;return n},a.getPrecisionSafe=function(t){var e=t.toString(),n=e.indexOf("e");if(n>0){var i=+e.slice(n+1);return i<0?-i:0}var r=e.indexOf(".");return r<0?0:e.length-1-r},a.getPixelPrecision=function(t,e){var n=Math.log,i=Math.LN10,r=Math.floor(n(t[1]-t[0])/i),o=Math.round(n(Math.abs(e[1]-e[0]))/i),a=Math.min(Math.max(-r+o,0),20);return isFinite(a)?a:20},a.getPercentWithPrecision=function(t,e,n){if(!t[e])return 0;var i=o.reduce(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===i)return 0;for(var r=Math.pow(10,n),a=o.map(t,function(t){return(isNaN(t)?0:t)/i*r*100}),s=100*r,l=o.map(a,function(t){return Math.floor(t)}),u=o.reduce(l,function(t,e){return t+e},0),h=o.map(a,function(t,e){return t-l[e]});u<s;){for(var c=Number.NEGATIVE_INFINITY,d=null,f=0,p=h.length;f<p;++f)h[f]>c&&(c=h[f],d=f);++l[d],h[d]=0,++u}return l[e]/r},a.MAX_SAFE_INTEGER=9007199254740991,a.remRadian=function(t){var e=2*Math.PI;return(t%e+e)%e},a.isRadianAroundZero=function(t){return t>-s&&t<s};var l=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;a.getTimezoneOffset=function(){return(new Date).getTimezoneOffset()},a.parseDate=function(t){if(t instanceof Date)return t;if("string"==typeof t){var e=l.exec(t);if(!e)return new Date(NaN);var n=a.getTimezoneOffset(),i=e[8]?"Z"===e[8].toUpperCase()?n:60*+e[8].slice(0,3)+n:0;return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0)-i,+e[6]||0,+e[7]||0)}return null==t?new Date(NaN):new Date(Math.round(t))},a.quantity=function(t){return Math.pow(10,r(t))},a.nice=function(t,e){var n,i=r(t),o=Math.pow(10,i),a=t/o;return n=e?a<1.5?1:a<2.5?2:a<4?3:a<7?5:10:a<1?1:a<2?2:a<3?3:a<5?5:10,t=n*o,i>=-20?+t.toFixed(i<0?-i:0):t},a.reformIntervals=function(t){function e(t,n,i){return t.interval[i]<n.interval[i]||t.interval[i]===n.interval[i]&&(t.close[i]-n.close[i]===(i?-1:1)||!i&&e(t,n,1))}t.sort(function(t,n){return e(t,n,0)?-1:1});for(var n=-(1/0),i=1,r=0;r<t.length;){for(var o=t[r].interval,a=t[r].close,s=0;s<2;s++)o[s]<=n&&(o[s]=n,a[s]=s?1:1-i),n=o[s],i=a[s];o[0]===o[1]&&a[0]*a[1]!==1?t.splice(r,1):r++}return t},a.isNumeric=function(t){return t-parseFloat(t)>=0},t.exports=a},function(t,e,n){function i(t,e){return t&&t.hasOwnProperty(e)}var r=n(7),o=n(4),a=n(11),s=n(1),l=s.each,u=s.isObject,h={};h.normalizeToArray=function(t){return t instanceof Array?t:null==t?[]:[t]},h.defaultEmphasis=function(t,e){if(t)for(var n=t.emphasis=t.emphasis||{},i=t.normal=t.normal||{},r=0,o=e.length;r<o;r++){var a=e[r];!n.hasOwnProperty(a)&&i.hasOwnProperty(a)&&(n[a]=i[a])}},h.TEXT_STYLE_OPTIONS=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"],h.getDataItemValue=function(t){return t&&(null==t.value?t:t.value)},h.isDataItemOption=function(t){return u(t)&&!(t instanceof Array)},h.converDataValue=function(t,e){var n=e&&e.type;return"ordinal"===n?t:("time"===n&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+o.parseDate(t)),null==t||""===t?NaN:+t)},h.createDataFormatModel=function(t,e){var n=new a;return s.mixin(n,h.dataFormatMixin),n.seriesIndex=e.seriesIndex,n.name=e.name||"",n.mainType=e.mainType,n.subType=e.subType,n.getData=function(){return t},n},h.dataFormatMixin={getDataParams:function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),o=n.getRawIndex(t),a=n.getName(t,!0),s=n.getRawDataItem(t),l=n.getItemVisual(t,"color");return{componentType:this.mainType,componentSubType:this.subType,seriesType:"series"===this.mainType?this.subType:null,seriesIndex:this.seriesIndex,seriesId:this.id,seriesName:this.name,name:a,dataIndex:o,data:s,dataType:e,value:i,color:l,marker:r.getTooltipMarker(l),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,n,i,o){e=e||"normal";var a=this.getData(n),s=a.getItemModel(t),l=this.getDataParams(t,n);null!=i&&l.value instanceof Array&&(l.value=l.value[i]);var u=s.get([o||"label",e,"formatter"]);return"function"==typeof u?(l.status=e,u(l)):"string"==typeof u?r.formatTpl(u,l):void 0},getRawValue:function(t,e){var n=this.getData(e),i=n.getRawDataItem(t);if(null!=i)return!u(i)||i instanceof Array?i:i.value},formatTooltip:s.noop},h.mappingToExists=function(t,e){e=(e||[]).slice();var n=s.map(t||[],function(t,e){return{exist:t}});return l(e,function(t,i){if(u(t)){for(var r=0;r<n.length;r++)if(!n[r].option&&null!=t.id&&n[r].exist.id===t.id+"")return n[r].option=t,void(e[i]=null);for(var r=0;r<n.length;r++){var o=n[r].exist;if(!(n[r].option||null!=o.id&&null!=t.id||null==t.name||h.isIdInner(t)||h.isIdInner(o)||o.name!==t.name+""))return n[r].option=t,void(e[i]=null)}}}),l(e,function(t,e){if(u(t)){for(var i=0;i<n.length;i++){var r=n[i].exist;if(!n[i].option&&!h.isIdInner(r)&&null==t.id){n[i].option=t;break}}i>=n.length&&n.push({option:t})}}),n},h.makeIdAndName=function(t){var e=s.createHashMap();l(t,function(t,n){var i=t.exist;i&&e.set(i.id,t)}),l(t,function(t,n){var i=t.option;s.assert(!i||null==i.id||!e.get(i.id)||e.get(i.id)===t,"id duplicates: "+(i&&i.id)),i&&null!=i.id&&e.set(i.id,t),!t.keyInfo&&(t.keyInfo={})}),l(t,function(t,n){var i=t.exist,r=t.option,o=t.keyInfo;if(u(r)){if(o.name=null!=r.name?r.name+"":i?i.name:"\0-",i)o.id=i.id;else if(null!=r.id)o.id=r.id+"";else{var a=0;do o.id="\0"+o.name+"\0"+a++;while(e.get(o.id))}e.set(o.id,t)}})},h.isIdInner=function(t){return u(t)&&t.id&&0===(t.id+"").indexOf("\0_ec_\0")},h.compressBatches=function(t,e){function n(t,e,n){for(var i=0,r=t.length;i<r;i++)for(var o=t[i].seriesId,a=h.normalizeToArray(t[i].dataIndex),s=n&&n[o],l=0,u=a.length;l<u;l++){var c=a[l];s&&s[c]?s[c]=null:(e[o]||(e[o]={}))[c]=1}}function i(t,e){var n=[];for(var r in t)if(t.hasOwnProperty(r)&&null!=t[r])if(e)n.push(+r);else{var o=i(t[r],!0);o.length&&n.push({seriesId:r,dataIndex:o})}return n}var r={},o={};return n(t||[],r),n(e||[],o,r),[i(r),i(o)]},h.queryDataIndex=function(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?s.isArray(e.dataIndex)?s.map(e.dataIndex,function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e.dataIndex):null!=e.name?s.isArray(e.name)?s.map(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0},h.makeGetter=function(){var t=0;return function(){var e="\0__ec_prop_getter_"+t++;return function(t){return t[e]||(t[e]={})}}}(),h.parseFinder=function(t,e,n){if(s.isString(e)){var r={};r[e+"Index"]=0,e=r}var o=n&&n.defaultMainType;!o||i(e,o+"Index")||i(e,o+"Id")||i(e,o+"Name")||(e[o+"Index"]=0);var a={};return l(e,function(i,r){var i=e[r];if("dataIndex"===r||"dataIndexInside"===r)return void(a[r]=i);var o=r.match(/^(\w+)(Index|Id|Name)$/)||[],l=o[1],u=(o[2]||"").toLowerCase();if(!(!l||!u||null==i||"index"===u&&"none"===i||n&&n.includeMainTypes&&s.indexOf(n.includeMainTypes,l)<0)){var h={mainType:l};"index"===u&&"all"===i||(h[u]=i);var c=t.queryComponents(h);a[l+"Models"]=c,a[l+"Model"]=c[0]}}),a},h.dataDimToCoordDim=function(t,e){var n=t.dimensions;e=t.getDimension(e);for(var i=0;i<n.length;i++){var r=t.getDimensionInfo(n[i]);if(r.name===e)return r.coordDim}},h.coordDimToDataDim=function(t,e){var n=[];return l(t.dimensions,function(i){var r=t.getDimensionInfo(i);r.coordDim===e&&(n[r.coordDimIndex]=r.name)}),n},h.otherDimToDataDim=function(t,e){var n=[];return l(t.dimensions,function(i){var r=t.getDimensionInfo(i),o=r.otherDims,a=o[e];null!=a&&a!==!1&&(n[a]=r.name)}),n},t.exports=h},function(t,e){var n="undefined"==typeof Float32Array?Array:Float32Array,i={create:function(t,e){var i=new n(2);return null==t&&(t=0),null==e&&(e=0),i[0]=t,i[1]=e,i},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},clone:function(t){var e=new n(2);return e[0]=t[0],e[1]=t[1],e},set:function(t,e,n){return t[0]=e,t[1]=n,t},add:function(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t},scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},sub:function(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t},len:function(t){return Math.sqrt(this.lenSquare(t))},lenSquare:function(t){return t[0]*t[0]+t[1]*t[1]},mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:function(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t},normalize:function(t,e){var n=i.len(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t},distance:function(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))},distanceSquare:function(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:function(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t},applyTransform:function(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t},min:function(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t},max:function(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}};i.length=i.len,i.lengthSquare=i.lenSquare,i.dist=i.distance,i.distSquare=i.distanceSquare,t.exports=i},function(t,e,n){var i=n(1),r=n(4),o=n(16),a={};a.addCommas=function(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:""))},a.toCamelCase=function(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t},a.normalizeCssArray=i.normalizeCssArray;var s=a.encodeHTML=function(t){return String(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;")},l=["a","b","c","d","e","f","g"],u=function(t,e){return"{"+t+(null==e?"":e)+"}"};a.formatTpl=function(t,e,n){i.isArray(e)||(e=[e]);var r=e.length;if(!r)return"";for(var o=e[0].$vars||[],a=0;a<o.length;a++){var h=l[a],c=u(h,0);t=t.replace(u(h),n?s(c):c)}for(var d=0;d<r;d++)for(var f=0;f<o.length;f++){var c=e[d][o[f]];t=t.replace(u(l[f],d),n?s(c):c)}return t},a.formatTplSimple=function(t,e,n){return i.each(e,function(e,i){t=t.replace("{"+i+"}",n?s(e):e)}),t},a.getTooltipMarker=function(t,e){return t?'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'+a.encodeHTML(t)+";"+(e||"")+'"></span>':""};var h=function(t){return t<10?"0"+t:t};a.formatTime=function(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=r.parseDate(e),o=n?"UTC":"",a=i["get"+o+"FullYear"](),s=i["get"+o+"Month"]()+1,l=i["get"+o+"Date"](),u=i["get"+o+"Hours"](),c=i["get"+o+"Minutes"](),d=i["get"+o+"Seconds"]();return t=t.replace("MM",h(s)).replace("M",s).replace("yyyy",a).replace("yy",a%100).replace("dd",h(l)).replace("d",l).replace("hh",h(u)).replace("h",u).replace("mm",h(c)).replace("m",c).replace("ss",h(d)).replace("s",d)},a.capitalFirst=function(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t},a.truncateText=o.truncateText,a.getTextRect=o.getBoundingRect,t.exports=a},function(t,e,n){function i(t){r.call(this,t),this.path=null}var r=n(38),o=n(1),a=n(27),s=n(167),l=n(74),u=l.prototype.getCanvasPattern,h=Math.abs,c=new a(!0);i.prototype={constructor:i,type:"path",__dirtyPath:!0,strokeContainThreshold:5,brush:function(t,e){var n=this.style,i=this.path||c,r=n.hasStroke(),o=n.hasFill(),a=n.fill,s=n.stroke,l=o&&!!a.colorStops,h=r&&!!s.colorStops,d=o&&!!a.image,f=r&&!!s.image;if(n.bind(t,this,e),this.setTransform(t),this.__dirty){var p;l&&(p=p||this.getBoundingRect(),this._fillGradient=n.getGradient(t,a,p)),h&&(p=p||this.getBoundingRect(),this._strokeGradient=n.getGradient(t,s,p))}l?t.fillStyle=this._fillGradient:d&&(t.fillStyle=u.call(a,t)),h?t.strokeStyle=this._strokeGradient:f&&(t.strokeStyle=u.call(s,t));var g=n.lineDash,m=n.lineDashOffset,v=!!t.setLineDash,x=this.getGlobalScale();i.setScale(x[0],x[1]),this.__dirtyPath||g&&!v&&r?(i.beginPath(t),g&&!v&&(i.setLineDash(g),i.setLineDashOffset(m)),this.buildPath(i,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),o&&i.fill(t),g&&v&&(t.setLineDash(g),t.lineDashOffset=m),r&&i.stroke(t),g&&v&&t.setLineDash([]),this.restoreTransform(t),null!=n.text&&this.drawRectText(t,this.getBoundingRect())},buildPath:function(t,e,n){},createPathProxy:function(){this.path=new a},getBoundingRect:function(){var t=this._rect,e=this.style,n=!t;if(n){var i=this.path;i||(i=this.path=new a),this.__dirtyPath&&(i.beginPath(),this.buildPath(i,this.shape,!1)),t=i.getBoundingRect()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||n){r.copy(t);var o=e.lineWidth,s=e.strokeNoScale?this.getLineScale():1;
e.hasFill()||(o=Math.max(o,this.strokeContainThreshold||4)),s>1e-10&&(r.width+=o/s,r.height+=o/s,r.x-=o/s/2,r.y-=o/s/2)}return r}return t},contain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var o=this.path.data;if(r.hasStroke()){var a=r.lineWidth,l=r.strokeNoScale?this.getLineScale():1;if(l>1e-10&&(r.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),s.containStroke(o,a/l,t,e)))return!0}if(r.hasFill())return s.contain(o,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):r.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var n=this.shape;if(n){if(o.isObject(t))for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);else n[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&h(t[0]-1)>1e-10&&h(t[3]-1)>1e-10?Math.sqrt(h(t[0]*t[3]-t[2]*t[1])):1}},i.extend=function(t){var e=function(e){i.call(this,e),t.style&&this.style.extendFrom(t.style,!1);var n=t.shape;if(n){this.shape=this.shape||{};var r=this.shape;for(var o in n)!r.hasOwnProperty(o)&&n.hasOwnProperty(o)&&(r[o]=n[o])}t.init&&t.init.call(this,e)};o.inherits(e,i);for(var n in t)"style"!==n&&"shape"!==n&&(e.prototype[n]=t[n]);return e},o.inherits(i,r),t.exports=i},function(t,e,n){"use strict";function i(t,e,n,i,r){var o=0,a=0;null==i&&(i=1/0),null==r&&(r=1/0);var s=0;e.eachChild(function(l,u){var h,c,d=l.position,f=l.getBoundingRect(),p=e.childAt(u+1),g=p&&p.getBoundingRect();if("horizontal"===t){var m=f.width+(g?-g.x+f.x:0);h=o+m,h>i||l.newline?(o=0,h=m,a+=s+n,s=f.height):s=Math.max(s,f.height)}else{var v=f.height+(g?-g.y+f.y:0);c=a+v,c>r||l.newline?(o+=s+n,a=0,c=v,s=f.width):s=Math.max(s,f.width)}l.newline||(d[0]=o,d[1]=a,"horizontal"===t?o=h+n:a=c+n)})}var r=n(1),o=n(12),a=n(4),s=n(7),l=a.parsePercent,u=r.each,h={},c=h.LOCATION_PARAMS=["left","right","top","bottom","width","height"],d=h.HV_NAMES=[["width","left","right"],["height","top","bottom"]];h.box=i,h.vbox=r.curry(i,"vertical"),h.hbox=r.curry(i,"horizontal"),h.getAvailableSize=function(t,e,n){var i=e.width,r=e.height,o=l(t.x,i),a=l(t.y,r),u=l(t.x2,i),h=l(t.y2,r);return(isNaN(o)||isNaN(parseFloat(t.x)))&&(o=0),(isNaN(u)||isNaN(parseFloat(t.x2)))&&(u=i),(isNaN(a)||isNaN(parseFloat(t.y)))&&(a=0),(isNaN(h)||isNaN(parseFloat(t.y2)))&&(h=r),n=s.normalizeCssArray(n||0),{width:Math.max(u-o-n[1]-n[3],0),height:Math.max(h-a-n[0]-n[2],0)}},h.getLayoutRect=function(t,e,n){n=s.normalizeCssArray(n||0);var i=e.width,r=e.height,a=l(t.left,i),u=l(t.top,r),h=l(t.right,i),c=l(t.bottom,r),d=l(t.width,i),f=l(t.height,r),p=n[2]+n[0],g=n[1]+n[3],m=t.aspect;switch(isNaN(d)&&(d=i-h-g-a),isNaN(f)&&(f=r-c-p-u),null!=m&&(isNaN(d)&&isNaN(f)&&(m>i/r?d=.8*i:f=.8*r),isNaN(d)&&(d=m*f),isNaN(f)&&(f=d/m)),isNaN(a)&&(a=i-h-d-g),isNaN(u)&&(u=r-c-f-p),t.left||t.right){case"center":a=i/2-d/2-n[3];break;case"right":a=i-d-g}switch(t.top||t.bottom){case"middle":case"center":u=r/2-f/2-n[0];break;case"bottom":u=r-f-p}a=a||0,u=u||0,isNaN(d)&&(d=i-g-a-(h||0)),isNaN(f)&&(f=r-p-u-(c||0));var v=new o(a+n[3],u+n[0],d,f);return v.margin=n,v},h.positionElement=function(t,e,n,i,a){var s=!a||!a.hv||a.hv[0],l=!a||!a.hv||a.hv[1],u=a&&a.boundingMode||"all";if(s||l){var c;if("raw"===u)c="group"===t.type?new o(0,0,+e.width||0,+e.height||0):t.getBoundingRect();else if(c=t.getBoundingRect(),t.needLocalTransform()){var d=t.getLocalTransform();c=c.clone(),c.applyTransform(d)}e=h.getLayoutRect(r.defaults({width:c.width,height:c.height},e),n,i);var f=t.position,p=s?e.x-c.x:0,g=l?e.y-c.y:0;t.attr("position","raw"===u?[p,g]:[f[0]+p,f[1]+g])}},h.sizeCalculable=function(t,e){return null!=t[d[e][0]]||null!=t[d[e][1]]&&null!=t[d[e][2]]},h.mergeLayoutParam=function(t,e,n){function i(n,i){var r={},s=0,h={},c=0,d=2;if(u(n,function(e){h[e]=t[e]}),u(n,function(t){o(e,t)&&(r[t]=h[t]=e[t]),a(r,t)&&s++,a(h,t)&&c++}),l[i])return a(e,n[1])?h[n[2]]=null:a(e,n[2])&&(h[n[1]]=null),h;if(c!==d&&s){if(s>=d)return r;for(var f=0;f<n.length;f++){var p=n[f];if(!o(r,p)&&o(t,p)){r[p]=t[p];break}}return r}return h}function o(t,e){return t.hasOwnProperty(e)}function a(t,e){return null!=t[e]&&"auto"!==t[e]}function s(t,e,n){u(t,function(t){e[t]=n[t]})}!r.isObject(n)&&(n={});var l=n.ignoreSize;!r.isArray(l)&&(l=[l,l]);var h=i(d[0],0),c=i(d[1],1);s(d[0],t,h),s(d[1],t,c)},h.getLayoutParams=function(t){return h.copyLayoutParams({},t)},h.copyLayoutParams=function(t,e){return e&&t&&u(c,function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t},t.exports=h},function(t,e){function n(t){var e={},n={},i=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge\/([\d.]+)/),a=/micromessenger/i.test(t);return i&&(n.firefox=!0,n.version=i[1]),r&&(n.ie=!0,n.version=r[1]),o&&(n.edge=!0,n.version=o[1]),a&&(n.weChat=!0),{browser:n,os:e,node:!1,canvasSupported:!!document.createElement("canvas").getContext,touchEventsSupported:"ontouchstart"in window&&!n.ie&&!n.edge,pointerEventsSupported:"onpointerdown"in window&&(n.edge||n.ie&&n.version>=11)}}var i={};i="undefined"==typeof navigator?{browser:{},os:{},node:!0,canvasSupported:!0}:n(navigator.userAgent),t.exports=i},function(t,e,n){function i(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}function r(t,e,n){for(var i=0;i<e.length&&(!e[i]||(t=t&&"object"==typeof t?t[e[i]]:null,null!=t));i++);return null==t&&n&&(t=n.get(e)),t}function o(t,e){var n=s.get(t,"getParent");return n?n.call(t,e):t.parentModel}var a=n(1),s=n(15),l=n(10);i.prototype={constructor:i,init:null,mergeOption:function(t){a.merge(this.option,t,!0)},get:function(t,e){return null==t?this.option:r(this.option,this.parsePath(t),!e&&o(this,t))},getShallow:function(t,e){var n=this.option,i=null==n?n:n[t],r=!e&&o(this,t);return null==i&&r&&(i=r.getShallow(t)),i},getModel:function(t,e){var n,a=null==t?this.option:r(this.option,t=this.parsePath(t));return e=e||(n=o(this,t))&&n.getModel(t),new i(a,e,this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){var t=this.constructor;return new t(a.clone(this.option))},setReadOnly:function(t){s.setReadOnly(this,t)},parsePath:function(t){return"string"==typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){s.set(this,"getParent",t)},isAnimationEnabled:function(){if(!l.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},s.enableClassExtend(i);var u=a.mixin;u(i,n(149)),u(i,n(146)),u(i,n(150)),u(i,n(148)),t.exports=i},function(t,e,n){"use strict";function i(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}var r=n(6),o=n(19),a=r.applyTransform,s=Math.min,l=Math.max;i.prototype={constructor:i,union:function(t){var e=s(t.x,this.x),n=s(t.y,this.y);this.width=l(t.x+t.width,this.x+this.width)-e,this.height=l(t.y+t.height,this.y+this.height)-n,this.x=e,this.y=n},applyTransform:function(){var t=[],e=[],n=[],i=[];return function(r){if(r){t[0]=n[0]=this.x,t[1]=i[1]=this.y,e[0]=i[0]=this.x+this.width,e[1]=n[1]=this.y+this.height,a(t,t,r),a(e,e,r),a(n,n,r),a(i,i,r),this.x=s(t[0],e[0],n[0],i[0]),this.y=s(t[1],e[1],n[1],i[1]);var o=l(t[0],e[0],n[0],i[0]),u=l(t[1],e[1],n[1],i[1]);this.width=o-this.x,this.height=u-this.y}}}(),calculateTransform:function(t){var e=this,n=t.width/e.width,i=t.height/e.height,r=o.create();return o.translate(r,r,[-e.x,-e.y]),o.scale(r,r,[n,i]),o.translate(r,r,[t.x,t.y]),r},intersect:function(t){if(!t)return!1;t instanceof i||(t=i.create(t));var e=this,n=e.x,r=e.x+e.width,o=e.y,a=e.y+e.height,s=t.x,l=t.x+t.width,u=t.y,h=t.y+t.height;return!(r<s||l<n||a<u||h<o)},contain:function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},clone:function(){return new i(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},i.create=function(t){return new i(t.x,t.y,t.width,t.height)},t.exports=i},function(t,e,n){function i(t){var e=[];return o.each(h.getClassesByMainType(t),function(t){a.apply(e,t.prototype.dependencies||[])}),o.map(e,function(t){return l.parseClassType(t).main})}var r=n(11),o=n(1),a=Array.prototype.push,s=n(50),l=n(15),u=n(9),h=r.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,n,i){r.call(this,t,e,n,i),this.uid=s.getUID("componentModel")},init:function(t,e,n,i){this.mergeDefaultAndTheme(t,n)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?u.getLayoutParams(t):{},r=e.getTheme();o.merge(t,r.get(this.mainType)),o.merge(t,this.getDefaultOption()),n&&u.mergeLayoutParam(t,i,n)},mergeOption:function(t,e){o.merge(this.option,t,!0);var n=this.layoutMode;n&&u.mergeLayoutParam(this.option,t,n)},optionUpdated:function(t,e){},getDefaultOption:function(){if(!l.hasOwn(this,"__defaultOption")){for(var t=[],e=this.constructor;e;){var n=e.prototype.defaultOption;n&&t.push(n),e=e.superClass}for(var i={},r=t.length-1;r>=0;r--)i=o.merge(i,t[r],!0);l.set(this,"__defaultOption",i)}return l.get(this,"__defaultOption")},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});l.enableClassManagement(h,{registerWhenExtend:!0}),s.enableSubTypeDefaulter(h),s.enableTopologicalTravel(h,i),o.mixin(h,n(147)),t.exports=h},function(t,e,n){(function(e){function i(t,e){p.each(v.concat(e.__wrappedMethods||[]),function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t.__wrappedMethods=e.__wrappedMethods}function r(t){this._array=t||[]}function o(t){return p.isArray(t)||(t=[t]),t}function a(t,e){var n=t.dimensions,r=new x(p.map(n,t.getDimensionInfo,t),t.hostModel);i(r,t);for(var o=r._storage={},a=t._storage,s=0;s<n.length;s++){var l=n[s],u=a[l];p.indexOf(e,l)>=0?o[l]=new u.constructor(a[l].length):o[l]=a[l]}return r}var s="undefined",l="undefined"==typeof window?e:window,u=typeof l.Float64Array===s?Array:l.Float64Array,h=typeof l.Int32Array===s?Array:l.Int32Array,c={float:u,int:h,ordinal:Array,number:Array,time:Array},d=n(11),f=n(44),p=n(1),g=n(5),m=p.isObject,v=["stackedOn","hasItemOption","_nameList","_idList","_rawData"];r.prototype.pure=!1,r.prototype.count=function(){return this._array.length},r.prototype.getItem=function(t){return this._array[t]};var x=function(t,e){t=t||["x","y"];for(var n={},i=[],r=0;r<t.length;r++){var o,a={};"string"==typeof t[r]?(o=t[r],a={name:o,coordDim:o,coordDimIndex:0,stackable:!1,type:"number"}):(a=t[r],o=a.name,a.type=a.type||"number",a.coordDim||(a.coordDim=o,a.coordDimIndex=0)),a.otherDims=a.otherDims||{},i.push(o),n[o]=a}this.dimensions=i,this._dimensionInfos=n,this.hostModel=e,this.dataType,this.indices=[],this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this.stackedOn=null,this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._rawData,this._extent},y=x.prototype;y.type="list",y.hasItemOption=!0,y.getDimension=function(t){return isNaN(t)||(t=this.dimensions[t]||t),t},y.getDimensionInfo=function(t){return p.clone(this._dimensionInfos[this.getDimension(t)])},y.initData=function(t,e,n){t=t||[];var i=p.isArray(t);i&&(t=new r(t)),this._rawData=t;var o,a=this._storage={},s=this.indices=[],l=this.dimensions,u=this._dimensionInfos,h=t.count(),d=[],f={};e=e||[];for(var m=0;m<l.length;m++){var v=u[l[m]];0===v.otherDims.itemName&&(o=m);var x=c[v.type];a[l[m]]=new x(h)}var y=this;n||(y.hasItemOption=!1),n=n||function(t,e,n,i){var r=g.getDataItemValue(t);return g.isDataItemOption(t)&&(y.hasItemOption=!0),g.converDataValue(r instanceof Array?r[i]:r,u[e])};for(var m=0;m<h;m++){for(var _=t.getItem(m),b=0;b<l.length;b++){var w=l[b],S=a[w];S[m]=n(_,w,m,b)}s.push(m)}for(var m=0;m<h;m++){var _=t.getItem(m);!e[m]&&_&&(null!=_.name?e[m]=_.name:null!=o&&(e[m]=a[l[o]][m]));var M=e[m]||"",T=_&&_.id;!T&&M&&(f[M]=f[M]||0,T=M,f[M]>0&&(T+="__ec__"+f[M]),f[M]++),T&&(d[m]=T)}this._nameList=e,this._idList=d},y.count=function(){return this.indices.length},y.get=function(t,e,n){var i=this._storage,r=this.indices[e];if(null==r||!i[t])return NaN;var o=i[t][r];if(n){var a=this._dimensionInfos[t];if(a&&a.stackable)for(var s=this.stackedOn;s;){var l=s.get(t,e);(o>=0&&l>0||o<=0&&l<0)&&(o+=l),s=s.stackedOn}}return o},y.getValues=function(t,e,n){var i=[];p.isArray(t)||(n=e,e=t,t=this.dimensions);for(var r=0,o=t.length;r<o;r++)i.push(this.get(t[r],e,n));return i},y.hasValue=function(t){for(var e=this.dimensions,n=this._dimensionInfos,i=0,r=e.length;i<r;i++)if("ordinal"!==n[e[i]].type&&isNaN(this.get(e[i],t)))return!1;return!0},y.getDataExtent=function(t,e,n){t=this.getDimension(t);var i=this._storage[t],r=this.getDimensionInfo(t);e=r&&r.stackable&&e;var o,a=(this._extent||(this._extent={}))[t+!!e];if(a)return a;if(i){for(var s=1/0,l=-(1/0),u=0,h=this.count();u<h;u++)o=this.get(t,u,e),n&&!n(o,t,u)||(o<s&&(s=o),o>l&&(l=o));return this._extent[t+!!e]=[s,l]}return[1/0,-(1/0)]},y.getSum=function(t,e){var n=this._storage[t],i=0;if(n)for(var r=0,o=this.count();r<o;r++){var a=this.get(t,r,e);isNaN(a)||(i+=a)}return i},y.indexOf=function(t,e){var n=this._storage,i=n[t],r=this.indices;if(i)for(var o=0,a=r.length;o<a;o++){var s=r[o];if(i[s]===e)return o}return-1},y.indexOfName=function(t){for(var e=this.indices,n=this._nameList,i=0,r=e.length;i<r;i++){var o=e[i];if(n[o]===t)return i}return-1},y.indexOfRawIndex=function(t){var e=this.indices,n=e[t];if(null!=n&&n===t)return t;for(var i=0,r=e.length-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=o+1;else{if(!(e[o]>t))return o;r=o-1}}return-1},y.indicesOfNearest=function(t,e,n,i){var r=this._storage,o=r[t],a=[];if(!o)return a;null==i&&(i=1/0);for(var s=Number.MAX_VALUE,l=-1,u=0,h=this.count();u<h;u++){var c=e-this.get(t,u,n),d=Math.abs(c);c<=i&&d<=s&&((d<s||c>=0&&l<0)&&(s=d,l=c,a.length=0),a.push(u))}return a},y.getRawIndex=function(t){var e=this.indices[t];return null==e?-1:e},y.getRawDataItem=function(t){return this._rawData.getItem(this.getRawIndex(t))},y.getName=function(t){return this._nameList[this.indices[t]]||""},y.getId=function(t){return this._idList[this.indices[t]]||this.getRawIndex(t)+""},y.each=function(t,e,n,i){"function"==typeof t&&(i=n,n=e,e=t,t=[]),t=p.map(o(t),this.getDimension,this);var r=[],a=t.length,s=this.indices;i=i||this;for(var l=0;l<s.length;l++)switch(a){case 0:e.call(i,l);break;case 1:e.call(i,this.get(t[0],l,n),l);break;case 2:e.call(i,this.get(t[0],l,n),this.get(t[1],l,n),l);break;default:for(var u=0;u<a;u++)r[u]=this.get(t[u],l,n);r[u]=l,e.apply(i,r)}},y.filterSelf=function(t,e,n,i){"function"==typeof t&&(i=n,n=e,e=t,t=[]),t=p.map(o(t),this.getDimension,this);var r=[],a=[],s=t.length,l=this.indices;i=i||this;for(var u=0;u<l.length;u++){var h;if(s)if(1===s)h=e.call(i,this.get(t[0],u,n),u);else{for(var c=0;c<s;c++)a[c]=this.get(t[c],u,n);a[c]=u,h=e.apply(i,a)}else h=e.call(i,u);h&&r.push(l[u])}return this.indices=r,this._extent={},this},y.mapArray=function(t,e,n,i){"function"==typeof t&&(i=n,n=e,e=t,t=[]);var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},n,i),r},y.map=function(t,e,n,i){t=p.map(o(t),this.getDimension,this);var r=a(this,t),s=r.indices=this.indices,l=r._storage,u=[];return this.each(t,function(){var n=arguments[arguments.length-1],i=e&&e.apply(this,arguments);if(null!=i){"number"==typeof i&&(u[0]=i,i=u);for(var r=0;r<i.length;r++){var o=t[r],a=l[o],h=s[n];a&&(a[h]=i[r])}}},n,i),r},y.downSample=function(t,e,n,i){for(var r=a(this,[t]),o=this._storage,s=r._storage,l=this.indices,u=r.indices=[],h=[],c=[],d=Math.floor(1/e),f=s[t],p=this.count(),g=0;g<o[t].length;g++)s[t][g]=o[t][g];for(var g=0;g<p;g+=d){d>p-g&&(d=p-g,h.length=d);for(var m=0;m<d;m++){var v=l[g+m];h[m]=f[v],c[m]=v}var x=n(h),v=c[i(h,x)||0];f[v]=x,u.push(v)}return r},y.getItemModel=function(t){var e=this.hostModel;return t=this.indices[t],new d(this._rawData.getItem(t),e,e&&e.ecModel)},y.diff=function(t){var e,n=this._idList,i=t&&t._idList,r="e\0\0";return new f(t?t.indices:[],this.indices,function(t){return null!=(e=i[t])?e:r+t},function(t){return null!=(e=n[t])?e:r+t})},y.getVisual=function(t){var e=this._visual;return e&&e[t]},y.setVisual=function(t,e){if(m(t))for(var n in t)t.hasOwnProperty(n)&&this.setVisual(n,t[n]);else this._visual=this._visual||{},this._visual[t]=e},y.setLayout=function(t,e){if(m(t))for(var n in t)t.hasOwnProperty(n)&&this.setLayout(n,t[n]);else this._layout[t]=e},y.getLayout=function(t){return this._layout[t]},y.getItemLayout=function(t){return this._itemLayouts[t]},y.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?p.extend(this._itemLayouts[t]||{},e):e},y.clearItemLayouts=function(){this._itemLayouts.length=0},y.getItemVisual=function(t,e,n){var i=this._itemVisuals[t],r=i&&i[e];return null!=r||n?r:this.getVisual(e)},y.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};if(this._itemVisuals[t]=i,m(e))for(var r in e)e.hasOwnProperty(r)&&(i[r]=e[r]);else i[e]=n},y.clearAllVisual=function(){this._visual={},this._itemVisuals=[]};var _=function(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType};y.setItemGraphicEl=function(t,e){var n=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=n&&n.seriesIndex,"group"===e.type&&e.traverse(_,e)),this._graphicEls[t]=e},y.getItemGraphicEl=function(t){return this._graphicEls[t]},y.eachItemGraphicEl=function(t,e){p.each(this._graphicEls,function(n,i){n&&t&&t.call(e,n,i)})},y.cloneShallow=function(){var t=p.map(this.dimensions,this.getDimensionInfo,this),e=new x(t,this.hostModel);return e._storage=this._storage,i(e,this),e.indices=this.indices.slice(),this._extent&&(e._extent=p.extend({},this._extent)),e},y.wrapMethod=function(t,e){var n=this[t];"function"==typeof n&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(p.slice(arguments)))})},y.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],y.CHANGABLE_METHODS=["filterSelf"],t.exports=x}).call(e,function(){return this}())},function(t,e,n){function i(t){a.assert(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}function r(t,e){var n=a.slice(arguments,2);return this.superClass.prototype[e].apply(t,n)}function o(t,e,n){return this.superClass.prototype[e].apply(t,n)}var a=n(1),s={},l=".",u="___EC__COMPONENT__CONTAINER___",h="\0ec_\0";s.set=function(t,e,n){return t[h+e]=n},s.get=function(t,e){return t[h+e]},s.hasOwn=function(t,e){return t.hasOwnProperty(h+e)};var c=s.parseClassType=function(t){var e={main:"",sub:""};return t&&(t=t.split(l),e.main=t[0]||"",e.sub=t[1]||""),e};s.enableClassExtend=function(t,e){t.$constructor=t,t.extend=function(t){var e=this,n=function(){t.$constructor?t.$constructor.apply(this,arguments):e.apply(this,arguments)};return a.extend(n.prototype,t),n.extend=this.extend,n.superCall=r,n.superApply=o,a.inherits(n,this),n.superClass=e,n}},s.enableClassManagement=function(t,e){function n(t){var e=r[t.main];return e&&e[u]||(e=r[t.main]={},e[u]=!0),e}e=e||{};var r={};if(t.registerClass=function(t,e){if(e)if(i(e),e=c(e),e.sub){if(e.sub!==u){var o=n(e);o[e.sub]=t}}else r[e.main]=t;return t},t.getClass=function(t,e,n){var i=r[t];if(i&&i[u]&&(i=e?i[e]:null),n&&!i)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return i},t.getClassesByMainType=function(t){t=c(t);var e=[],n=r[t.main];return n&&n[u]?a.each(n,function(t,n){n!==u&&e.push(t)}):e.push(n),e},t.hasClass=function(t){return t=c(t),!!r[t.main]},t.getAllClassMainTypes=function(){var t=[];return a.each(r,function(e,n){t.push(n)}),t},t.hasSubTypes=function(t){t=c(t);var e=r[t.main];return e&&e[u]},t.parseClassType=c,e.registerWhenExtend){var o=t.extend;o&&(t.extend=function(e){var n=o.call(this,e);return t.registerClass(n,e.type)})}return t},s.setReadOnly=function(t,e){},t.exports=s},function(t,e,n){function i(t,e){e=e||A;var n=t+":"+e;if(S[n])return S[n];for(var i=(t+"").split("\n"),r=0,o=0,a=i.length;o<a;o++)r=Math.max(D.measureText(i[o],e).width,r);return M>T&&(M=0,S={}),M++,S[n]=r,r}function r(t,e,n,i,r,s,l){return s?a(t,e,n,i,r,s,l):o(t,e,n,i,r,l)}function o(t,e,n,r,o,a){var u=m(t,e,o,a),h=i(t,e);o&&(h+=o[1]+o[3]);var c=u.outerHeight,d=s(0,h,n),f=l(0,c,r),p=new b(d,f,h,c);return p.lineHeight=u.lineHeight,p}function a(t,e,n,i,r,o,a){var u=v(t,{rich:o,truncate:a,font:e,textAlign:n,textPadding:r}),h=u.outerWidth,c=u.outerHeight,d=s(0,h,n),f=l(0,c,i);return new b(d,f,h,c)}function s(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function l(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function u(t,e,n){var i=e.x,r=e.y,o=e.height,a=e.width,s=o/2,l="left",u="top";switch(t){case"left":i-=n,r+=s,l="right",u="middle";break;case"right":i+=n+a,r+=s,u="middle";break;case"top":i+=a/2,r-=n,l="center",u="bottom";break;case"bottom":i+=a/2,r+=o+n,l="center";break;case"inside":i+=a/2,r+=s,l="center",u="middle";break;case"insideLeft":i+=n,r+=s,u="middle";break;case"insideRight":i+=a-n,r+=s,l="right",u="middle";break;case"insideTop":i+=a/2,r+=n,l="center";break;case"insideBottom":i+=a/2,r+=o-n,l="center",u="bottom";break;case"insideTopLeft":i+=n,r+=n;break;case"insideTopRight":i+=a-n,r+=n,l="right";break;case"insideBottomLeft":i+=n,r+=o-n,u="bottom";break;case"insideBottomRight":i+=a-n,r+=o-n,l="right",u="bottom"}return{x:i,y:r,textAlign:l,textVerticalAlign:u}}function h(t,e,n,i,r){if(!e)return"";var o=(t+"").split("\n");r=c(e,n,i,r);for(var a=0,s=o.length;a<s;a++)o[a]=d(o[a],r);return o.join("\n")}function c(t,e,n,r){r=_.extend({},r),r.font=e;var n=C(n,"...");r.maxIterations=C(r.maxIterations,2);var o=r.minChar=C(r.minChar,0);r.cnCharWidth=i("国",e);var a=r.ascCharWidth=i("a",e);r.placeholder=C(r.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;l<o&&s>=a;l++)s-=a;var u=i(n);return u>s&&(n="",u=0),s=t-u,r.ellipsis=n,r.ellipsisWidth=u,r.contentWidth=s,r.containerWidth=t,r}function d(t,e){var n=e.containerWidth,r=e.font,o=e.contentWidth;if(!n)return"";var a=i(t,r);if(a<=n)return t;for(var s=0;;s++){if(a<=o||s>=e.maxIterations){t+=e.ellipsis;break}var l=0===s?f(t,o,e.ascCharWidth,e.cnCharWidth):a>0?Math.floor(t.length*o/a):0;t=t.substr(0,l),a=i(t,r)}return""===t&&(t=e.placeholder),t}function f(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}function p(t){return i("国",t)}function g(t,e){var n=_.getContext();return n.font=e||A,n.measureText(t)}function m(t,e,n,i){null!=t&&(t+="");var r=p(e),o=t?t.split("\n"):[],a=o.length*r,s=a;if(n&&(s+=n[0]+n[2]),t&&i){var l=i.outerHeight,u=i.outerWidth;if(null!=l&&s>l)t="",o=[];else if(null!=u)for(var h=c(u-(n?n[1]+n[3]:0),e,i.ellipsis,{minChar:i.minChar,placeholder:i.placeholder}),f=0,g=o.length;f<g;f++)o[f]=d(o[f],h)}return{lines:o,height:a,outerHeight:s,lineHeight:r}}function v(t,e){var n={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return n;for(var i,r=I.lastIndex=0;null!=(i=I.exec(t));){var o=i.index;o>r&&x(n,t.substring(r,o)),x(n,i[2],i[1]),r=I.lastIndex}r<t.length&&x(n,t.substring(r,t.length));var a=n.lines,s=0,l=0,u=[],c=e.textPadding,d=e.truncate,f=d&&d.outerWidth,p=d&&d.outerHeight;c&&(null!=f&&(f-=c[1]+c[3]),null!=p&&(p-=c[0]+c[2]));for(var g=0;g<a.length;g++){for(var m=a[g],v=0,y=0,_=0;_<m.tokens.length;_++){var b=m.tokens[_],S=b.styleName&&e.rich[b.styleName]||{},M=b.textPadding=S.textPadding,T=b.font=S.font||e.font,A=b.textHeight=C(S.textHeight,D.getLineHeight(T));if(M&&(A+=M[0]+M[2]),b.height=A,b.lineHeight=P(S.textLineHeight,e.textLineHeight,A),b.textAlign=S&&S.textAlign||e.textAlign,b.textVerticalAlign=S&&S.textVerticalAlign||"middle",null!=p&&s+b.lineHeight>p)return{lines:[],width:0,height:0};b.textWidth=D.getWidth(b.text,T);var k=S.textWidth,L=null==k||"auto"===k;if("string"==typeof k&&"%"===k.charAt(k.length-1))b.percentWidth=k,u.push(b),k=0;else{if(L){k=b.textWidth;var O=S.textBackgroundColor,z=O&&O.image;z&&(z=w.findExistImage(z),w.isImageReady(z)&&(k=Math.max(k,z.width*A/z.height)))}var E=M?M[1]+M[3]:0;k+=E;var R=null!=f?f-y:null;null!=R&&R<k&&(!L||R<E?(b.text="",b.textWidth=k=0):(b.text=h(b.text,R-E,T,d.ellipsis,{minChar:d.minChar}),b.textWidth=D.getWidth(b.text,T),k=b.textWidth+E))}y+=b.width=k,S&&(v=Math.max(v,b.lineHeight))}m.width=y,m.lineHeight=v,s+=v,l=Math.max(l,y)}n.outerWidth=n.width=C(e.textWidth,l),n.outerHeight=n.height=C(e.textHeight,s),c&&(n.outerWidth+=c[1]+c[3],n.outerHeight+=c[0]+c[2]);for(var g=0;g<u.length;g++){var b=u[g],N=b.percentWidth;b.width=parseInt(N,10)/100*l}return n}function x(t,e,n){for(var i=""===e,r=e.split("\n"),o=t.lines,a=0;a<r.length;a++){var s=r[a],l={styleName:n,text:s,isLineHolder:!s&&!i};if(a)o.push({tokens:[l]});else{var u=(o[o.length-1]||(o[0]={tokens:[]})).tokens,h=u.length;1===h&&u[0].isLineHolder?u[0]=l:(s||!h||i)&&u.push(l)}}}function y(t){return(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ")||t.textFont||t.font}var _=n(1),b=n(12),w=n(53),S={},M=0,T=5e3,I=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,A="12px sans-serif",C=_.retrieve2,P=_.retrieve3,D={getWidth:i,getBoundingRect:r,adjustTextPositionOnRect:u,truncateText:h,measureText:g,getLineHeight:p,parsePlainText:m,parseRichText:v,adjustTextX:s,adjustTextY:l,makeFont:y,DEFAULT_FONT:A};t.exports=D},function(t,e,n){"use strict";var i=n(1),r=n(7),o=n(15),a=n(5),s=n(13),l=n(64),u=n(10),h=n(9),c=o.set,d=o.get,f=r.encodeHTML,p=r.addCommas,g=s.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendDataProvider:null,visualColorAccessPath:"itemStyle.normal.color",layoutMode:null,init:function(t,e,n,i){this.seriesIndex=this.componentIndex,this.mergeDefaultAndTheme(t,n);var r=this.getInitialData(t,n);c(this,"dataBeforeProcessed",r),this.restoreData()},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,r=n?h.getLayoutParams(t):{},o=this.subType;s.hasClass(o)&&(o+="Series"),i.merge(t,e.getTheme().get(this.subType)),i.merge(t,this.getDefaultOption()),a.defaultEmphasis(t.label,["show"]),this.fillDataTextStyle(t.data),n&&h.mergeLayoutParam(t,r,n)},mergeOption:function(t,e){t=i.merge(this.option,t,!0),this.fillDataTextStyle(t.data);var n=this.layoutMode;n&&h.mergeLayoutParam(this.option,t,n);var r=this.getInitialData(t,e);r&&(c(this,"data",r),c(this,"dataBeforeProcessed",r.cloneShallow()))},fillDataTextStyle:function(t){if(t)for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&a.defaultEmphasis(t[n].label,e)},getInitialData:function(){},getData:function(t){var e=d(this,"data");return null==t?e:e.getLinkedData(t)},setData:function(t){c(this,"data",t)},getRawData:function(){return d(this,"dataBeforeProcessed")},coordDimToDataDim:function(t){return a.coordDimToDataDim(this.getData(),t)},dataDimToCoordDim:function(t){return a.dataDimToCoordDim(this.getData(),t)},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(t,e,n){function o(n){function o(t,n){var i=s.getDimensionInfo(n);if(i&&i.otherDims.tooltip!==!1){var o=i.type,a=(l?"- "+(i.tooltipName||i.name)+": ":"")+("ordinal"===o?t+"":"time"===o?e?"":r.formatTime("yyyy/MM/dd hh:mm:ss",t):p(t));a&&u.push(f(a))}}var l=i.reduce(n,function(t,e,n){var i=s.getDimensionInfo(n);return t|=i&&i.tooltip!==!1&&null!=i.tooltipName},0),u=[],h=a.otherDimToDataDim(s,"tooltip");return h.length?i.each(h,function(e){o(s.get(e,t),e)}):i.each(n,o),(l?"<br/>":"")+u.join(l?"<br/>":", ")}var s=d(this,"data"),l=this.getRawValue(t),u=i.isArray(l)?o(l):f(p(l)),h=s.getName(t),c=s.getItemVisual(t,"color");i.isObject(c)&&c.colorStops&&(c=(c.colorStops[0]||{}).color),c=c||"transparent";var g=r.getTooltipMarker(c),m=this.name;return"\0-"===m&&(m=""),m=m?f(m)+(e?": ":"<br/>"):"",e?g+m+u:m+g+(h?f(h)+": "+u:u)},isAnimationEnabled:function(){if(u.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){c(this,"data",d(this,"dataBeforeProcessed").cloneShallow())},getColorFromPalette:function(t,e){var n=this.ecModel,i=l.getColorFromPalette.call(this,t,e);return i||(i=n.getColorFromPalette(t,e)),i},getAxisTooltipData:null,getTooltipPosition:null});i.mixin(g,a.dataFormatMixin),i.mixin(g,l),t.exports=g},function(t,e,n){var i=n(155),r=n(45);n(156),n(154);var o=n(34),a=n(4),s=n(1),l=n(16),u={};u.getScaleExtent=function(t,e){var n,i,r,o=t.type,l=e.getMin(),u=e.getMax(),h=null!=l,c=null!=u,d=t.getExtent();return"ordinal"===o?n=(e.get("data")||[]).length:(i=e.get("boundaryGap"),s.isArray(i)||(i=[i||0,i||0]),"boolean"==typeof i[0]&&(i=[0,0]),i[0]=a.parsePercent(i[0],1),i[1]=a.parsePercent(i[1],1),r=d[1]-d[0]||Math.abs(d[0])),null==l&&(l="ordinal"===o?n?0:NaN:d[0]-i[0]*r),null==u&&(u="ordinal"===o?n?n-1:NaN:d[1]+i[1]*r),"dataMin"===l?l=d[0]:"function"==typeof l&&(l=l({min:d[0],max:d[1]})),"dataMax"===u?u=d[1]:"function"==typeof u&&(u=u({min:d[0],max:d[1]})),(null==l||!isFinite(l))&&(l=NaN),(null==u||!isFinite(u))&&(u=NaN),t.setBlank(s.eqNaN(l)||s.eqNaN(u)),e.getNeedCrossZero()&&(l>0&&u>0&&!h&&(l=0),l<0&&u<0&&!c&&(u=0)),[l,u]},u.niceScaleExtent=function(t,e){var n=u.getScaleExtent(t,e),i=null!=e.getMin(),r=null!=e.getMax(),o=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var a=t.type;t.setExtent(n[0],n[1]),t.niceExtent({splitNumber:o,fixMin:i,fixMax:r,minInterval:"interval"===a||"time"===a?e.get("minInterval"):null,maxInterval:"interval"===a||"time"===a?e.get("maxInterval"):null});var s=e.get("interval");null!=s&&t.setInterval&&t.setInterval(s)},u.createScaleByModel=function(t,e){if(e=e||t.get("type"))switch(e){case"category":return new i(t.getCategories(),[1/0,-(1/0)]);case"value":return new r;default:return(o.getClass(e)||r).create(t)}},u.ifAxisCrossZero=function(t){var e=t.scale.getExtent(),n=e[0],i=e[1];return!(n>0&&i>0||n<0&&i<0)},u.getAxisLabelInterval=function(t,e,n,i){var r,o=0,a=0,s=1;e.length>40&&(s=Math.floor(e.length/40));for(var u=0;u<t.length;u+=s){var h=t[u],c=l.getBoundingRect(e[u],n,"center","top");c[i?"x":"y"]+=h,c[i?"width":"height"]*=1.3,r?r.intersect(c)?(a++,o=Math.max(o,a)):(r.union(c),a=0):r=c.clone()}return 0===o&&s>1?s:(o+1)*s-1},u.getFormattedLabels=function(t,e){var n=t.scale,i=n.getTicksLabels(),r=n.getTicks();return"string"==typeof e?(e=function(t){return function(e){return t.replace("{value}",null!=e?e:"")}}(e),s.map(i,e)):"function"==typeof e?s.map(r,function(n,i){return e(u.getAxisRawValue(t,n),i)},this):i},u.getAxisRawValue=function(t,e){return"category"===t.type?t.scale.getLabel(e):e},t.exports=u},function(t,e){var n="undefined"==typeof Float32Array?Array:Float32Array,i={create:function(){var t=new n(6);return i.identity(t),t},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t},mul:function(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],l=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=l,t},translate:function(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t},rotate:function(t,e,n){var i=e[0],r=e[2],o=e[4],a=e[1],s=e[3],l=e[5],u=Math.sin(n),h=Math.cos(n);return t[0]=i*h+a*u,t[1]=-i*u+a*h,t[2]=r*h+s*u,t[3]=-r*u+h*s,t[4]=h*o+u*l,t[5]=h*l-u*o,t},scale:function(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t},invert:function(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],s=e[5],l=n*a-o*i;return l?(l=1/l,t[0]=a*l,t[1]=-o*l,t[2]=-i*l,t[3]=n*l,t[4]=(i*s-a*r)*l,t[5]=(o*r-n*s)*l,t):null}};t.exports=i},function(t,e,n){"use strict";function i(t){return t>-w&&t<w;
}function r(t){return t>w||t<-w}function o(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function a(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function s(t,e,n,r,o,a){var s=r+3*(e-n)-t,l=3*(n-2*e+t),u=3*(e-t),h=t-o,c=l*l-3*s*u,d=l*u-9*s*h,f=u*u-3*l*h,p=0;if(i(c)&&i(d))if(i(l))a[0]=0;else{var g=-u/l;g>=0&&g<=1&&(a[p++]=g)}else{var m=d*d-4*c*f;if(i(m)){var v=d/c,g=-l/s+v,x=-v/2;g>=0&&g<=1&&(a[p++]=g),x>=0&&x<=1&&(a[p++]=x)}else if(m>0){var y=b(m),w=c*l+1.5*s*(-d+y),S=c*l+1.5*s*(-d-y);w=w<0?-_(-w,T):_(w,T),S=S<0?-_(-S,T):_(S,T);var g=(-l-(w+S))/(3*s);g>=0&&g<=1&&(a[p++]=g)}else{var I=(2*c*l-3*s*d)/(2*b(c*c*c)),A=Math.acos(I)/3,C=b(c),P=Math.cos(A),g=(-l-2*C*P)/(3*s),x=(-l+C*(P+M*Math.sin(A)))/(3*s),D=(-l+C*(P-M*Math.sin(A)))/(3*s);g>=0&&g<=1&&(a[p++]=g),x>=0&&x<=1&&(a[p++]=x),D>=0&&D<=1&&(a[p++]=D)}}return p}function l(t,e,n,o,a){var s=6*n-12*e+6*t,l=9*e+3*o-3*t-9*n,u=3*e-3*t,h=0;if(i(l)){if(r(s)){var c=-u/s;c>=0&&c<=1&&(a[h++]=c)}}else{var d=s*s-4*l*u;if(i(d))a[0]=-s/(2*l);else if(d>0){var f=b(d),c=(-s+f)/(2*l),p=(-s-f)/(2*l);c>=0&&c<=1&&(a[h++]=c),p>=0&&p<=1&&(a[h++]=p)}}return h}function u(t,e,n,i,r,o){var a=(e-t)*r+t,s=(n-e)*r+e,l=(i-n)*r+n,u=(s-a)*r+a,h=(l-s)*r+s,c=(h-u)*r+u;o[0]=t,o[1]=a,o[2]=u,o[3]=c,o[4]=c,o[5]=h,o[6]=l,o[7]=i}function h(t,e,n,i,r,a,s,l,u,h,c){var d,f,p,g,m,v=.005,x=1/0;I[0]=u,I[1]=h;for(var _=0;_<1;_+=.05)A[0]=o(t,n,r,s,_),A[1]=o(e,i,a,l,_),g=y(I,A),g<x&&(d=_,x=g);x=1/0;for(var w=0;w<32&&!(v<S);w++)f=d-v,p=d+v,A[0]=o(t,n,r,s,f),A[1]=o(e,i,a,l,f),g=y(A,I),f>=0&&g<x?(d=f,x=g):(C[0]=o(t,n,r,s,p),C[1]=o(e,i,a,l,p),m=y(C,I),p<=1&&m<x?(d=p,x=m):v*=.5);return c&&(c[0]=o(t,n,r,s,d),c[1]=o(e,i,a,l,d)),b(x)}function c(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function d(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function f(t,e,n,o,a){var s=t-2*e+n,l=2*(e-t),u=t-o,h=0;if(i(s)){if(r(l)){var c=-u/l;c>=0&&c<=1&&(a[h++]=c)}}else{var d=l*l-4*s*u;if(i(d)){var c=-l/(2*s);c>=0&&c<=1&&(a[h++]=c)}else if(d>0){var f=b(d),c=(-l+f)/(2*s),p=(-l-f)/(2*s);c>=0&&c<=1&&(a[h++]=c),p>=0&&p<=1&&(a[h++]=p)}}return h}function p(t,e,n){var i=t+n-2*e;return 0===i?.5:(t-e)/i}function g(t,e,n,i,r){var o=(e-t)*i+t,a=(n-e)*i+e,s=(a-o)*i+o;r[0]=t,r[1]=o,r[2]=s,r[3]=s,r[4]=a,r[5]=n}function m(t,e,n,i,r,o,a,s,l){var u,h=.005,d=1/0;I[0]=a,I[1]=s;for(var f=0;f<1;f+=.05){A[0]=c(t,n,r,f),A[1]=c(e,i,o,f);var p=y(I,A);p<d&&(u=f,d=p)}d=1/0;for(var g=0;g<32&&!(h<S);g++){var m=u-h,v=u+h;A[0]=c(t,n,r,m),A[1]=c(e,i,o,m);var p=y(A,I);if(m>=0&&p<d)u=m,d=p;else{C[0]=c(t,n,r,v),C[1]=c(e,i,o,v);var x=y(C,I);v<=1&&x<d?(u=v,d=x):h*=.5}}return l&&(l[0]=c(t,n,r,u),l[1]=c(e,i,o,u)),b(d)}var v=n(6),x=v.create,y=v.distSquare,_=Math.pow,b=Math.sqrt,w=1e-8,S=1e-4,M=b(3),T=1/3,I=x(),A=x(),C=x();t.exports={cubicAt:o,cubicDerivativeAt:a,cubicRootAt:s,cubicExtrema:l,cubicSubdivide:u,cubicProjectPoint:h,quadraticAt:c,quadraticDerivativeAt:d,quadraticRootAt:f,quadraticExtremum:p,quadraticSubdivide:g,quadraticProjectPoint:m}},function(t,e,n){"use strict";function i(t){return t.getBoundingClientRect?t.getBoundingClientRect():{left:0,top:0}}function r(t,e,n,i){return n=n||{},i||!h.canvasSupported?o(t,e,n):h.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):o(t,e,n),n}function o(t,e,n){var r=i(t);n.zrX=e.clientX-r.left,n.zrY=e.clientY-r.top}function a(t,e,n){if(e=e||window.event,null!=e.zrX)return e;var i=e.type,o=i&&i.indexOf("touch")>=0;if(o){var a="touchend"!=i?e.targetTouches[0]:e.changedTouches[0];a&&r(t,a,e,n)}else r(t,e,e,n),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;return e}function s(t,e,n){c?t.addEventListener(e,n):t.attachEvent("on"+e,n)}function l(t,e,n){c?t.removeEventListener(e,n):t.detachEvent("on"+e,n)}var u=n(23),h=n(10),c="undefined"!=typeof window&&!!window.addEventListener,d=c?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0};t.exports={clientToLocal:r,normalizeEvent:a,addEventListener:s,removeEventListener:l,stop:d,Dispatcher:u}},function(t,e,n){function i(t){return t=Math.round(t),t<0?0:t>255?255:t}function r(t){return t=Math.round(t),t<0?0:t>360?360:t}function o(t){return t<0?0:t>1?1:t}function a(t){return i(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100*255:parseInt(t,10))}function s(t){return o(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100:parseFloat(t))}function l(t,e,n){return n<0?n+=1:n>1&&(n-=1),6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function u(t,e,n){return t+(e-t)*n}function h(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function c(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function d(t,e){I&&c(I,e),I=T.put(t,I||e.slice())}function f(t,e){if(t){e=e||[];var n=T.get(t);if(n)return c(e,n);t+="";var i=t.replace(/ /g,"").toLowerCase();if(i in M)return c(e,M[i]),d(t,e),e;if("#"!==i.charAt(0)){var r=i.indexOf("("),o=i.indexOf(")");if(r!==-1&&o+1===i.length){var l=i.substr(0,r),u=i.substr(r+1,o-(r+1)).split(","),f=1;switch(l){case"rgba":if(4!==u.length)return void h(e,0,0,0,1);f=s(u.pop());case"rgb":return 3!==u.length?void h(e,0,0,0,1):(h(e,a(u[0]),a(u[1]),a(u[2]),f),d(t,e),e);case"hsla":return 4!==u.length?void h(e,0,0,0,1):(u[3]=s(u[3]),p(u,e),d(t,e),e);case"hsl":return 3!==u.length?void h(e,0,0,0,1):(p(u,e),d(t,e),e);default:return}}h(e,0,0,0,1)}else{if(4===i.length){var g=parseInt(i.substr(1),16);return g>=0&&g<=4095?(h(e,(3840&g)>>4|(3840&g)>>8,240&g|(240&g)>>4,15&g|(15&g)<<4,1),d(t,e),e):void h(e,0,0,0,1)}if(7===i.length){var g=parseInt(i.substr(1),16);return g>=0&&g<=16777215?(h(e,(16711680&g)>>16,(65280&g)>>8,255&g,1),d(t,e),e):void h(e,0,0,0,1)}}}}function p(t,e){var n=(parseFloat(t[0])%360+360)%360/360,r=s(t[1]),o=s(t[2]),a=o<=.5?o*(r+1):o+r-o*r,u=2*o-a;return e=e||[],h(e,i(255*l(u,a,n+1/3)),i(255*l(u,a,n)),i(255*l(u,a,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function g(t){if(t){var e,n,i=t[0]/255,r=t[1]/255,o=t[2]/255,a=Math.min(i,r,o),s=Math.max(i,r,o),l=s-a,u=(s+a)/2;if(0===l)e=0,n=0;else{n=u<.5?l/(s+a):l/(2-s-a);var h=((s-i)/6+l/2)/l,c=((s-r)/6+l/2)/l,d=((s-o)/6+l/2)/l;i===s?e=d-c:r===s?e=1/3+h-d:o===s&&(e=2/3+c-h),e<0&&(e+=1),e>1&&(e-=1)}var f=[360*e,n,u];return null!=t[3]&&f.push(t[3]),f}}function m(t,e){var n=f(t);if(n){for(var i=0;i<3;i++)e<0?n[i]=n[i]*(1-e)|0:n[i]=(255-n[i])*e+n[i]|0;return w(n,4===n.length?"rgba":"rgb")}}function v(t,e){var n=f(t);if(n)return((1<<24)+(n[0]<<16)+(n[1]<<8)+ +n[2]).toString(16).slice(1)}function x(t,e,n){if(e&&e.length&&t>=0&&t<=1){n=n||[];var r=t*(e.length-1),a=Math.floor(r),s=Math.ceil(r),l=e[a],h=e[s],c=r-a;return n[0]=i(u(l[0],h[0],c)),n[1]=i(u(l[1],h[1],c)),n[2]=i(u(l[2],h[2],c)),n[3]=o(u(l[3],h[3],c)),n}}function y(t,e,n){if(e&&e.length&&t>=0&&t<=1){var r=t*(e.length-1),a=Math.floor(r),s=Math.ceil(r),l=f(e[a]),h=f(e[s]),c=r-a,d=w([i(u(l[0],h[0],c)),i(u(l[1],h[1],c)),i(u(l[2],h[2],c)),o(u(l[3],h[3],c))],"rgba");return n?{color:d,leftIndex:a,rightIndex:s,value:r}:d}}function _(t,e,n,i){if(t=f(t))return t=g(t),null!=e&&(t[0]=r(e)),null!=n&&(t[1]=s(n)),null!=i&&(t[2]=s(i)),w(p(t),"rgba")}function b(t,e){if(t=f(t),t&&null!=e)return t[3]=o(e),w(t,"rgba")}function w(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}}var S=n(72),M={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]},T=new S(20),I=null;t.exports={parse:f,lift:m,toHex:v,fastMapToColor:x,mapToColor:y,modifyHSL:_,modifyAlpha:b,stringify:w}},function(t,e){var n=Array.prototype.slice,i=function(){this._$handlers={}};i.prototype={constructor:i,one:function(t,e,n){var i=this._$handlers;if(!e||!t)return this;i[t]||(i[t]=[]);for(var r=0;r<i[t].length;r++)if(i[t][r].h===e)return this;return i[t].push({h:e,one:!0,ctx:n||this}),this},on:function(t,e,n){var i=this._$handlers;if(!e||!t)return this;i[t]||(i[t]=[]);for(var r=0;r<i[t].length;r++)if(i[t][r].h===e)return this;return i[t].push({h:e,one:!1,ctx:n||this}),this},isSilent:function(t){var e=this._$handlers;return e[t]&&e[t].length},off:function(t,e){var n=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!=e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},trigger:function(t){if(this._$handlers[t]){var e=arguments,i=e.length;i>3&&(e=n.call(e,1));for(var r=this._$handlers[t],o=r.length,a=0;a<o;){switch(i){case 1:r[a].h.call(r[a].ctx);break;case 2:r[a].h.call(r[a].ctx,e[1]);break;case 3:r[a].h.call(r[a].ctx,e[1],e[2]);break;default:r[a].h.apply(r[a].ctx,e)}r[a].one?(r.splice(a,1),o--):a++}}return this},triggerWithContext:function(t){if(this._$handlers[t]){var e=arguments,i=e.length;i>4&&(e=n.call(e,1,e.length-1));for(var r=e[e.length-1],o=this._$handlers[t],a=o.length,s=0;s<a;){switch(i){case 1:o[s].h.call(r);break;case 2:o[s].h.call(r,e[1]);break;case 3:o[s].h.call(r,e[1],e[2]);break;default:o[s].h.apply(r,e)}o[s].one?(o.splice(s,1),a--):s++}}return this}},t.exports=i},function(t,e,n){"use strict";var i=n(3),r=n(12),o=i.extendShape({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i+o),t.lineTo(n-r,i+o),t.closePath()}}),a=i.extendShape({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i),t.lineTo(n,i+o),t.lineTo(n-r,i),t.closePath()}}),s=i.extendShape({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,o=Math.max(r,e.height),a=r/2,s=a*a/(o-a),l=i-o+a+s,u=Math.asin(s/a),h=Math.cos(u)*a,c=Math.sin(u),d=Math.cos(u);t.arc(n,l,a,Math.PI-u,2*Math.PI+u);var f=.6*a,p=.7*a;t.bezierCurveTo(n+h-c*f,l+s+d*f,n,i-p,n,i),t.bezierCurveTo(n,i-p,n-h+c*f,l+s+d*f,n-h,l+s),t.closePath()}}),l=i.extendShape({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,o=e.y,a=i/3*2;t.moveTo(r,o),t.lineTo(r+a,o+n),t.lineTo(r,o+n/4*3),t.lineTo(r-a,o+n),t.lineTo(r,o),t.closePath()}}),u={line:i.Line,rect:i.Rect,roundRect:i.Rect,square:i.Rect,circle:i.Circle,diamond:a,pin:s,arrow:l,triangle:o},h={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var o=Math.min(n,i);r.x=t,r.y=e,r.width=o,r.height=o},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},c={};for(var d in u)u.hasOwnProperty(d)&&(c[d]=new u[d]);var f=i.extendShape({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},beforeBrush:function(){var t=this.style,e=this.shape;"pin"===e.symbolType&&"inside"===t.textPosition&&(t.textPosition=["50%","40%"],t.textAlign="center",t.textVerticalAlign="middle")},buildPath:function(t,e,n){var i=e.symbolType,r=c[i];"none"!==e.symbolType&&(r||(i="rect",r=c[i]),h[i](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,n))}}),p=function(t){if("image"!==this.type){var e=this.style,n=this.shape;n&&"line"===n.symbolType?e.stroke=t:this.__isEmptyBrush?(e.stroke=t,e.fill="#fff"):(e.fill&&(e.fill=t),e.stroke&&(e.stroke=t)),this.dirty(!1)}},g={createSymbol:function(t,e,n,o,a,s){var l=0===t.indexOf("empty");l&&(t=t.substr(5,1).toLowerCase()+t.substr(6));var u;return u=0===t.indexOf("image://")?new i.Image({style:{image:t.slice(8),x:e,y:n,width:o,height:a}}):0===t.indexOf("path://")?i.makePath(t.slice(7),{},new r(e,n,o,a)):new f({shape:{symbolType:t,x:e,y:n,width:o,height:a}}),u.__isEmptyBrush=l,u.setColor=p,u.setColor(s),u}};t.exports=g},function(t,e,n){function i(t,e,n){function i(t,e,n){c[e]?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,m.set(e,!0))}function a(t,e,n){if(n||null!=e.get(t)){for(var i=0;null!=e.get(t+i);)i++;t+=i}return e.set(t,!0),t}e=e||[],n=n||{},t=(t||[]).slice();var f=(n.dimsDef||[]).slice(),p=o.createHashMap(n.encodeDef),g=o.createHashMap(),m=o.createHashMap(),v=[],x=n.dimCount;if(null==x){var y=r(e[0]);x=Math.max(o.isArray(y)&&y.length||1,t.length,f.length),s(t,function(t){var e=t.dimsDef;e&&(x=Math.max(x,e.length))})}for(var _=0;_<x;_++){var b=l(f[_])?{name:f[_]}:f[_]||{},w=b.name,S=v[_]={otherDims:{}};null!=w&&null==g.get(w)&&(S.name=S.tooltipName=w,g.set(w,_)),null!=b.type&&(S.type=b.type)}p.each(function(t,e){t=p.set(e,h(t).slice()),s(t,function(n,r){l(n)&&(n=g.get(n)),null!=n&&n<x&&(t[r]=n,i(v[n],e,r))})});var M=0;s(t,function(t,e){var n,t,r,a;l(t)?(n=t,t={}):(n=t.name,t=o.clone(t),r=t.dimsDef,a=t.otherDims,t.name=t.coordDim=t.coordDimIndex=t.dimsDef=t.otherDims=null);var c=h(p.get(n));if(!c.length)for(var d=0;d<(r&&r.length||1);d++){for(;M<v.length&&null!=v[M].coordDim;)M++;M<v.length&&c.push(M++)}s(c,function(e,o){var s=v[e];i(u(s,t),n,o),null==s.name&&r&&(s.name=s.tooltipName=r[o]),a&&u(s.otherDims,a)})});for(var T=n.extraPrefix||"value",I=0;I<x;I++){var S=v[I]=v[I]||{},A=S.coordDim;null==A&&(S.coordDim=a(T,m,n.extraFromZero),S.coordDimIndex=0,S.isExtraCoord=!0),null==S.name&&(S.name=a(S.coordDim,g)),null==S.type&&d(e,I)&&(S.type="ordinal")}return v}function r(t){return o.isArray(t)?t:o.isObject(t)?t.value:t}var o=n(1),a=n(5),s=o.each,l=o.isString,u=o.defaults,h=a.normalizeToArray,c={tooltip:1,label:1,itemName:1},d=i.guessOrdinal=function(t,e){for(var n=0,i=t.length;n<i;n++){var a=r(t[n]);if(!o.isArray(a))return!1;var a=a[e];if(null!=a&&isFinite(a)&&""!==a)return!1;if(l(a)&&"-"!==a)return!0}return!1};t.exports=i},function(t,e,n){"use strict";function i(){this._coordinateSystems=[]}var r=n(1),o={};i.prototype={constructor:i,create:function(t,e){var n=[];r.each(o,function(i,r){var o=i.create(t,e);n=n.concat(o||[])}),this._coordinateSystems=n},update:function(t,e){r.each(this._coordinateSystems,function(n){n.update&&n.update(t,e)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},i.register=function(t,e){o[t]=e},i.get=function(t){return o[t]},t.exports=i},function(t,e,n){"use strict";var i=n(20),r=n(6),o=n(89),a=n(12),s=n(35).devicePixelRatio,l={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},u=[],h=[],c=[],d=[],f=Math.min,p=Math.max,g=Math.cos,m=Math.sin,v=Math.sqrt,x=Math.abs,y="undefined"!=typeof Float32Array,_=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};_.prototype={constructor:_,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e){this._ux=x(1/s/t)||0,this._uy=x(1/s/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(l.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var n=x(t-this._xi)>this._ux||x(e-this._yi)>this._uy||this._len<5;return this.addData(l.L,t,e),this._ctx&&n&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),n&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,n,i,r,o){return this.addData(l.C,t,e,n,i,r,o),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,n,i,r,o):this._ctx.bezierCurveTo(t,e,n,i,r,o)),this._xi=r,this._yi=o,this},quadraticCurveTo:function(t,e,n,i){return this.addData(l.Q,t,e,n,i),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,n,i):this._ctx.quadraticCurveTo(t,e,n,i)),this._xi=n,this._yi=i,this},arc:function(t,e,n,i,r,o){return this.addData(l.A,t,e,n,n,i,r-i,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=g(r)*n+t,this._yi=m(r)*n+t,this},arcTo:function(t,e,n,i,r){return this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},rect:function(t,e,n,i){return this._ctx&&this._ctx.rect(t,e,n,i),this.addData(l.R,t,e,n,i),this},closePath:function(){this.addData(l.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,n),t.closePath()),this._xi=e,this._yi=n,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,n=0;n<t.length;n++)e+=t[n];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length==e||!y||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();y&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(var r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var n=0;n<arguments.length;n++)e[this._len++]=arguments[n];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var n,i,r=this._dashSum,o=this._dashOffset,a=this._lineDash,s=this._ctx,l=this._xi,u=this._yi,h=t-l,c=e-u,d=v(h*h+c*c),g=l,m=u,x=a.length;for(h/=d,c/=d,o<0&&(o=r+o),o%=r,g-=o*h,m-=o*c;h>0&&g<=t||h<0&&g>=t||0==h&&(c>0&&m<=e||c<0&&m>=e);)i=this._dashIdx,n=a[i],g+=h*n,m+=c*n,this._dashIdx=(i+1)%x,h>0&&g<l||h<0&&g>l||c>0&&m<u||c<0&&m>u||s[i%2?"moveTo":"lineTo"](h>=0?f(g,t):p(g,t),c>=0?f(m,e):p(m,e));h=g-t,c=m-e,this._dashOffset=-v(h*h+c*c)},_dashedBezierTo:function(t,e,n,r,o,a){var s,l,u,h,c,d=this._dashSum,f=this._dashOffset,p=this._lineDash,g=this._ctx,m=this._xi,x=this._yi,y=i.cubicAt,_=0,b=this._dashIdx,w=p.length,S=0;for(f<0&&(f=d+f),f%=d,s=0;s<1;s+=.1)l=y(m,t,n,o,s+.1)-y(m,t,n,o,s),u=y(x,e,r,a,s+.1)-y(x,e,r,a,s),_+=v(l*l+u*u);for(;b<w&&(S+=p[b],!(S>f));b++);for(s=(S-f)/_;s<=1;)h=y(m,t,n,o,s),c=y(x,e,r,a,s),b%2?g.moveTo(h,c):g.lineTo(h,c),s+=p[b]/_,b=(b+1)%w;b%2!==0&&g.lineTo(o,a),l=o-h,u=a-c,this._dashOffset=-v(l*l+u*u)},_dashedQuadraticTo:function(t,e,n,i){var r=n,o=i;n=(n+2*t)/3,i=(i+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,n,i,r,o)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,y&&(this.data=new Float32Array(t)))},getBoundingRect:function(){u[0]=u[1]=c[0]=c[1]=Number.MAX_VALUE,h[0]=h[1]=d[0]=d[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,n=0,i=0,s=0,f=0;f<t.length;){var p=t[f++];switch(1==f&&(e=t[f],n=t[f+1],i=e,s=n),p){case l.M:i=t[f++],s=t[f++],e=i,n=s,c[0]=i,c[1]=s,d[0]=i,d[1]=s;break;case l.L:o.fromLine(e,n,t[f],t[f+1],c,d),e=t[f++],n=t[f++];break;case l.C:o.fromCubic(e,n,t[f++],t[f++],t[f++],t[f++],t[f],t[f+1],c,d),e=t[f++],n=t[f++];break;case l.Q:o.fromQuadratic(e,n,t[f++],t[f++],t[f],t[f+1],c,d),e=t[f++],n=t[f++];break;case l.A:var v=t[f++],x=t[f++],y=t[f++],_=t[f++],b=t[f++],w=t[f++]+b,S=(t[f++],1-t[f++]);1==f&&(i=g(b)*y+v,s=m(b)*_+x),o.fromArc(v,x,y,_,b,w,S,c,d),e=g(w)*y+v,n=m(w)*_+x;break;case l.R:i=e=t[f++],s=n=t[f++];var M=t[f++],T=t[f++];o.fromLine(i,s,i+M,s+T,c,d);break;case l.Z:e=i,n=s}r.min(u,u,c),r.max(h,h,d)}return 0===f&&(u[0]=u[1]=h[0]=h[1]=0),new a(u[0],u[1],h[0]-u[0],h[1]-u[1])},rebuildPath:function(t){for(var e,n,i,r,o,a,s=this.data,u=this._ux,h=this._uy,c=this._len,d=0;d<c;){var f=s[d++];switch(1==d&&(i=s[d],r=s[d+1],e=i,n=r),f){case l.M:e=i=s[d++],n=r=s[d++],t.moveTo(i,r);break;case l.L:o=s[d++],a=s[d++],(x(o-i)>u||x(a-r)>h||d===c-1)&&(t.lineTo(o,a),i=o,r=a);break;case l.C:t.bezierCurveTo(s[d++],s[d++],s[d++],s[d++],s[d++],s[d++]),i=s[d-2],r=s[d-1];break;case l.Q:t.quadraticCurveTo(s[d++],s[d++],s[d++],s[d++]),i=s[d-2],r=s[d-1];break;case l.A:var p=s[d++],v=s[d++],y=s[d++],_=s[d++],b=s[d++],w=s[d++],S=s[d++],M=s[d++],T=y>_?y:_,I=y>_?1:y/_,A=y>_?_/y:1,C=Math.abs(y-_)>.001,P=b+w;C?(t.translate(p,v),t.rotate(S),t.scale(I,A),t.arc(0,0,T,b,P,1-M),t.scale(1/I,1/A),t.rotate(-S),t.translate(-p,-v)):t.arc(p,v,T,b,P,1-M),1==d&&(e=g(b)*y+p,n=m(b)*_+v),i=g(P)*y+p,r=m(P)*_+v;break;case l.R:e=i=s[d],n=r=s[d+1],t.rect(s[d++],s[d++],s[d++],s[d++]);break;case l.Z:t.closePath(),i=e,r=n}}}},_.CMD=l,t.exports=_},function(t,e,n){"use strict";function i(t){for(var e=0;e<t.length&&null==t[e];)e++;return t[e]}function r(t){var e=i(t);return null!=e&&!c.isArray(p(e))}function o(t,e,n){t=t||[];var i=e.get("coordinateSystem"),o=m[i],a=f.get(i),s={encodeDef:e.get("encode"),dimsDef:e.get("dimensions")},v=o&&o(t,e,n,s),x=v&&v.dimensions;x||(x=a&&(a.getDimensionsInfo?a.getDimensionsInfo():a.dimensions.slice())||["x","y"],x=h(x,t,s));var y=v?v.categoryIndex:-1,_=new u(x,e),b=l(v,t),w={},S=y>=0&&r(t)?function(t,e,n,i){return d.isDataItemOption(t)&&(_.hasItemOption=!0),i===y?n:g(p(t),x[i])}:function(t,e,n,i){var r=p(t),o=g(r&&r[i],x[i]);d.isDataItemOption(t)&&(_.hasItemOption=!0);var a=v&&v.categoryAxesModels;return a&&a[e]&&"string"==typeof o&&(w[e]=w[e]||a[e].getCategories(),o=c.indexOf(w[e],o),o<0&&!isNaN(o)&&(o=+o)),o};return _.hasItemOption=!1,_.initData(t,b,S),_}function a(t){return"category"!==t&&"time"!==t}function s(t){return"category"===t?"ordinal":"time"===t?"time":"float"}function l(t,e){var n,i=[],r=t&&t.dimensions[t.categoryIndex];if(r&&(n=t.categoryAxesModels[r.name]),n){var o=n.getCategories();if(o){var a=e.length;if(c.isArray(e[0])&&e[0].length>1){i=[];for(var s=0;s<a;s++)i[s]=o[e[s][t.categoryIndex||0]]}else i=o.slice(0)}}return i}var u=n(14),h=n(25),c=n(1),d=n(5),f=n(26),p=d.getDataItemValue,g=d.converDataValue,m={cartesian2d:function(t,e,n,i){var r=c.map(["xAxis","yAxis"],function(t){return n.queryComponents({mainType:t,index:e.get(t+"Index"),id:e.get(t+"Id")})[0]}),o=r[0],l=r[1],u=o.get("type"),d=l.get("type"),f=[{name:"x",type:s(u),stackable:a(u)},{name:"y",type:s(d),stackable:a(d)}],p="category"===u,g="category"===d;f=h(f,t,i);var m={};return p&&(m.x=o),g&&(m.y=l),{dimensions:f,categoryIndex:p?0:g?1:-1,categoryAxesModels:m}},singleAxis:function(t,e,n,i){var r=n.queryComponents({mainType:"singleAxis",index:e.get("singleAxisIndex"),id:e.get("singleAxisId")})[0],o=r.get("type"),l="category"===o,u=[{name:"single",type:s(o),stackable:a(o)}];u=h(u,t,i);var c={};return l&&(c.single=r),{dimensions:u,categoryIndex:l?0:-1,categoryAxesModels:c}},polar:function(t,e,n,i){var r=n.queryComponents({mainType:"polar",index:e.get("polarIndex"),id:e.get("polarId")})[0],o=r.findAxisModel("angleAxis"),l=r.findAxisModel("radiusAxis"),u=l.get("type"),c=o.get("type"),d=[{name:"radius",type:s(u),stackable:a(u)},{name:"angle",type:s(c),stackable:a(c)}],f="category"===c,p="category"===u;d=h(d,t,i);var g={};return p&&(g.radius=l),f&&(g.angle=o),{dimensions:d,categoryIndex:f?1:p?0:-1,categoryAxesModels:g}},geo:function(t,e,n,i){return{dimensions:h([{name:"lng"},{name:"lat"}],t,i)}}};t.exports=o},function(t,e){"use strict";var n={};t.exports={register:function(t,e){n[t]=e},get:function(t){return n[t]}}},function(t,e,n){function i(){this.group=new a,this.uid=s.getUID("viewChart")}function r(t,e){if(t&&(t.trigger(e),"group"===t.type))for(var n=0;n<t.childCount();n++)r(t.childAt(n),e)}function o(t,e,n){var i=u.queryDataIndex(t,e);null!=i?h.each(u.normalizeToArray(i),function(e){r(t.getItemGraphicEl(e),n)}):t.eachItemGraphicEl(function(t){r(t,n)})}var a=n(36),s=n(50),l=n(15),u=n(5),h=n(1);i.prototype={type:"chart",init:function(t,e){},render:function(t,e,n,i){},highlight:function(t,e,n,i){o(t.getData(),i,"emphasis")},downplay:function(t,e,n,i){o(t.getData(),i,"normal")},remove:function(t,e){this.group.removeAll()},dispose:function(){}};var c=i.prototype;c.updateView=c.updateLayout=c.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},l.enableClassExtend(i,["dispose"]),l.enableClassManagement(i,{registerWhenExtend:!0}),t.exports=i},function(t,e,n){var i=n(1);t.exports=function(t){for(var e=0;e<t.length;e++)t[e][1]||(t[e][1]=t[e][0]);return function(e,n){for(var r={},o=0;o<t.length;o++){var a=t[o][1];if(!(e&&i.indexOf(e,a)>=0||n&&i.indexOf(n,a)<0)){var s=this.getShallow(a);null!=s&&(r[t[o][0]]=s)}}return r}}},function(t,e,n){"use strict";var i=n(3),r=n(1),o=n(2);n(59),n(121),o.extendComponentView({type:"grid",render:function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new i.Rect({shape:t.coordinateSystem.getRect(),style:r.defaults({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),o.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})},function(t,e,n){function i(t,e){var n=t[1]-t[0],i=e,r=n/i/2;t[0]+=r,t[1]-=r}var r=n(4),o=r.linearMap,a=n(1),s=n(18),l=[0,1],u=function(t,e,n){this.dim=t,this.scale=e,this._extent=n||[0,0],this.inverse=!1,this.onBand=!1,this._labelInterval};u.prototype={constructor:u,contain:function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return t>=n&&t<=i},containData:function(t){return this.contain(this.dataToCoord(t))},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return r.getPixelPrecision(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var n=this._extent;n[0]=t,n[1]=e},dataToCoord:function(t,e){var n=this._extent,r=this.scale;return t=r.normalize(t),this.onBand&&"ordinal"===r.type&&(n=n.slice(),i(n,r.count())),o(t,l,n,e)},coordToData:function(t,e){var n=this._extent,r=this.scale;this.onBand&&"ordinal"===r.type&&(n=n.slice(),i(n,r.count()));var a=o(t,n,l,e);return this.scale.scale(a)},pointToData:function(t,e){},getTicksCoords:function(t){if(this.onBand&&!t){for(var e=this.getBands(),n=[],i=0;i<e.length;i++)n.push(e[i][0]);return e[i-1]&&n.push(e[i-1][1]),n}return a.map(this.scale.getTicks(),this.dataToCoord,this)},getLabelsCoords:function(){return a.map(this.scale.getTicks(),this.dataToCoord,this)},getBands:function(){for(var t=this.getExtent(),e=[],n=this.scale.count(),i=t[0],r=t[1],o=r-i,a=0;a<n;a++)e.push([o*a/n+i,o*(a+1)/n+i]);return e},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);0===n&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},getLabelInterval:function(){var t=this._labelInterval;if(!t){var e=this.model,n=e.getModel("axisLabel"),i=n.get("interval");"category"!==this.type||"auto"!==i?t="auto"===i?0:i:this.isHorizontal&&(t=s.getAxisLabelInterval(a.map(this.scale.getTicks(),this.dataToCoord,this),e.getFormattedLabels(),n.getFont(),this.isHorizontal())),this._labelInterval=t}return t}},t.exports=u},function(t,e,n){function i(t){this._setting=t||{},this._extent=[1/0,-(1/0)],this._interval=0,this.init&&this.init.apply(this,arguments)}var r=n(15),o=i.prototype;o.parse=function(t){return t},o.getSetting=function(t){return this._setting[t]},o.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},o.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},o.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},o.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},o.unionExtentFromData=function(t,e){this.unionExtent(t.getDataExtent(e,!0))},o.getExtent=function(){return this._extent.slice()},o.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},o.getTicksLabels=function(){for(var t=[],e=this.getTicks(),n=0;n<e.length;n++)t.push(this.getLabel(e[n]));return t},o.isBlank=function(){return this._isBlank},o.setBlank=function(t){this._isBlank=t},r.enableClassExtend(i),r.enableClassManagement(i,{registerWhenExtend:!0}),t.exports=i},function(t,e){var n=1;"undefined"!=typeof window&&(n=Math.max(window.devicePixelRatio||1,1));var i={debugMode:0,devicePixelRatio:n};t.exports=i},function(t,e,n){var i=n(1),r=n(68),o=n(12),a=function(t){t=t||{},r.call(this,t);for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};a.prototype={constructor:a,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),
this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,i=n.indexOf(e);i>=0&&(n.splice(i,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__storage,n=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof a&&t.addChildrenToStorage(e)),n&&n.refresh()},remove:function(t){var e=this.__zr,n=this.__storage,r=this._children,o=i.indexOf(r,t);return o<0?this:(r.splice(o,1),t.parent=null,n&&(n.delFromStorage(t),t instanceof a&&t.delChildrenFromStorage(n)),e&&e.refresh(),this)},removeAll:function(){var t,e,n=this._children,i=this.__storage;for(e=0;e<n.length;e++)t=n[e],i&&(i.delFromStorage(t),t instanceof a&&t.delChildrenFromStorage(i)),t.parent=null;return n.length=0,this},eachChild:function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},traverse:function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n];t.call(e,i),"group"===i.type&&i.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.addToStorage(n),n instanceof a&&n.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.delFromStorage(n),n instanceof a&&n.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,n=new o(0,0,0,0),i=t||this._children,r=[],a=0;a<i.length;a++){var s=i[a];if(!s.ignore&&!s.invisible){var l=s.getBoundingRect(),u=s.getLocalTransform(r);u?(n.copy(l),n.applyTransform(u),e=e||n.clone(),e.union(n)):(e=e||l.clone(),e.union(l))}}return e||n}},i.inherits(a,r),t.exports=a},function(t,e){var n={},i="\0__throttleOriginMethod",r="\0__throttleRate",o="\0__throttleType";n.throttle=function(t,e,n){function i(){h=(new Date).getTime(),c=null,t.apply(a,s||[])}var r,o,a,s,l,u=0,h=0,c=null;e=e||0;var d=function(){r=(new Date).getTime(),a=this,s=arguments;var t=l||e,d=l||n;l=null,o=r-(d?u:h)-t,clearTimeout(c),d?c=setTimeout(i,t):o>=0?i():c=setTimeout(i,-o),u=r};return d.clear=function(){c&&(clearTimeout(c),c=null)},d.debounceNextCall=function(t){l=t},d},n.createOrUpdate=function(t,e,a,s){var l=t[e];if(l){var u=l[i]||l,h=l[o],c=l[r];if(c!==a||h!==s){if(null==a||!s)return t[e]=u;l=t[e]=n.throttle(u,a,"debounce"===s),l[i]=u,l[o]=s,l[r]=a}return l}},n.clear=function(t,e){var n=t[e];n&&n[i]&&(t[e]=n[i])},t.exports=n},function(t,e,n){function i(t){t=t||{},a.call(this,t);for(var e in t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new o(t.style,this),this._rect=null,this.__clipPaths=[]}var r=n(1),o=n(75),a=n(68),s=n(91);i.prototype={constructor:i,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:-1,beforeBrush:function(t){},afterBrush:function(t){},brush:function(t,e){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();return i.contain(n[0],n[1])},dirty:function(){this.__dirty=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?a.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new o(t,this),this.dirty(!1),this}},r.inherits(i,a),r.mixin(i,s),t.exports=i},function(t,e){var n=function(t){this.colorStops=t||[]};n.prototype={constructor:n,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}},t.exports=n},function(t,e,n){function i(t){if(t){t.font=m.makeFont(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||w[e]?e:"left";var n=t.textVerticalAlign||t.textBaseline;"center"===n&&(n="middle"),t.textVerticalAlign=null==n||S[n]?n:"top";var i=t.textPadding;i&&(t.textPadding=v.normalizeCssArray(t.textPadding))}}function r(t,e,n,i,r){var o=f(e,"font",i.font||m.DEFAULT_FONT),a=i.textPadding,l=t.__textCotentBlock;l&&!t.__dirty||(l=t.__textCotentBlock=m.parsePlainText(n,o,a,i.truncate));var c=l.outerHeight,p=l.lines,v=l.lineHeight,x=d(c,i,r),y=x.baseX,_=x.baseY,b=x.textAlign,w=x.textVerticalAlign;s(e,i,r,y,_);var S=m.adjustTextY(_,c,w),M=y,A=S,C=u(i);if(C||a){var P=m.getWidth(n,o),D=P;a&&(D+=a[1]+a[3]);var k=m.adjustTextX(y,D,b);C&&h(t,e,i,k,S,D,c),a&&(M=g(y,b,a),A+=a[0])}f(e,"textAlign",b||"left"),f(e,"textBaseline","middle"),f(e,"shadowBlur",i.textShadowBlur||0),f(e,"shadowColor",i.textShadowColor||"transparent"),f(e,"shadowOffsetX",i.textShadowOffsetX||0),f(e,"shadowOffsetY",i.textShadowOffsetY||0),A+=v/2;var L=i.textLineWidth,O=T(i.textStroke,L),z=I(i.textFill);O&&(f(e,"lineWidth",L),f(e,"strokeStyle",O)),z&&f(e,"fillStyle",z);for(var E=0;E<p.length;E++)O&&e.strokeText(p[E],M,A),z&&e.fillText(p[E],M,A),A+=v}function o(t,e,n,i,r){var o=t.__textCotentBlock;o&&!t.__dirty||(o=t.__textCotentBlock=m.parseRichText(n,i)),a(t,e,o,i,r)}function a(t,e,n,i,r){var o=n.width,a=n.outerWidth,c=n.outerHeight,f=i.textPadding,p=d(c,i,r),g=p.baseX,v=p.baseY,x=p.textAlign,y=p.textVerticalAlign;s(e,i,r,g,v);var _=m.adjustTextX(g,a,x),b=m.adjustTextY(v,c,y),w=_,S=b;f&&(w+=f[3],S+=f[0]);var M=w+o;u(i)&&h(t,e,i,_,b,a,c);for(var T=0;T<n.lines.length;T++){for(var I,A=n.lines[T],C=A.tokens,P=C.length,D=A.lineHeight,k=A.width,L=0,O=w,z=M,E=P-1;L<P&&(I=C[L],!I.textAlign||"left"===I.textAlign);)l(t,e,I,i,D,S,O,"left"),k-=I.width,O+=I.width,L++;for(;E>=0&&(I=C[E],"right"===I.textAlign);)l(t,e,I,i,D,S,z,"right"),k-=I.width,z-=I.width,E--;for(O+=(o-(O-w)-(M-z)-k)/2;L<=E;)I=C[L],l(t,e,I,i,D,S,O+I.width/2,"center"),O+=I.width,L++;S+=D}}function s(t,e,n,i,r){if(n&&e.textRotation){var o=e.textOrigin;"center"===o?(i=n.width/2+n.x,r=n.height/2+n.y):o&&(i=o[0]+n.x,r=o[1]+n.y),t.translate(i,r),t.rotate(-e.textRotation),t.translate(-i,-r)}}function l(t,e,n,i,r,o,a,s){var l=i.rich[n.styleName]||{},c=n.textVerticalAlign,d=o+r/2;"top"===c?d=o+n.height/2:"bottom"===c&&(d=o+r-n.height/2),!n.isLineHolder&&u(l)&&h(t,e,l,"right"===s?a-n.width:"center"===s?a-n.width/2:a,d-n.height/2,n.width,n.height);var p=n.textPadding;p&&(a=g(a,s,p),d-=n.height/2-p[2]-n.textHeight/2),f(e,"shadowBlur",_(l.textShadowBlur,i.textShadowBlur,0)),f(e,"shadowColor",l.textShadowColor||i.textShadowColor||"transparent"),f(e,"shadowOffsetX",_(l.textShadowOffsetX,i.textShadowOffsetX,0)),f(e,"shadowOffsetY",_(l.textShadowOffsetY,i.textShadowOffsetY,0)),f(e,"textAlign",s),f(e,"textBaseline","middle"),f(e,"font",n.font||m.DEFAULT_FONT);var v=T(l.textStroke||i.textStroke,y),x=I(l.textFill||i.textFill),y=b(l.textLineWidth,i.textLineWidth);v&&(f(e,"lineWidth",y),f(e,"strokeStyle",v),e.strokeText(n.text,a,d)),x&&(f(e,"fillStyle",x),e.fillText(n.text,a,d))}function u(t){return t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor}function h(t,e,n,i,r,o,a){var s=n.textBackgroundColor,l=n.textBorderWidth,u=n.textBorderColor,h=v.isString(s);if(f(e,"shadowBlur",n.textBoxShadowBlur||0),f(e,"shadowColor",n.textBoxShadowColor||"transparent"),f(e,"shadowOffsetX",n.textBoxShadowOffsetX||0),f(e,"shadowOffsetY",n.textBoxShadowOffsetY||0),h||l&&u){e.beginPath();var d=n.textBorderRadius;d?x.buildPath(e,{x:i,y:r,width:o,height:a,r:d}):e.rect(i,r,o,a),e.closePath()}if(h)f(e,"fillStyle",s),e.fill();else if(v.isObject(s)){var p=s.image;p=y.createOrUpdateImage(p,null,t,c,s),p&&y.isImageReady(p)&&e.drawImage(p,i,r,o,a)}l&&u&&(f(e,"lineWidth",l),f(e,"strokeStyle",u),e.stroke())}function c(t,e){e.image=t}function d(t,e,n){var i=e.x||0,r=e.y||0,o=e.textAlign,a=e.textVerticalAlign;if(n){var s=e.textPosition;if(s instanceof Array)i=n.x+p(s[0],n.width),r=n.y+p(s[1],n.height);else{var l=m.adjustTextPositionOnRect(s,n,e.textDistance);i=l.x,r=l.y,o=o||l.textAlign,a=a||l.textVerticalAlign}var u=e.textOffset;u&&(i+=u[0],r+=u[1])}return{baseX:i,baseY:r,textAlign:o,textVerticalAlign:a}}function f(t,e,n){return t[e]=t.__currentValues[e]=n,t[e]}function p(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function g(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}var m=n(16),v=n(1),x=n(78),y=n(53),_=v.retrieve3,b=v.retrieve2,w={left:1,right:1,center:1},S={top:1,bottom:1,middle:1},M={};M.normalizeTextStyle=function(t){return i(t),v.each(t.rich,i),t},M.renderText=function(t,e,n,i,a){i.rich?o(t,e,n,i,a):r(t,e,n,i,a)};var T=M.getStroke=function(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t},I=M.getFill=function(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t};M.needDrawText=function(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)},t.exports=M},function(t,e,n){function i(t){var e={componentType:t.mainType};return e[t.mainType+"Index"]=t.componentIndex,e}function r(t,e,n,i){var r,o,a=f(n-t.rotation),s=i[0]>i[1],l="start"===e&&!s||"start"!==e&&s;return p(a-y/2)?(o=l?"bottom":"top",r="center"):p(a-1.5*y)?(o=l?"top":"bottom",r="center"):(o="middle",r=a<1.5*y&&a>y/2?l?"left":"right":l?"right":"left"),{rotation:a,textAlign:r,textVerticalAlign:o}}function o(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)}function a(t,e){var n=t.get("axisLabel.showMinLabel"),i=t.get("axisLabel.showMaxLabel"),r=e[0],o=e[1],a=e[e.length-1],l=e[e.length-2];n===!1?r.ignore=!0:null!=t.getMin()&&s(r,o)&&(n?o.ignore=!0:r.ignore=!0),i===!1?a.ignore=!0:null!=t.getMax()&&s(l,a)&&(i?l.ignore=!0:a.ignore=!0)}function s(t,e,n){var i=t&&t.getBoundingRect().clone(),r=e&&e.getBoundingRect().clone();if(i&&r){var o=m.identity([]);return m.rotate(o,o,-t.rotation),i.applyTransform(m.mul([],o,t.getLocalTransform())),r.applyTransform(m.mul([],o,e.getLocalTransform())),i.intersect(r)}}var l=n(1),u=n(7),h=n(3),c=n(11),d=n(4),f=d.remRadian,p=d.isRadianAroundZero,g=n(6),m=n(19),v=g.applyTransform,x=l.retrieve,y=Math.PI,_=function(t,e){this.opt=e,this.axisModel=t,l.defaults(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new h.Group;var n=new h.Group({position:e.position.slice(),rotation:e.rotation});n.updateTransform(),this._transform=n.transform,this._dumbGroup=n};_.prototype={constructor:_,hasBuilder:function(t){return!!b[t]},add:function(t){b[t].call(this)},getGroup:function(){return this.group}};var b={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var n=this.axisModel.axis.getExtent(),i=this._transform,r=[n[0],0],o=[n[1],0];i&&(v(r,r,i),v(o,o,i)),this.group.add(new h.Line(h.subPixelOptimizeLine({anid:"line",shape:{x1:r[0],y1:r[1],x2:o[0],y2:o[1]},style:l.extend({lineCap:"round"},e.getModel("axisLine.lineStyle").getLineStyle()),strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1})))}},axisTick:function(){var t=this.axisModel,e=t.axis;if(t.get("axisTick.show")&&!e.scale.isBlank())for(var n=t.getModel("axisTick"),i=this.opt,r=n.getModel("lineStyle"),o=n.get("length"),a=M(n,i.labelInterval),s=e.getTicksCoords(n.get("alignWithLabel")),u=e.scale.getTicks(),c=[],d=[],f=this._transform,p=0;p<s.length;p++)if(!S(e,p,a)){var g=s[p];c[0]=g,c[1]=0,d[0]=g,d[1]=i.tickDirection*o,f&&(v(c,c,f),v(d,d,f)),this.group.add(new h.Line(h.subPixelOptimizeLine({anid:"tick_"+u[p],shape:{x1:c[0],y1:c[1],x2:d[0],y2:d[1]},style:l.defaults(r.getLineStyle(),{stroke:t.get("axisLine.lineStyle.color")}),z2:2,silent:!0})))}},axisLabel:function(){var t=this.opt,e=this.axisModel,n=e.axis,r=x(t.axisLabelShow,e.get("axisLabel.show"));if(r&&!n.scale.isBlank()){var s=e.getModel("axisLabel"),u=s.get("margin"),d=n.scale.getTicks(),f=e.getFormattedLabels(),p=(x(t.labelRotate,s.get("rotate"))||0)*y/180,g=w(t.rotation,p,t.labelDirection),m=e.get("data"),v=[],_=o(e),b=e.get("triggerEvent");l.each(d,function(r,o){if(!S(n,o,t.labelInterval)){var a=s;m&&m[r]&&m[r].textStyle&&(a=new c(m[r].textStyle,s,e.ecModel));var l=a.getTextColor()||e.get("axisLine.lineStyle.color"),d=n.dataToCoord(r),p=[d,t.labelOffset+t.labelDirection*u],x=n.scale.getLabel(r),y=new h.Text({anid:"label_"+r,position:p,rotation:g.rotation,silent:_,z2:10});h.setTextStyle(y.style,a,{text:f[o],textAlign:a.getShallow("align",!0)||g.textAlign,textVerticalAlign:a.getShallow("verticalAlign",!0)||a.getShallow("baseline",!0)||g.textVerticalAlign,textFill:"function"==typeof l?l("category"===n.type?x:"value"===n.type?r+"":r,o):l}),b&&(y.eventData=i(e),y.eventData.targetType="axisLabel",y.eventData.value=x),this._dumbGroup.add(y),y.updateTransform(),v.push(y),this.group.add(y),y.decomposeTransform()}},this),a(e,v)}},axisName:function(){var t=this.opt,e=this.axisModel,n=x(t.axisName,e.get("name"));if(n){var a,s=e.get("nameLocation"),c=t.nameDirection,d=e.getModel("nameTextStyle"),f=e.get("nameGap")||0,p=this.axisModel.axis.getExtent(),g=p[0]>p[1]?-1:1,m=["start"===s?p[0]-g*f:"end"===s?p[1]+g*f:(p[0]+p[1])/2,"middle"===s?t.labelOffset+c*f:0],v=e.get("nameRotate");null!=v&&(v=v*y/180);var _;"middle"===s?a=w(t.rotation,null!=v?v:t.rotation,c):(a=r(t,s,v||0,p),_=t.axisNameAvailableWidth,null!=_&&(_=Math.abs(_/Math.sin(a.rotation)),!isFinite(_)&&(_=null)));var b=d.getFont(),S=e.get("nameTruncate",!0)||{},M=S.ellipsis,T=x(t.nameTruncateMaxWidth,S.maxWidth,_),I=null!=M&&null!=T?u.truncateText(n,T,b,M,{minChar:2,placeholder:S.placeholder}):n,A=e.get("tooltip",!0),C=e.mainType,P={componentType:C,name:n,$vars:["name"]};P[C+"Index"]=e.componentIndex;var D=new h.Text({anid:"name",__fullText:n,__truncatedText:I,position:m,rotation:a.rotation,silent:o(e),z2:1,tooltip:A&&A.show?l.extend({content:n,formatter:function(){return n},formatterParams:P},A):null});h.setTextStyle(D.style,d,{text:I,textFont:b,textFill:d.getTextColor()||e.get("axisLine.lineStyle.color"),textAlign:a.textAlign,textVerticalAlign:a.textVerticalAlign}),e.get("triggerEvent")&&(D.eventData=i(e),D.eventData.targetType="axisName",D.eventData.name=n),this._dumbGroup.add(D),D.updateTransform(),this.group.add(D),D.decomposeTransform()}}},w=_.innerTextLayout=function(t,e,n){var i,r,o=f(e-t);return p(o)?(r=n>0?"top":"bottom",i="center"):p(o-y)?(r=n>0?"bottom":"top",i="center"):(r="middle",i=o>0&&o<y?n>0?"right":"left":n>0?"left":"right"),{rotation:o,textAlign:i,textVerticalAlign:r}},S=_.ifIgnoreOnTick=function(t,e,n){var i,r=t.scale;return"ordinal"===r.type&&("function"==typeof n?(i=r.getTicks()[e],!n(i,r.getLabel(i))):e%(n+1))},M=_.getInterval=function(t,e){var n=t.get("interval");return null!=n&&"auto"!=n||(n=e),n};t.exports=_},function(t,e,n){function i(t,e,n,i,s,l){var u=a.getAxisPointerClass(t.axisPointerClass);if(u){var h=o.getAxisPointerModel(e);h?(t._axisPointer||(t._axisPointer=new u)).render(e,h,i,l):r(t,i)}}function r(t,e,n){var i=t._axisPointer;i&&i.dispose(e,n),t._axisPointer=null}var o=n(47),a=n(2).extendComponentView({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,n,r){this.axisPointerClass&&o.fixValue(t),a.superApply(this,"render",arguments),i(this,t,e,n,r,!0)},updateAxisPointer:function(t,e,n,r,o){i(this,t,e,n,r,!1)},remove:function(t,e){var n=this._axisPointer;n&&n.remove(e),a.superApply(this,"remove",arguments)},dispose:function(t,e){r(this,e),a.superApply(this,"dispose",arguments)}}),s=[];a.registerAxisPointerClass=function(t,e){s[t]=e},a.getAxisPointerClass=function(t){return t&&s[t]},t.exports=a},function(t,e,n){function i(t){return r.isObject(t)&&null!=t.value?t.value:t+""}var r=n(1),o=n(18);t.exports={getFormattedLabels:function(){return o.getFormattedLabels(this.axis,this.get("axisLabel.formatter"))},getCategories:function(){return"category"===this.get("type")&&r.map(this.get("data"),i)},getMin:function(t){var e=this.option,n=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=n&&"dataMin"!==n&&"function"!=typeof n&&!r.eqNaN(n)&&(n=this.axis.scale.parse(n)),n},getMax:function(t){var e=this.option,n=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=n&&"dataMax"!==n&&"function"!=typeof n&&!r.eqNaN(n)&&(n=this.axis.scale.parse(n)),n},getNeedCrossZero:function(){var t=this.option;return null==t.rangeStart&&null==t.rangeEnd&&!t.scale},getCoordSysModel:r.noop,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}}},function(t,e){"use strict";function n(t){return t}function i(t,e,i,r,o){this._old=t,this._new=e,this._oldKeyGetter=i||n,this._newKeyGetter=r||n,this.context=o}function r(t,e,n,i,r){for(var o=0;o<t.length;o++){var a="_ec_"+r[i](t[o],o),s=e[a];null==s?(n.push(a),e[a]=o):(s.length||(e[a]=s=[s]),s.push(o))}}i.prototype={constructor:i,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t,e=this._old,n=this._new,i={},o={},a=[],s=[];for(r(e,i,a,"_oldKeyGetter",this),r(n,o,s,"_newKeyGetter",this),t=0;t<e.length;t++){var l=a[t],u=o[l];if(null!=u){var h=u.length;h?(1===h&&(o[l]=null),u=u.unshift()):o[l]=null,this._update&&this._update(u,t)}else this._remove&&this._remove(t)}for(var t=0;t<s.length;t++){var l=s[t];if(o.hasOwnProperty(l)){var u=o[l];if(null==u)continue;if(u.length)for(var c=0,h=u.length;c<h;c++)this._add&&this._add(u[c]);else this._add&&this._add(u)}}}},t.exports=i},function(t,e,n){var i=n(4),r=n(7),o=n(34),a=n(66),s=i.round,l=o.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),l.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=a.getIntervalPrecision(t)},getTicks:function(){return a.intervalScaleGetTicks(this._interval,this._extent,this._niceExtent,this._intervalPrecision)},getTicksLabels:function(){for(var t=[],e=this.getTicks(),n=0;n<e.length;n++)t.push(this.getLabel(e[n]));return t},getLabel:function(t,e){if(null==t)return"";var n=e&&e.precision;return null==n?n=i.getPrecisionSafe(t)||0:"auto"===n&&(n=this._intervalPrecision),t=s(t,n,!0),r.addCommas(t)},niceTicks:function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];if(isFinite(r)){r<0&&(r=-r,i.reverse());var o=a.intervalScaleNiceTicks(i,t,e,n);this._intervalPrecision=o.intervalPrecision,this._interval=o.interval,this._niceExtent=o.niceTickExtent}},niceExtent:function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var n=e[0];t.fixMax?e[0]-=n/2:(e[1]+=n/2,e[0]-=n/2)}else e[1]=1;var i=e[1]-e[0];isFinite(i)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=s(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=s(Math.ceil(e[1]/r)*r))}});l.create=function(){return new l},t.exports=l},function(t,e,n){function i(t){this.group=new o.Group,this._symbolCtor=t||a}function r(t,e,n){var i=t.getItemLayout(e);return i&&!isNaN(i[0])&&!isNaN(i[1])&&!(n&&n(e))&&"none"!==t.getItemVisual(e,"symbol")}var o=n(3),a=n(56),s=i.prototype;s.updateData=function(t,e){var n=this.group,i=t.hostModel,a=this._data,s=this._symbolCtor,l={itemStyle:i.getModel("itemStyle.normal").getItemStyle(["color"]),hoverItemStyle:i.getModel("itemStyle.emphasis").getItemStyle(),symbolRotate:i.get("symbolRotate"),symbolOffset:i.get("symbolOffset"),hoverAnimation:i.get("hoverAnimation"),labelModel:i.getModel("label.normal"),hoverLabelModel:i.getModel("label.emphasis"),cursorStyle:i.get("cursor")};t.diff(a).add(function(i){var o=t.getItemLayout(i);if(r(t,i,e)){var a=new s(t,i,l);a.attr("position",o),t.setItemGraphicEl(i,a),n.add(a)}}).update(function(u,h){var c=a.getItemGraphicEl(h),d=t.getItemLayout(u);return r(t,u,e)?(c?(c.updateData(t,u,l),o.updateProps(c,{position:d},i)):(c=new s(t,u),c.attr("position",d)),n.add(c),void t.setItemGraphicEl(u,c)):void n.remove(c)}).remove(function(t){var e=a.getItemGraphicEl(t);e&&e.fadeOut(function(){n.remove(e)})}).execute(),this._data=t},s.updateLayout=function(){var t=this._data;t&&t.eachItemGraphicEl(function(e,n){var i=t.getItemLayout(n);e.attr("position",i)})},s.remove=function(t){var e=this.group,n=this._data;n&&(t?n.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)})}):e.removeAll())},t.exports=i},function(t,e,n){function i(t,e,n){var i=e.getComponent("tooltip"),o=e.getComponent("axisPointer"),s=o.get("link",!0)||[],u=[];c(n.getCoordinateSystems(),function(n){function h(i,h,c){var d=c.model.getModel("axisPointer",o),f=d.get("show");if(f&&("auto"!==f||i||l(d))){null==h&&(h=d.get("triggerTooltip")),d=i?r(c,v,o,e,i,h):d;var m=d.get("snap"),x=p(c.model),y=h||m||"category"===c.type,_=t.axesInfo[x]={key:x,axis:c,coordSys:n,axisPointerModel:d,triggerTooltip:h,involveSeries:y,snap:m,useHandle:l(d),seriesModels:[]};g[x]=_,t.seriesInvolved|=y;var b=a(s,c);if(null!=b){var w=u[b]||(u[b]={axesInfo:{}});w.axesInfo[x]=_,w.mapper=s[b].mapper,_.linkGroup=w}}}if(n.axisPointerEnabled){var f=p(n.model),g=t.coordSysAxesInfo[f]={};t.coordSysMap[f]=n;var m=n.model,v=m.getModel("tooltip",i);if(c(n.getAxes(),d(h,!1,null)),n.getTooltipAxes&&i&&v.get("show")){var x="axis"===v.get("trigger"),y="cross"===v.get("axisPointer.type"),_=n.getTooltipAxes(v.get("axisPointer.axis"));(x||y)&&c(_.baseAxes,d(h,!y||"cross",x)),y&&c(_.otherAxes,d(h,"cross",!1))}}})}function r(t,e,n,i,r,o){var a=e.getModel("axisPointer"),s={};c(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){s[t]=u.clone(a.get(t))}),s.snap="category"!==t.type&&!!o,"cross"===a.get("type")&&(s.type="line");var l=s.label||(s.label={});if(null==l.show&&(l.show=!1),"cross"===r&&(l.show=!0,!o)){var d=s.lineStyle=a.get("crossStyle");d&&u.defaults(l,d.textStyle)}return t.model.getModel("axisPointer",new h(s,n,i))}function o(t,e){e.eachSeries(function(e){var n=e.coordinateSystem,i=e.get("tooltip.trigger",!0),r=e.get("tooltip.show",!0);n&&"none"!==i&&i!==!1&&"item"!==i&&r!==!1&&e.get("axisPointer.show",!0)!==!1&&c(t.coordSysAxesInfo[p(n.model)],function(t){var i=t.axis;n.getAxis(i.dim)===i&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())})},this)}function a(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var o=t[r]||{};if(s(o[i+"AxisId"],n.id)||s(o[i+"AxisIndex"],n.componentIndex)||s(o[i+"AxisName"],n.name))return r}}function s(t,e){return"all"===t||u.isArray(t)&&u.indexOf(t,e)>=0||t===e}function l(t){return!!t.get("handle.show")}var u=n(1),h=n(11),c=u.each,d=u.curry,f={};f.collect=function(t,e){var n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return i(n,t,e),n.seriesInvolved&&o(n,t),n},f.fixValue=function(t){var e=f.getAxisInfo(t);if(e){var n=e.axisPointerModel,i=e.axis.scale,r=n.option,o=n.get("status"),a=n.get("value");null!=a&&(a=i.parse(a));var s=l(n);null==o&&(r.status=s?"show":"hide");var u=i.getExtent().slice();u[0]>u[1]&&u.reverse(),(null==a||a>u[1])&&(a=u[1]),a<u[0]&&(a=u[0]),r.value=a,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}},f.getAxisInfo=function(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[p(t)]},f.getAxisPointerModel=function(t){var e=f.getAxisInfo(t);return e&&e.axisPointerModel};var p=f.makeKey=function(t){return t.type+"||"+t.id};t.exports=f},function(t,e,n){function i(t){var e={};return c(["start","end","startValue","endValue","throttle"],function(n){t.hasOwnProperty(n)&&(e[n]=t[n])}),e}function r(t,e){var n=t._rangePropMode,i=t.get("rangeMode");c([["start","startValue"],["end","endValue"]],function(t,r){var o=null!=e[t[0]],a=null!=e[t[1]];o&&!a?n[r]="percent":!o&&a?n[r]="value":i?n[r]=i[r]:o&&(n[r]="percent")})}var o=n(1),a=n(10),s=n(2),l=n(5),u=n(81),h=n(203),c=o.each,d=u.eachAxisDim,f=s.extendComponentModel({type:"dataZoom",dependencies:["xAxis","yAxis","zAxis","radiusAxis","angleAxis","singleAxis","series"],defaultOption:{zlevel:0,z:4,orient:null,xAxisIndex:null,yAxisIndex:null,filterMode:"filter",throttle:null,start:0,end:100,startValue:null,endValue:null,minSpan:null,maxSpan:null,minValueSpan:null,maxValueSpan:null,rangeMode:null},init:function(t,e,n){this._dataIntervalByAxis={},this._dataInfo={},this._axisProxies={},this.textStyleModel,this._autoThrottle=!0,this._rangePropMode=["percent","percent"];var r=i(t);this.mergeDefaultAndTheme(t,n),this.doInit(r)},mergeOption:function(t){var e=i(t);o.merge(this.option,t,!0),this.doInit(e)},doInit:function(t){var e=this.option;a.canvasSupported||(e.realtime=!1),this._setDefaultThrottle(t),r(this,t),c([["start","startValue"],["end","endValue"]],function(t,n){"value"===this._rangePropMode[n]&&(e[t[0]]=null)},this),this.textStyleModel=this.getModel("textStyle"),this._resetTarget(),this._giveAxisProxies()},_giveAxisProxies:function(){var t=this._axisProxies;this.eachTargetAxis(function(e,n,i,r){var o=this.dependentModels[e.axis][n],a=o.__dzAxisProxy||(o.__dzAxisProxy=new h(e.name,n,this,r));t[e.name+"_"+n]=a},this)},_resetTarget:function(){var t=this.option,e=this._judgeAutoMode();d(function(e){var n=e.axisIndex;t[n]=l.normalizeToArray(t[n])},this),"axisIndex"===e?this._autoSetAxisIndex():"orient"===e&&this._autoSetOrient()},_judgeAutoMode:function(){var t=this.option,e=!1;d(function(n){null!=t[n.axisIndex]&&(e=!0)},this);var n=t.orient;return null==n&&e?"orient":e?void 0:(null==n&&(t.orient="horizontal"),"axisIndex")},_autoSetAxisIndex:function(){var t=!0,e=this.get("orient",!0),n=this.option,i=this.dependentModels;if(t){var r="vertical"===e?"y":"x";i[r+"Axis"].length?(n[r+"AxisIndex"]=[0],t=!1):c(i.singleAxis,function(i){t&&i.get("orient",!0)===e&&(n.singleAxisIndex=[i.componentIndex],t=!1)})}t&&d(function(e){if(t){var i=[],r=this.dependentModels[e.axis];if(r.length&&!i.length)for(var o=0,a=r.length;o<a;o++)"category"===r[o].get("type")&&i.push(o);n[e.axisIndex]=i,i.length&&(t=!1)}},this),t&&this.ecModel.eachSeries(function(t){this._isSeriesHasAllAxesTypeOf(t,"value")&&d(function(e){var i=n[e.axisIndex],r=t.get(e.axisIndex),a=t.get(e.axisId),s=t.ecModel.queryComponents({mainType:e.axis,index:r,id:a})[0];r=s.componentIndex,o.indexOf(i,r)<0&&i.push(r)})},this)},_autoSetOrient:function(){var t;this.eachTargetAxis(function(e){!t&&(t=e.name)},this),this.option.orient="y"===t?"vertical":"horizontal"},_isSeriesHasAllAxesTypeOf:function(t,e){var n=!0;return d(function(i){var r=t.get(i.axisIndex),o=this.dependentModels[i.axis][r];o&&o.get("type")===e||(n=!1)},this),n},_setDefaultThrottle:function(t){if(t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var e=this.ecModel.option;this.option.throttle=e.animation&&e.animationDurationUpdate>0?100:20}},getFirstTargetAxisModel:function(){var t;return d(function(e){if(null==t){var n=this.get(e.axisIndex);n.length&&(t=this.dependentModels[e.axis][n[0]])}},this),t},eachTargetAxis:function(t,e){var n=this.ecModel;d(function(i){c(this.get(i.axisIndex),function(r){t.call(e,i,r,this,n)},this)},this)},getAxisProxy:function(t,e){return this._axisProxies[t+"_"+e]},getAxisModel:function(t,e){var n=this.getAxisProxy(t,e);return n&&n.getAxisModel()},setRawRange:function(t,e){c(["start","end","startValue","endValue"],function(e){this.option[e]=t[e]},this),!e&&r(this,t)},getPercentRange:function(){var t=this.findRepresentativeAxisProxy();if(t)return t.getDataPercentWindow()},getValueRange:function(t,e){if(null!=t||null!=e)return this.getAxisProxy(t,e).getDataValueWindow();var n=this.findRepresentativeAxisProxy();return n?n.getDataValueWindow():void 0},findRepresentativeAxisProxy:function(t){if(t)return t.__dzAxisProxy;var e=this._axisProxies;for(var n in e)if(e.hasOwnProperty(n)&&e[n].hostedBy(this))return e[n];for(var n in e)if(e.hasOwnProperty(n)&&!e[n].hostedBy(this))return e[n]},getRangePropMode:function(){return this._rangePropMode.slice()}});t.exports=f},function(t,e,n){var i=n(67);t.exports=i.extend({type:"dataZoom",render:function(t,e,n,i){this.dataZoomModel=t,this.ecModel=e,this.api=n},getTargetCoordInfo:function(){function t(t,e,n,i){for(var r,o=0;o<n.length;o++)if(n[o].model===t){r=n[o];break}r||n.push(r={model:t,axisModels:[],coordIndex:i}),r.axisModels.push(e)}var e=this.dataZoomModel,n=this.ecModel,i={};return e.eachTargetAxis(function(e,r){var o=n.getComponent(e.axis,r);if(o){var a=o.getCoordSysModel();a&&t(a,o,i[a.mainType]||(i[a.mainType]=[]),a.componentIndex)}},this),i}})},function(t,e,n){var i=n(1),r=n(15),o=r.parseClassType,a=0,s={},l="_";s.getUID=function(t){return[t||"",a++,Math.random()].join(l)},s.enableSubTypeDefaulter=function(t){var e={};return t.registerSubTypeDefaulter=function(t,n){t=o(t),e[t.main]=n},t.determineSubType=function(n,i){var r=i.type;if(!r){var a=o(n).main;t.hasSubTypes(n)&&e[a]&&(r=e[a](i))}return r},t},s.enableTopologicalTravel=function(t,e){function n(t){var n={},a=[];return i.each(t,function(s){var l=r(n,s),u=l.originalDeps=e(s),h=o(u,t);l.entryCount=h.length,0===l.entryCount&&a.push(s),i.each(h,function(t){i.indexOf(l.predecessor,t)<0&&l.predecessor.push(t);var e=r(n,t);i.indexOf(e.successor,t)<0&&e.successor.push(s)})}),{graph:n,noEntryList:a}}function r(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function o(t,e){var n=[];return i.each(t,function(t){i.indexOf(e,t)>=0&&n.push(t)}),n}t.topologicalTravel=function(t,e,r,o){function a(t){u[t].entryCount--,0===u[t].entryCount&&h.push(t)}function s(t){c[t]=!0,a(t)}if(t.length){var l=n(e),u=l.graph,h=l.noEntryList,c={};for(i.each(t,function(t){c[t]=!0});h.length;){var d=h.pop(),f=u[d],p=!!c[d];p&&(r.call(o,d,f.originalDeps.slice()),delete c[d]),i.each(f.successor,p?s:a)}i.each(c,function(){throw new Error("Circle dependency may exists")})}}},t.exports=s},function(t,e){t.exports=function(t,e,n,i,r){i.eachRawSeriesByType(t,function(t){var r=t.getData(),o=t.get("symbol")||e,a=t.get("symbolSize");r.setVisual({legendSymbol:n||o,symbol:o,symbolSize:a}),i.isSeriesFiltered(t)||("function"==typeof a&&r.each(function(e){var n=t.getRawValue(e),i=t.getDataParams(e);r.setItemVisual(e,"symbolSize",a(n,i))}),r.each(function(t){var e=r.getItemModel(t),n=e.getShallow("symbol",!0),i=e.getShallow("symbolSize",!0);null!=n&&r.setItemVisual(t,"symbol",n),null!=i&&r.setItemVisual(t,"symbolSize",i)}))})}},function(t,e){function n(t){for(var e=0;t>=h;)e|=1&t,t>>=1;return t+e}function i(t,e,n,i){var o=e+1;if(o===n)return 1;if(i(t[o++],t[e])<0){for(;o<n&&i(t[o],t[o-1])<0;)o++;r(t,e,o)}else for(;o<n&&i(t[o],t[o-1])>=0;)o++;return o-e}function r(t,e,n){for(n--;e<n;){var i=t[e];t[e++]=t[n],t[n--]=i}}function o(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)o=s+l>>>1,r(a,t[o])<0?l=o:s=o+1;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;u>0;)t[s+u]=t[s+u-1],u--}t[s]=a}}function a(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])>0){for(s=i-r;l<s&&o(t,e[n+r+l])>0;)a=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)a=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s);var u=a;a=r-l,l=r-u}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])>0?a=h+1:l=h}return l}function s(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)a=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s);var u=a;a=r-l,l=r-u}else{for(s=i-r;l<s&&o(t,e[n+r+l])>=0;)a=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function l(t,e){function n(t,e){h[x]=t,f[x]=e,x+=1}function i(){for(;x>1;){var t=x-2;if(t>=1&&f[t-1]<=f[t]+f[t+1]||t>=2&&f[t-2]<=f[t]+f[t-1])f[t-1]<f[t+1]&&t--;else if(f[t]>f[t+1])break;o(t)}}function r(){for(;x>1;){var t=x-2;t>0&&f[t-1]<f[t+1]&&t--,o(t)}}function o(n){var i=h[n],r=f[n],o=h[n+1],c=f[n+1];f[n]=r+c,n===x-3&&(h[n+1]=h[n+2],f[n+1]=f[n+2]),x--;var d=s(t[o],t,i,r,0,e);i+=d,r-=d,0!==r&&(c=a(t[i+r-1],t,o,c,c-1,e),0!==c&&(r<=c?l(i,r,o,c):u(i,r,o,c)))}function l(n,i,r,o){var l=0;for(l=0;l<i;l++)y[l]=t[n+l];
var u=0,h=r,d=n;if(t[d++]=t[h++],0!==--o){if(1===i){for(l=0;l<o;l++)t[d+l]=t[h+l];return void(t[d+o]=y[u])}for(var f,g,m,v=p;;){f=0,g=0,m=!1;do if(e(t[h],y[u])<0){if(t[d++]=t[h++],g++,f=0,0===--o){m=!0;break}}else if(t[d++]=y[u++],f++,g=0,1===--i){m=!0;break}while((f|g)<v);if(m)break;do{if(f=s(t[h],y,u,i,0,e),0!==f){for(l=0;l<f;l++)t[d+l]=y[u+l];if(d+=f,u+=f,i-=f,i<=1){m=!0;break}}if(t[d++]=t[h++],0===--o){m=!0;break}if(g=a(y[u],t,h,o,0,e),0!==g){for(l=0;l<g;l++)t[d+l]=t[h+l];if(d+=g,h+=g,o-=g,0===o){m=!0;break}}if(t[d++]=y[u++],1===--i){m=!0;break}v--}while(f>=c||g>=c);if(m)break;v<0&&(v=0),v+=2}if(p=v,p<1&&(p=1),1===i){for(l=0;l<o;l++)t[d+l]=t[h+l];t[d+o]=y[u]}else{if(0===i)throw new Error;for(l=0;l<i;l++)t[d+l]=y[u+l]}}else for(l=0;l<i;l++)t[d+l]=y[u+l]}function u(n,i,r,o){var l=0;for(l=0;l<o;l++)y[l]=t[r+l];var u=n+i-1,h=o-1,d=r+o-1,f=0,g=0;if(t[d--]=t[u--],0!==--i){if(1===o){for(d-=i,u-=i,g=d+1,f=u+1,l=i-1;l>=0;l--)t[g+l]=t[f+l];return void(t[d]=y[h])}for(var m=p;;){var v=0,x=0,_=!1;do if(e(y[h],t[u])<0){if(t[d--]=t[u--],v++,x=0,0===--i){_=!0;break}}else if(t[d--]=y[h--],x++,v=0,1===--o){_=!0;break}while((v|x)<m);if(_)break;do{if(v=i-s(y[h],t,n,i,i-1,e),0!==v){for(d-=v,u-=v,i-=v,g=d+1,f=u+1,l=v-1;l>=0;l--)t[g+l]=t[f+l];if(0===i){_=!0;break}}if(t[d--]=y[h--],1===--o){_=!0;break}if(x=o-a(t[u],y,0,o,o-1,e),0!==x){for(d-=x,h-=x,o-=x,g=d+1,f=h+1,l=0;l<x;l++)t[g+l]=y[f+l];if(o<=1){_=!0;break}}if(t[d--]=t[u--],0===--i){_=!0;break}m--}while(v>=c||x>=c);if(_)break;m<0&&(m=0),m+=2}if(p=m,p<1&&(p=1),1===o){for(d-=i,u-=i,g=d+1,f=u+1,l=i-1;l>=0;l--)t[g+l]=t[f+l];t[d]=y[h]}else{if(0===o)throw new Error;for(f=d-(o-1),l=0;l<o;l++)t[f+l]=y[l]}}else for(f=d-(o-1),l=0;l<o;l++)t[f+l]=y[l]}var h,f,p=c,g=0,m=d,v=0,x=0;g=t.length,g<2*d&&(m=g>>>1);var y=[];v=g<120?5:g<1542?10:g<119151?19:40,h=[],f=[],this.mergeRuns=i,this.forceMergeRuns=r,this.pushRun=n}function u(t,e,r,a){r||(r=0),a||(a=t.length);var s=a-r;if(!(s<2)){var u=0;if(s<h)return u=i(t,r,a,e),void o(t,r,a,r+u,e);var c=new l(t,e),d=n(s);do{if(u=i(t,r,a,e),u<d){var f=s;f>d&&(f=d),o(t,r,r+f,r+u,e),u=f}c.pushRun(r,u),c.mergeRuns(),s-=u,r+=u}while(0!==s);c.forceMergeRuns()}}var h=32,c=7,d=256;t.exports=u},function(t,e,n){function i(){var t=this.__cachedImgObj;this.onload=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}var r=n(72),o=new r(50),a={};a.findExistImage=function(t){if("string"==typeof t){var e=o.get(t);return e&&e.image}return t},a.createOrUpdateImage=function(t,e,n,r,a){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!n)return e;var l=o.get(t),u={hostEl:n,cb:r,cbPayload:a};return l?(e=l.image,!s(e)&&l.pending.push(u)):(!e&&(e=new Image),e.onload=i,o.put(t,e.__cachedImgObj={image:e,pending:[u]}),e.src=e.__zrImageSrc=t),e}return t}return e};var s=a.isImageReady=function(t){return t&&t.width&&t.height};t.exports=a},function(t,e,n){var i=n(35);t.exports=function(){if(0!==i.debugMode)if(1==i.debugMode)for(var t in arguments)throw new Error(arguments[t]);else if(i.debugMode>1)for(var t in arguments)console.log(arguments[t])}},function(t,e,n){function i(t){r.call(this,t)}var r=n(38),o=n(12),a=n(1),s=n(53);i.prototype={constructor:i,type:"image",brush:function(t,e){var n=this.style,i=n.image;n.bind(t,this,e);var r=this._image=s.createOrUpdateImage(i,this._image,this);if(r&&s.isImageReady(r)){var o=n.x||0,a=n.y||0,l=n.width,u=n.height,h=r.width/r.height;if(null==l&&null!=u?l=u*h:null==u&&null!=l?u=l/h:null==l&&null==u&&(l=r.width,u=r.height),this.setTransform(t),n.sWidth&&n.sHeight){var c=n.sx||0,d=n.sy||0;t.drawImage(r,c,d,n.sWidth,n.sHeight,o,a,l,u)}else if(n.sx&&n.sy){var c=n.sx,d=n.sy,f=l-c,p=u-d;t.drawImage(r,c,d,f,p,o,a,l,u)}else t.drawImage(r,o,a,l,u);this.restoreTransform(t),null!=n.text&&this.drawRectText(t,this.getBoundingRect())}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new o(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},a.inherits(i,r),t.exports=i},function(t,e,n){function i(t,e){var n=t.getItemVisual(e,"symbolSize");return n instanceof Array?n.slice():[+n,+n]}function r(t){return[t[0]/2,t[1]/2]}function o(t,e,n){u.Group.call(this),this.updateData(t,e,n)}function a(t,e){this.parent.drift(t,e)}var s=n(1),l=n(24),u=n(3),h=n(4),c=n(96),d=o.prototype;d._createSymbol=function(t,e,n,i){this.removeAll();var o=e.hostModel,s=e.getItemVisual(n,"color"),h=l.createSymbol(t,-1,-1,2,2,s);h.attr({z2:100,culling:!0,scale:[0,0]}),h.drift=a,u.initProps(h,{scale:r(i)},o,n),this._symbolType=t,this.add(h)},d.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},d.getSymbolPath=function(){return this.childAt(0)},d.getScale=function(){return this.childAt(0).scale},d.highlight=function(){this.childAt(0).trigger("emphasis")},d.downplay=function(){this.childAt(0).trigger("normal")},d.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},d.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":"pointer"},d.updateData=function(t,e,n){this.silent=!1;var o=t.getItemVisual(e,"symbol")||"circle",a=t.hostModel,s=i(t,e);if(o!==this._symbolType)this._createSymbol(o,t,e,s);else{var l=this.childAt(0);l.silent=!1,u.updateProps(l,{scale:r(s)},a,e)}this._updateCommon(t,e,s,n),this._seriesModel=a};var f=["itemStyle","normal"],p=["itemStyle","emphasis"],g=["label","normal"],m=["label","emphasis"];d._updateCommon=function(t,e,n,i){var o=this.childAt(0),a=t.hostModel,l=t.getItemVisual(e,"color");"image"!==o.type&&o.useStyle({strokeNoScale:!0}),i=i||null;var d=i&&i.itemStyle,v=i&&i.hoverItemStyle,x=i&&i.symbolRotate,y=i&&i.symbolOffset,_=i&&i.labelModel,b=i&&i.hoverLabelModel,w=i&&i.hoverAnimation,S=i&&i.cursorStyle;if(!i||t.hasItemOption){var M=t.getItemModel(e);d=M.getModel(f).getItemStyle(["color"]),v=M.getModel(p).getItemStyle(),x=M.getShallow("symbolRotate"),y=M.getShallow("symbolOffset"),_=M.getModel(g),b=M.getModel(m),w=M.getShallow("hoverAnimation"),S=M.getShallow("cursor")}else v=s.extend({},v);var T=o.style;o.attr("rotation",(x||0)*Math.PI/180||0),y&&o.attr("position",[h.parsePercent(y[0],n[0]),h.parsePercent(y[1],n[1])]),S&&o.attr("cursor",S),o.setColor(l),o.setStyle(d);var I=t.getItemVisual(e,"opacity");null!=I&&(T.opacity=I);var A=c.findLabelValueDim(t);null!=A&&u.setLabelStyle(T,v,_,b,{labelFetcher:a,labelDataIndex:e,defaultText:t.get(A,e),isRectText:!0,autoColor:l}),o.off("mouseover").off("mouseout").off("emphasis").off("normal"),o.hoverStyle=v,u.setHoverStyle(o);var C=r(n);if(w&&a.isAnimationEnabled()){var P=function(){var t=C[1]/C[0];this.animateTo({scale:[Math.max(1.1*C[0],C[0]+3),Math.max(1.1*C[1],C[1]+3*t)]},400,"elasticOut")},D=function(){this.animateTo({scale:C},400,"elasticOut")};o.on("mouseover",P).on("mouseout",D).on("emphasis",P).on("normal",D)}},d.fadeOut=function(t){var e=this.childAt(0);this.silent=e.silent=!0,e.style.text=null,u.updateProps(e,{scale:[0,0]},this._seriesModel,this.dataIndex,t)},s.inherits(o,u.Group),t.exports=o},function(t,e,n){var i=n(2),r=n(47),o=n(201),a=n(1);n(199),n(200),n(124),i.registerPreprocessor(function(t){if(t){(!t.axisPointer||0===t.axisPointer.length)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!a.isArray(e)&&(t.axisPointer.link=[e])}}),i.registerProcessor(i.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=r.collect(t,e)}),i.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},o)},function(t,e){function n(t,e){var n=t[e]-t[1-e];return{span:Math.abs(n),sign:n>0?-1:n<0?1:e?-1:1}}function i(t,e){return Math.min(e[1],Math.max(e[0],t))}t.exports=function(t,e,r,o,a,s){e[0]=i(e[0],r),e[1]=i(e[1],r),t=t||0;var l=r[1]-r[0];null!=a&&(a=i(a,[0,l])),null!=s&&(s=Math.max(s,null!=a?a:0)),"all"===o&&(a=s=Math.abs(e[1]-e[0]),o=0);var u=n(e,o);e[o]+=t;var h=a||0,c=r.slice();u.sign<0?c[0]+=h:c[1]-=h,e[o]=i(e[o],c);var d=n(e,o);null!=a&&(d.sign!==u.sign||d.span<a)&&(e[1-o]=e[o]+u.sign*a);var d=n(e,o);return null!=s&&d.span>s&&(e[1-o]=e[o]+d.sign*s),e}},function(t,e,n){function i(t,e,n){return t.getCoordSysModel()===e}function r(t){var e,n=t.model,i=n.getFormattedLabels(),r=n.getModel("axisLabel"),o=1,a=i.length;a>40&&(o=Math.ceil(a/40));for(var s=0;s<a;s+=o)if(!t.isLabelIgnored(s)){var l=r.getTextRect(i[s]);e?e.union(l):e=l}return e}function o(t,e,n){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,n),this.model=t}function a(t,e,n){var i=t[e];if(n.onZero){var r=n.onZeroAxisIndex;if(null!=r){var o=i[r];return void(o&&s(o)&&(n.onZero=!1))}for(var a in i)if(i.hasOwnProperty(a)){var o=i[a];if(o&&!s(o)){r=+a;break}}null==r&&(n.onZero=!1),n.onZeroAxisIndex=r}}function s(t){return"category"===t.type||"time"===t.type||!v(t)}function l(t,e){var n=t.getExtent(),i=n[0]+n[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return i-t+e}}function u(t,e){return f.map(_,function(e){var n=t.getReferringComponents(e)[0];return n})}function h(t){return"cartesian2d"===t.get("coordinateSystem")}var c=n(9),d=n(18),f=n(1),p=n(139),g=n(137),m=f.each,v=d.ifAxisCrossZero,x=d.niceScaleExtent;n(140);var y=o.prototype;y.type="grid",y.axisPointerEnabled=!0,y.getRect=function(){return this._rect},y.update=function(t,e){var n=this._axesMap;this._updateScale(t,this.model),m(n.x,function(t){x(t.scale,t.model)}),m(n.y,function(t){x(t.scale,t.model)}),m(n.x,function(t){a(n,"y",t)}),m(n.y,function(t){a(n,"x",t)}),this.resize(this.model,e)},y.resize=function(t,e,n){function i(){m(a,function(t){var e=t.isHorizontal(),n=e?[0,o.width]:[0,o.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),l(t,e?o.x:o.y)})}var o=c.getLayoutRect(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=o;var a=this._axesList;i(),!n&&t.get("containLabel")&&(m(a,function(t){if(!t.model.get("axisLabel.inside")){var e=r(t);if(e){var n=t.isHorizontal()?"height":"width",i=t.model.get("axisLabel.margin");o[n]-=e[n]+i,"top"===t.position?o.y+=e.height+i:"left"===t.position&&(o.x+=e.width+i)}}}),i())},y.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n){if(null==e)for(var i in n)if(n.hasOwnProperty(i))return n[i];return n[e]}},y.getAxes=function(){return this._axesList.slice()},y.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}f.isObject(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,r=this._coordsList;i<r.length;i++)if(r[i].getAxis("x").index===t||r[i].getAxis("y").index===e)return r[i]},y.getCartesians=function(){return this._coordsList.slice()},y.convertToPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},y.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},y._findConvertTarget=function(t,e){var n,i,r=e.seriesModel,o=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],a=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,l=this._coordsList;if(r)n=r.coordinateSystem,f.indexOf(l,n)<0&&(n=null);else if(o&&a)n=this.getCartesian(o.componentIndex,a.componentIndex);else if(o)i=this.getAxis("x",o.componentIndex);else if(a)i=this.getAxis("y",a.componentIndex);else if(s){var u=s.coordinateSystem;u===this&&(n=this._coordsList[0])}return{cartesian:n,axis:i}},y.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},y._initCartesian=function(t,e,n){function r(n){return function(r,l){if(i(r,t,e)){var u=r.get("position");"x"===n?"top"!==u&&"bottom"!==u&&(u="bottom",o[u]&&(u="top"===u?"bottom":"top")):"left"!==u&&"right"!==u&&(u="left",o[u]&&(u="left"===u?"right":"left")),o[u]=!0;var h=new g(n,d.createScaleByModel(r),[0,0],r.get("type"),u),c="category"===h.type;h.onBand=c&&r.get("boundaryGap"),h.inverse=r.get("inverse"),h.onZero=r.get("axisLine.onZero"),h.onZeroAxisIndex=r.get("axisLine.onZeroAxisIndex"),r.axis=h,h.model=r,h.grid=this,h.index=l,this._axesList.push(h),a[n][l]=h,s[n]++}}}var o={left:!1,right:!1,top:!1,bottom:!1},a={x:{},y:{}},s={x:0,y:0};return e.eachComponent("xAxis",r("x"),this),e.eachComponent("yAxis",r("y"),this),s.x&&s.y?(this._axesMap=a,void m(a.x,function(e,n){m(a.y,function(i,r){var o="x"+n+"y"+r,a=new p(o);a.grid=this,a.model=t,this._coordsMap[o]=a,this._coordsList.push(a),a.addAxis(e),a.addAxis(i)},this)},this)):(this._axesMap={},void(this._axesList=[]))},y._updateScale=function(t,e){function n(t,e,n){m(n.coordDimToDataDim(e.dim),function(n){e.scale.unionExtentFromData(t,n)})}f.each(this._axesList,function(t){t.scale.setExtent(1/0,-(1/0))}),t.eachSeries(function(r){if(h(r)){var o=u(r,t),a=o[0],s=o[1];if(!i(a,e,t)||!i(s,e,t))return;var l=this.getCartesian(a.componentIndex,s.componentIndex),c=r.getData(),d=l.getAxis("x"),f=l.getAxis("y");"list"===c.type&&(n(c,d,r),n(c,f,r))}},this)},y.getTooltipAxes=function(t){var e=[],n=[];return m(this.getCartesians(),function(i){var r=null!=t&&"auto"!==t?i.getAxis(t):i.getBaseAxis(),o=i.getOtherAxis(r);f.indexOf(e,r)<0&&e.push(r),f.indexOf(n,o)<0&&n.push(o)}),{baseAxes:e,otherAxes:n}};var _=["xAxis","yAxis"];o.create=function(t,e){var n=[];return t.eachComponent("grid",function(i,r){var a=new o(i,t,e);a.name="grid_"+r,a.resize(i,e,!0),i.coordinateSystem=a,n.push(a)}),t.eachSeries(function(e){if(h(e)){var n=u(e,t),i=n[0],r=n[1],o=i.getCoordSysModel(),a=o.coordinateSystem;e.coordinateSystem=a.getCartesian(i.componentIndex,r.componentIndex)}}),n},o.dimensions=o.prototype.dimensions=p.prototype.dimensions,n(26).register("cartesian2d",o),t.exports=o},function(t,e,n){"use strict";function i(t){return t>s||t<-s}var r=n(19),o=n(6),a=r.identity,s=5e-5,l=function(t){t=t||{},t.position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},u=l.prototype;u.transform=null,u.needLocalTransform=function(){return i(this.rotation)||i(this.position[0])||i(this.position[1])||i(this.scale[0]-1)||i(this.scale[1]-1)},u.updateTransform=function(){var t=this.parent,e=t&&t.transform,n=this.needLocalTransform(),i=this.transform;return n||e?(i=i||r.create(),n?this.getLocalTransform(i):a(i),e&&(n?r.mul(i,t.transform,i):r.copy(i,t.transform)),this.transform=i,this.invTransform=this.invTransform||r.create(),void r.invert(this.invTransform,i)):void(i&&a(i))},u.getLocalTransform=function(t){return l.getLocalTransform(this,t)},u.setTransform=function(t){var e=this.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)},u.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var h=[];u.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(r.mul(h,t.invTransform,e),e=h);var n=e[0]*e[0]+e[1]*e[1],o=e[2]*e[2]+e[3]*e[3],a=this.position,s=this.scale;i(n-1)&&(n=Math.sqrt(n)),i(o-1)&&(o=Math.sqrt(o)),e[0]<0&&(n=-n),e[3]<0&&(o=-o),a[0]=e[4],a[1]=e[5],s[0]=n,s[1]=o,this.rotation=Math.atan2(-e[1]/o,e[0]/n)}},u.getGlobalScale=function(){var t=this.transform;if(!t)return[1,1];var e=Math.sqrt(t[0]*t[0]+t[1]*t[1]),n=Math.sqrt(t[2]*t[2]+t[3]*t[3]);return t[0]<0&&(e=-e),t[3]<0&&(n=-n),[e,n]},u.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&o.applyTransform(n,n,i),n},u.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&o.applyTransform(n,n,i),n},l.getLocalTransform=function(t,e){e=e||[],a(e);var n=t.origin,i=t.scale||[1,1],o=t.rotation||0,s=t.position||[0,0];return n&&(e[4]-=n[0],e[5]-=n[1]),r.scale(e,e,i),o&&r.rotate(e,e,o),n&&(e[4]+=n[0],e[5]+=n[1]),e[4]+=s[0],e[5]+=s[1],e},t.exports=l},function(t,e,n){var i=n(100),r=n(1),o=n(13),a=n(9),s=["value","category","time","log"];t.exports=function(t,e,n,l){r.each(s,function(o){e.extend({type:t+"Axis."+o,mergeDefaultAndTheme:function(e,i){var s=this.layoutMode,l=s?a.getLayoutParams(e):{},u=i.getTheme();r.merge(e,u.get(o+"Axis")),r.merge(e,this.getDefaultOption()),e.type=n(t,e),s&&a.mergeLayoutParam(e,l,s)},defaultOption:r.mergeAll([{},i[o+"Axis"],l],!0)})}),o.registerSubTypeDefaulter(t+"Axis",r.curry(n,t))}},function(t,e,n){"use strict";function i(t,e){return e.type||(e.data?"category":"value")}var r=n(13),o=n(1),a=n(61),s=r.extend({type:"cartesian2dAxis",axis:null,init:function(){s.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){s.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){s.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});o.merge(s.prototype,n(43));var l={offset:0};a("x",s,i,l),a("y",s,i,l),t.exports=s},function(t,e){t.exports=function(t,e){e.eachSeriesByType(t,function(t){var e=t.getData(),n=t.coordinateSystem;if(n){for(var i=[],r=n.dimensions,o=0;o<r.length;o++)i.push(t.coordDimToDataDim(n.dimensions[o])[0]);1===i.length?e.each(i[0],function(t,i){e.setItemLayout(i,isNaN(t)?[NaN,NaN]:n.dataToPoint(t))}):2===i.length&&e.each(i,function(t,i,r){e.setItemLayout(r,isNaN(t)||isNaN(i)?[NaN,NaN]:n.dataToPoint([t,i]))},!0)}})}},function(t,e,n){var i=n(15),r=i.set,o=i.get;t.exports={clearColorPalette:function(){r(this,"colorIdx",0),r(this,"colorNameMap",{})},getColorFromPalette:function(t,e){e=e||this;var n=o(e,"colorIdx")||0,i=o(e,"colorNameMap")||r(e,"colorNameMap",{});if(i.hasOwnProperty(t))return i[t];var a=this.get("color",!0)||[];if(a.length){var s=a[n];return t&&(i[t]=s),r(e,"colorIdx",(n+1)%a.length),s}}}},function(t,e){t.exports=function(t,e){var n=e.findComponents({mainType:"legend"});n&&n.length&&e.eachSeriesByType(t,function(t){var e=t.getData();e.filterSelf(function(t){for(var i=e.getName(t),r=0;r<n.length;r++)if(!n[r].isSelected(i))return!1;return!0},this)},this)}},function(t,e,n){function i(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}var r=n(4),o=r.round,a={};a.intervalScaleNiceTicks=function(t,e,n,i){var s={},l=t[1]-t[0],u=s.interval=r.nice(l/e,!0);null!=n&&u<n&&(u=s.interval=n),null!=i&&u>i&&(u=s.interval=i);var h=s.intervalPrecision=a.getIntervalPrecision(u),c=s.niceTickExtent=[o(Math.ceil(t[0]/u)*u,h),o(Math.floor(t[1]/u)*u,h)];return a.fixExtent(c,t),s},a.getIntervalPrecision=function(t){return r.getPrecisionSafe(t)+2},a.fixExtent=function(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),i(t,0,e),i(t,1,e),t[0]>t[1]&&(t[0]=t[1])},a.intervalScaleGetTicks=function(t,e,n,i){var r=[];if(!t)return r;var a=1e4;e[0]<n[0]&&r.push(e[0]);for(var s=n[0];s<=n[1]&&(r.push(s),s=o(s+t,i),s!==r[r.length-1]);)if(r.length>a)return[];return e[1]>(r.length?r[r.length-1]:n[1])&&r.push(e[1]),r},t.exports=a},function(t,e,n){var i=n(36),r=n(50),o=n(15),a=function(){this.group=new i,this.uid=r.getUID("viewComponent")};a.prototype={constructor:a,init:function(t,e){},render:function(t,e,n,i){},dispose:function(){}};var s=a.prototype;s.updateView=s.updateLayout=s.updateVisual=function(t,e,n,i){},o.enableClassExtend(a),o.enableClassManagement(a,{registerWhenExtend:!0}),t.exports=a},function(t,e,n){"use strict";var i=n(73),r=n(23),o=n(60),a=n(183),s=n(1),l=function(t){o.call(this,t),r.call(this,t),a.call(this,t),this.id=t.id||i()};l.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(t,e){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var n=this[t];n||(n=this[t]=[]),n[0]=e[0],n[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(s.isObject(t))for(var n in t)t.hasOwnProperty(n)&&this.attrKV(n,t[n]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},s.mixin(l,a),s.mixin(l,o),s.mixin(l,r),t.exports=l},function(t,e,n){function i(t,e){return t[e]}function r(t,e,n){t[e]=n}function o(t,e,n){return(e-t)*n+t}function a(t,e,n){return n>.5?e:t}function s(t,e,n,i,r){var a=t.length;if(1==r)for(var s=0;s<a;s++)i[s]=o(t[s],e[s],n);else for(var l=a&&t[0].length,s=0;s<a;s++)for(var u=0;u<l;u++)i[s][u]=o(t[s][u],e[s][u],n)}function l(t,e,n){var i=t.length,r=e.length;if(i!==r){var o=i>r;if(o)t.length=r;else for(var a=i;a<r;a++)t.push(1===n?e[a]:_.call(e[a]))}for(var s=t[0]&&t[0].length,a=0;a<t.length;a++)if(1===n)isNaN(t[a])&&(t[a]=e[a]);else for(var l=0;l<s;l++)isNaN(t[a][l])&&(t[a][l]=e[a][l])}function u(t,e,n){if(t===e)return!0;var i=t.length;if(i!==e.length)return!1;if(1===n){for(var r=0;r<i;r++)if(t[r]!==e[r])return!1}else for(var o=t[0].length,r=0;r<i;r++)for(var a=0;a<o;a++)if(t[r][a]!==e[r][a])return!1;return!0}function h(t,e,n,i,r,o,a,s,l){var u=t.length;if(1==l)for(var h=0;h<u;h++)s[h]=c(t[h],e[h],n[h],i[h],r,o,a);else for(var d=t[0].length,h=0;h<u;h++)for(var f=0;f<d;f++)s[h][f]=c(t[h][f],e[h][f],n[h][f],i[h][f],r,o,a)}function c(t,e,n,i,r,o,a){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*a+(-3*(e-n)-2*s-l)*o+s*r+e}function d(t){if(y(t)){var e=t.length;if(y(t[0])){for(var n=[],i=0;i<e;i++)n.push(_.call(t[i]));return n}return _.call(t)}return t}function f(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function p(t){var e=t[t.length-1].value;return y(e&&e[0])?2:1}function g(t,e,n,i,r,d){var g=t._getter,x=t._setter,_="spline"===e,b=i.length;if(b){var w,S=i[0].value,M=y(S),T=!1,I=!1,A=M?p(i):0;i.sort(function(t,e){return t.time-e.time}),w=i[b-1].time;for(var C=[],P=[],D=i[0].value,k=!0,L=0;L<b;L++){C.push(i[L].time/w);var O=i[L].value;if(M&&u(O,D,A)||!M&&O===D||(k=!1),D=O,"string"==typeof O){var z=v.parse(O);z?(O=z,T=!0):I=!0}P.push(O)}if(d||!k){for(var E=P[b-1],L=0;L<b-1;L++)M?l(P[L],E,A):!isNaN(P[L])||isNaN(E)||I||T||(P[L]=E);M&&l(g(t._target,r),E,A);var R,N,B,V,F,G,H=0,W=0;if(T)var Z=[0,0,0,0];var q=function(t,e){var n;if(e<0)n=0;else if(e<W){for(R=Math.min(H+1,b-1),n=R;n>=0&&!(C[n]<=e);n--);n=Math.min(n,b-2)}else{for(n=H;n<b&&!(C[n]>e);n++);n=Math.min(n-1,b-2)}H=n,W=e;var i=C[n+1]-C[n];if(0!==i)if(N=(e-C[n])/i,_)if(V=P[n],B=P[0===n?n:n-1],F=P[n>b-2?b-1:n+1],G=P[n>b-3?b-1:n+2],M)h(B,V,F,G,N,N*N,N*N*N,g(t,r),A);else{var l;if(T)l=h(B,V,F,G,N,N*N,N*N*N,Z,1),l=f(Z);else{if(I)return a(V,F,N);l=c(B,V,F,G,N,N*N,N*N*N)}x(t,r,l)}else if(M)s(P[n],P[n+1],N,g(t,r),A);else{var l;if(T)s(P[n],P[n+1],N,Z,1),l=f(Z);else{if(I)return a(P[n],P[n+1],N);l=o(P[n],P[n+1],N)}x(t,r,l)}},j=new m({target:t._target,life:w,loop:t._loop,delay:t._delay,onframe:q,ondestroy:n});return e&&"spline"!==e&&(j.easing=e),j}}}var m=n(163),v=n(22),x=n(1),y=x.isArrayLike,_=Array.prototype.slice,b=function(t,e,n,o){this._tracks={},this._target=t,this._loop=e||!1,this._getter=n||i,this._setter=o||r,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};b.prototype={when:function(t,e){var n=this._tracks;for(var i in e)if(e.hasOwnProperty(i)){if(!n[i]){n[i]=[];var r=this._getter(this._target,i);if(null==r)continue;0!==t&&n[i].push({time:0,value:d(r)})}n[i].push({time:t,value:e[i]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,n=0;n<e;n++)t[n].call(this)},start:function(t,e){var n,i=this,r=0,o=function(){r--,r||i._doneCallback()};for(var a in this._tracks)if(this._tracks.hasOwnProperty(a)){var s=g(this,t,o,this._tracks[a],a,e);s&&(this._clipList.push(s),r++,this.animation&&this.animation.addClip(s),n=s)}if(n){var l=n.onframe;n.onframe=function(t,e){l(t,e);for(var n=0;n<i._onframeList.length;n++)i._onframeList[n](t,e)}}return r||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,n=this.animation,i=0;i<e.length;i++){var r=e[i];t&&r.onframe(this._target,1),n&&n.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}},t.exports=b},function(t,e){t.exports="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)}},function(t,e){var n=2*Math.PI;t.exports={normalizeRadian:function(t){return t%=n,t<0&&(t+=n),t}}},function(t,e){var n=function(){this.head=null,this.tail=null,this._len=0},i=n.prototype;i.insert=function(t){var e=new r(t);return this.insertEntry(e),e},i.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},i.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},i.len=function(){return this._len},i.clear=function(){this.head=this.tail=null,this._len=0};var r=function(t){this.value=t,this.next,this.prev},o=function(t){this._list=new n,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null},a=o.prototype;a.put=function(t,e){var n=this._list,i=this._map,o=null;if(null==i[t]){var a=n.len(),s=this._lastRemovedEntry;if(a>=this._maxSize&&a>0){var l=n.head;n.remove(l),delete i[l.key],o=l.value,this._lastRemovedEntry=l}s?s.value=e:s=new r(e),s.key=t,n.insertEntry(s),i[t]=s}return o},a.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},a.clear=function(){this._list.clear(),this._map={}},t.exports=o},function(t,e){var n=2311;t.exports=function(){return n++}},function(t,e){var n=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};n.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")},t.exports=n},function(t,e,n){function i(t,e,n){var i=null==e.x?0:e.x,r=null==e.x2?1:e.x2,o=null==e.y?0:e.y,a=null==e.y2?0:e.y2;e.global||(i=i*n.width+n.x,r=r*n.width+n.x,o=o*n.height+n.y,a=a*n.height+n.y);var s=t.createLinearGradient(i,o,r,a);return s}function r(t,e,n){var i=n.width,r=n.height,o=Math.min(i,r),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;e.global||(a=a*i+n.x,s=s*r+n.y,l*=o);var u=t.createRadialGradient(a,s,0,a,s,l);return u}var o=(n(40),[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]]),a=function(t,e){this.extendFrom(t,!1),this.host=e};a.prototype={constructor:a,host:null,fill:"#000",stroke:null,opacity:1,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textLineWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,n){for(var i=this,r=n&&n.style,a=!r,s=0;s<o.length;s++){var l=o[s],u=l[0];(a||i[u]!==r[u])&&(t[u]=i[u]||l[1])}if((a||i.fill!==r.fill)&&(t.fillStyle=i.fill),(a||i.stroke!==r.stroke)&&(t.strokeStyle=i.stroke),(a||i.opacity!==r.opacity)&&(t.globalAlpha=null==i.opacity?1:i.opacity),(a||i.blend!==r.blend)&&(t.globalCompositeOperation=i.blend||"source-over"),this.hasStroke()){var h=i.lineWidth;t.lineWidth=h/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t)for(var n in t)!t.hasOwnProperty(n)||e!==!0&&(e===!1?this.hasOwnProperty(n):null==t[n])||(this[n]=t[n])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,n){for(var o="radial"===e.type?r:i,a=o(t,e,n),s=e.colorStops,l=0;l<s.length;l++)a.addColorStop(s[l].offset,s[l].color);return a}};for(var s=a.prototype,l=0;l<o.length;l++){var u=o[l];u[0]in s||(s[u[0]]=u[1])}a.getGradient=s.getGradient,t.exports=a},function(t,e,n){var i=n(10),r=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]];t.exports=function(t){return i.browser.ie&&i.browser.version>=11?function(){var e,n=this.__clipPaths,i=this.style;if(n)for(var o=0;o<n.length;o++){var a=n[o],s=a&&a.shape,l=a&&a.type;if(s&&("sector"===l&&s.startAngle===s.endAngle||"rect"===l&&(!s.width||!s.height))){for(var u=0;u<r.length;u++)r[u][2]=i[r[u][0]],i[r[u][0]]=r[u][1];e=!0;break}}if(t.apply(this,arguments),e)for(var u=0;u<r.length;u++)i[r[u][0]]=r[u][2]}:t}},function(t,e,n){var i=n(173),r=n(172);t.exports={buildPath:function(t,e,n){var o=e.points,a=e.smooth;if(o&&o.length>=2){if(a&&"spline"!==a){var s=r(o,a,n,e.smoothConstraint);t.moveTo(o[0][0],o[0][1]);for(var l=o.length,u=0;u<(n?l:l-1);u++){var h=s[2*u],c=s[2*u+1],d=o[(u+1)%l];t.bezierCurveTo(h[0],h[1],c[0],c[1],d[0],d[1])}}else{"spline"===a&&(o=i(o,n)),t.moveTo(o[0][0],o[0][1]);for(var u=1,f=o.length;u<f;u++)t.lineTo(o[u][0],o[u][1])}n&&t.closePath()}}}},function(t,e){t.exports={buildPath:function(t,e){var n,i,r,o,a=e.x,s=e.y,l=e.width,u=e.height,h=e.r;l<0&&(a+=l,l=-l),u<0&&(s+=u,u=-u),"number"==typeof h?n=i=r=o=h:h instanceof Array?1===h.length?n=i=r=o=h[0]:2===h.length?(n=r=h[0],i=o=h[1]):3===h.length?(n=h[0],i=o=h[1],r=h[2]):(n=h[0],i=h[1],r=h[2],o=h[3]):n=i=r=o=0;var c;n+i>l&&(c=n+i,n*=l/c,i*=l/c),r+o>l&&(c=r+o,r*=l/c,o*=l/c),i+r>u&&(c=i+r,i*=u/c,r*=u/c),n+o>u&&(c=n+o,n*=u/c,o*=u/c),t.moveTo(a+n,s),t.lineTo(a+l-i,s),0!==i&&t.quadraticCurveTo(a+l,s,a+l,s+i),t.lineTo(a+l,s+u-r),0!==r&&t.quadraticCurveTo(a+l,s+u,a+l-r,s+u),t.lineTo(a+o,s+u),0!==o&&t.quadraticCurveTo(a,s+u,a,s+u-o),t.lineTo(a,s+n),0!==n&&t.quadraticCurveTo(a,s,a+n,s)}}},function(t,e,n){var i=n(1),r={};r.layout=function(t,e,n){n=n||{};var r=t.coordinateSystem,o=e.axis,a={},s=o.position,l=o.onZero?"onZero":s,u=o.dim,h=r.getRect(),c=[h.x,h.x+h.width,h.y,h.y+h.height],d={left:0,right:1,top:0,bottom:1,onZero:2},f=e.get("offset")||0,p="x"===u?[c[2]-f,c[3]+f]:[c[0]-f,c[1]+f];if(o.onZero){var g=r.getAxis("x"===u?"y":"x",o.onZeroAxisIndex),m=g.toGlobalCoord(g.dataToCoord(0));p[d.onZero]=Math.max(Math.min(m,p[1]),p[0])}a.position=["y"===u?p[d[l]]:c[0],"x"===u?p[d[l]]:c[3]],a.rotation=Math.PI/2*("x"===u?0:1);var v={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=v[s],a.labelOffset=o.onZero?p[d[s]]-p[d.onZero]:0,
e.get("axisTick.inside")&&(a.tickDirection=-a.tickDirection),i.retrieve(n.labelInside,e.get("axisLabel.inside"))&&(a.labelDirection=-a.labelDirection);var x=e.get("axisLabel.rotate");return a.labelRotate="top"===l?-x:x,a.labelInterval=o.getLabelInterval(),a.z2=1,a},t.exports=r},function(t,e,n){"use strict";function i(t,e,n,i){var r=i.getWidth(),o=i.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+n,o)-n,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}var r=n(1),o=n(3),a=n(16),s=n(7),l=n(19),u=n(18),h=n(41),c={};c.buildElStyle=function(t){var e,n=t.get("type"),i=t.getModel(n+"Style");return"line"===n?(e=i.getLineStyle(),e.fill=null):"shadow"===n&&(e=i.getAreaStyle(),e.stroke=null),e},c.buildLabelElOption=function(t,e,n,r,o){var l=n.get("value"),u=c.getValueLabel(l,e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get("label.precision"),formatter:n.get("label.formatter")}),h=n.getModel("label"),d=s.normalizeCssArray(h.get("padding")||0),f=h.getFont(),p=a.getBoundingRect(u,f),g=o.position,m=p.width+d[1]+d[3],v=p.height+d[0]+d[2],x=o.align;"right"===x&&(g[0]-=m),"center"===x&&(g[0]-=m/2);var y=o.verticalAlign;"bottom"===y&&(g[1]-=v),"middle"===y&&(g[1]-=v/2),i(g,m,v,r);var _=h.get("backgroundColor");_&&"auto"!==_||(_=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:m,height:v,r:h.get("borderRadius")},position:g.slice(),style:{text:u,textFont:f,textFill:h.getTextColor(),textPosition:"inside",fill:_,stroke:h.get("borderColor")||"transparent",lineWidth:h.get("borderWidth")||0,shadowBlur:h.get("shadowBlur"),shadowColor:h.get("shadowColor"),shadowOffsetX:h.get("shadowOffsetX"),shadowOffsetY:h.get("shadowOffsetY")},z2:10}},c.getValueLabel=function(t,e,n,i,o){var a=e.scale.getLabel(t,{precision:o.precision}),s=o.formatter;if(s){var l={value:u.getAxisRawValue(e,t),seriesData:[]};r.each(i,function(t){var e=n.getSeriesByIndex(t.seriesIndex),i=t.dataIndexInside,r=e&&e.getDataParams(i);r&&l.seriesData.push(r)}),r.isString(s)?a=s.replace("{value}",a):r.isFunction(s)&&(a=s(l))}return a},c.getTransformedPosition=function(t,e,n){var i=l.create();return l.rotate(i,i,n.rotation),l.translate(i,i,n.position),o.applyTransform([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)},c.buildCartesianSingleLabelElOption=function(t,e,n,i,r,o){var a=h.innerTextLayout(n.rotation,0,n.labelDirection);n.labelMargin=r.get("label.margin"),c.buildLabelElOption(e,i,r,o,{position:c.getTransformedPosition(i.axis,t,n),align:a.textAlign,verticalAlign:a.textVerticalAlign})},c.makeLineShape=function(t,e,n){return n=n||0,{x1:t[n],y1:t[1-n],x2:e[n],y2:e[1-n]}},c.makeRectShape=function(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}},c.makeSectorShape=function(t,e,n,i,r,o){return{cx:t,cy:e,r0:n,r:i,startAngle:r,endAngle:o,clockwise:!0}},t.exports=c},function(t,e,n){var i=n(7),r=n(1),o={},a=["x","y","z","radius","angle","single"],s=["cartesian2d","polar","singleAxis"];o.isCoordSupported=function(t){return r.indexOf(s,t)>=0},o.createNameEach=function(t,e){t=t.slice();var n=r.map(t,i.capitalFirst);e=(e||[]).slice();var o=r.map(e,i.capitalFirst);return function(i,a){r.each(t,function(t,r){for(var s={name:t,capital:n[r]},l=0;l<e.length;l++)s[e[l]]=t+o[l];i.call(a,s)})}},o.eachAxisDim=o.createNameEach(a,["axisIndex","axis","index","id"]),o.createLinkedNodesFinder=function(t,e,n){function i(t,e){return r.indexOf(e.nodes,t)>=0}function o(t,i){var o=!1;return e(function(e){r.each(n(t,e)||[],function(t){i.records[e.name][t]&&(o=!0)})}),o}function a(t,i){i.nodes.push(t),e(function(e){r.each(n(t,e)||[],function(t){i.records[e.name][t]=!0})})}return function(n){function r(t){!i(t,s)&&o(t,s)&&(a(t,s),l=!0)}var s={nodes:[],records:{}};if(e(function(t){s.records[t.name]={}}),!n)return s;a(n,s);var l;do l=!1,t(r);while(l);return s}},t.exports=o},function(t,e,n){var i=n(1);t.exports={updateSelectedMap:function(t){this._targetList=t.slice(),this._selectTargetMap=i.reduce(t||[],function(t,e){return t.set(e.name,e),t},i.createHashMap())},select:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t),i=this.get("selectedMode");"single"===i&&this._selectTargetMap.each(function(t){t.selected=!1}),n&&(n.selected=!0)},unSelect:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);n&&(n.selected=!1)},toggleSelected:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);if(null!=n)return this[n.selected?"unSelect":"select"](t,e),n.selected},isSelected:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return n&&n.selected}}},function(t,e,n){function i(t){r.defaultEmphasis(t.label,["show"])}var r=n(5),o=n(1),a=n(10),s=n(7),l=s.addCommas,u=s.encodeHTML,h=n(2).extendComponentModel({type:"marker",dependencies:["series","grid","polar","geo"],init:function(t,e,n,i){this.mergeDefaultAndTheme(t,n),this.mergeOption(t,n,i.createdBySelf,!0)},isAnimationEnabled:function(){if(a.node)return!1;var t=this.__hostSeries;return this.getShallow("animation")&&t&&t.isAnimationEnabled()},mergeOption:function(t,e,n,r){var a=this.constructor,s=this.mainType+"Model";n||e.eachSeries(function(t){var n=t.get(this.mainType),l=t[s];return n&&n.data?(l?l.mergeOption(n,e,!0):(r&&i(n),o.each(n.data,function(t){t instanceof Array?(i(t[0]),i(t[1])):i(t)}),l=new a(n,this,e),o.extend(l,{mainType:this.mainType,seriesIndex:t.seriesIndex,name:t.name,createdBySelf:!0}),l.__hostSeries=t),void(t[s]=l)):void(t[s]=null)},this)},formatTooltip:function(t){var e=this.getData(),n=this.getRawValue(t),i=o.isArray(n)?o.map(n,l).join(", "):l(n),r=e.getName(t),a=u(this.name);return(null!=n||r)&&(a+="<br />"),r&&(a+=u(r),null!=n&&(a+=" : ")),null!=n&&(a+=u(i)),a},getData:function(){return this._data},setData:function(t){this._data=t}});o.mixin(h,r.dataFormatMixin),t.exports=h},function(t,e,n){var i=n(1);t.exports=n(2).extendComponentView({type:"marker",init:function(){this.markerGroupMap=i.createHashMap()},render:function(t,e,n){var i=this.markerGroupMap;i.each(function(t){t.__keep=!1});var r=this.type+"Model";e.eachSeries(function(t){var i=t[r];i&&this.renderSeries(t,i,e,n)},this),i.each(function(t){!t.__keep&&this.group.remove(t.group)},this)},renderSeries:function(){}})},function(t,e,n){function i(t){return!(isNaN(parseFloat(t.x))&&isNaN(parseFloat(t.y)))}function r(t){return!isNaN(parseFloat(t.x))&&!isNaN(parseFloat(t.y))}function o(t,e,n){var i=-1;do i=Math.max(l.getPrecision(t.get(e,n)),i),t=t.stackedOn;while(t);return i}function a(t,e,n,i,r,a){var s=[],l=m(e,i,t),u=e.indicesOfNearest(i,l,!0)[0];s[r]=e.get(n,u,!0),s[a]=e.get(i,u,!0);var h=o(e,i,u);return h=Math.min(h,20),h>=0&&(s[a]=+s[a].toFixed(h)),s}var s=n(1),l=n(4),u=s.indexOf,h=s.curry,c={min:h(a,"min"),max:h(a,"max"),average:h(a,"average")},d=function(t,e){var n=t.getData(),i=t.coordinateSystem;if(e&&!r(e)&&!s.isArray(e.coord)&&i){var o=i.dimensions,a=f(e,n,i,t);if(e=s.clone(e),e.type&&c[e.type]&&a.baseAxis&&a.valueAxis){var l=u(o,a.baseAxis.dim),h=u(o,a.valueAxis.dim);e.coord=c[e.type](n,a.baseDataDim,a.valueDataDim,l,h),e.value=e.coord[h]}else{for(var d=[null!=e.xAxis?e.xAxis:e.radiusAxis,null!=e.yAxis?e.yAxis:e.angleAxis],p=0;p<2;p++)if(c[d[p]]){var g=t.coordDimToDataDim(o[p])[0];d[p]=m(n,g,d[p])}e.coord=d}}return e},f=function(t,e,n,i){var r={};return null!=t.valueIndex||null!=t.valueDim?(r.valueDataDim=null!=t.valueIndex?e.getDimension(t.valueIndex):t.valueDim,r.valueAxis=n.getAxis(i.dataDimToCoordDim(r.valueDataDim)),r.baseAxis=n.getOtherAxis(r.valueAxis),r.baseDataDim=i.coordDimToDataDim(r.baseAxis.dim)[0]):(r.baseAxis=i.getBaseAxis(),r.valueAxis=n.getOtherAxis(r.baseAxis),r.baseDataDim=i.coordDimToDataDim(r.baseAxis.dim)[0],r.valueDataDim=i.coordDimToDataDim(r.valueAxis.dim)[0]),r},p=function(t,e){return!(t&&t.containData&&e.coord&&!i(e))||t.containData(e.coord)},g=function(t,e,n,i){return i<2?t.coord&&t.coord[i]:t.value},m=function(t,e,n){if("average"===n){var i=0,r=0;return t.each(e,function(t,e){isNaN(t)||(i+=t,r++)},!0),i/r}return t.getDataExtent(e,!0)["max"===n?1:0]};t.exports={dataTransform:d,dataFilter:p,dimValueGetter:g,getAxisInfo:f,numCalculate:m}},function(t,e,n){"use strict";function i(t){return t.get("stack")||d+t.seriesIndex}function r(t){return t.dim+t.index}function o(t,e){var n=[],i=t.axis,r="axis0";if("category"===i.type){for(var o=i.getBandWidth(),a=0;a<t.count;a++)n.push(u.defaults({bandWidth:o,axisKey:r,stackId:d+a},t));for(var l=s(n,e),h=[],a=0;a<t.count;a++){var c=l[r][d+a];c.offsetCenter=c.offset+c.width/2,h.push(c)}return h}}function a(t,e){var n=u.map(t,function(t){var e=t.getData(),n=t.coordinateSystem,o=n.getBaseAxis(),a=o.getExtent(),s="category"===o.type?o.getBandWidth():Math.abs(a[1]-a[0])/e.count(),l=c(t.get("barWidth"),s),u=c(t.get("barMaxWidth"),s),h=t.get("barGap"),d=t.get("barCategoryGap");return{bandWidth:s,barWidth:l,barMaxWidth:u,barGap:h,barCategoryGap:d,axisKey:r(o),stackId:i(t)}});return s(n,e)}function s(t,e){var n={};u.each(t,function(t,e){var i=t.axisKey,r=t.bandWidth,o=n[i]||{bandWidth:r,remainedWidth:r,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},a=o.stacks;n[i]=o;var s=t.stackId;a[s]||o.autoWidthCount++,a[s]=a[s]||{width:0,maxWidth:0};var l=t.barWidth;l&&!a[s].width&&(a[s].width=l,l=Math.min(o.remainedWidth,l),o.remainedWidth-=l);var u=t.barMaxWidth;u&&(a[s].maxWidth=u);var h=t.barGap;null!=h&&(o.gap=h);var c=t.barCategoryGap;null!=c&&(o.categoryGap=c)});var i={};return u.each(n,function(t,e){i[e]={};var n=t.stacks,r=t.bandWidth,o=c(t.categoryGap,r),a=c(t.gap,1),s=t.remainedWidth,l=t.autoWidthCount,h=(s-o)/(l+(l-1)*a);h=Math.max(h,0),u.each(n,function(t,e){var n=t.maxWidth;n&&n<h&&(n=Math.min(n,s),t.width&&(n=Math.min(n,t.width)),s-=n,t.width=n,l--)}),h=(s-o)/(l+(l-1)*a),h=Math.max(h,0);var d,f=0;u.each(n,function(t,e){t.width||(t.width=h),d=t,f+=t.width*(1+a)}),d&&(f-=d.width*a);var p=-f/2;u.each(n,function(t,n){i[e][n]=i[e][n]||{offset:p,width:t.width},p+=t.width*(1+a)})}),i}function l(t,e,n){var o=a(u.filter(e.getSeriesByType(t),function(t){return!e.isSeriesFiltered(t)&&t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type})),s={},l={};e.eachSeriesByType(t,function(t){if("cartesian2d"===t.coordinateSystem.type){var e=t.getData(),n=t.coordinateSystem,a=n.getBaseAxis(),u=i(t),h=o[r(a)][u],c=h.offset,d=h.width,f=n.getOtherAxis(a),p=t.get("barMinHeight")||0,g=a.onZero?f.toGlobalCoord(f.dataToCoord(0)):f.getGlobalExtent()[0],m=[t.coordDimToDataDim("x")[0],t.coordDimToDataDim("y")[0]],v=e.mapArray(m,function(t,e){return n.dataToPoint([t,e])},!0);s[u]=s[u]||[],l[u]=l[u]||[],e.setLayout({offset:c,size:d}),e.each(t.coordDimToDataDim(f.dim)[0],function(t,n){if(!isNaN(t)){s[u][n]||(s[u][n]={p:g,n:g},l[u][n]={p:g,n:g});var i,r,o,a,h=t>=0?"p":"n",m=v[n],x=s[u][n][h],y=l[u][n][h];f.isHorizontal()?(i=x,r=m[1]+c,o=m[0]-y,a=d,l[u][n][h]+=o,Math.abs(o)<p&&(o=(o<0?-1:1)*p),s[u][n][h]+=o):(i=m[0]+c,r=x,o=d,a=m[1]-y,l[u][n][h]+=a,Math.abs(a)<p&&(a=(a<=0?-1:1)*p),s[u][n][h]+=a),e.setItemLayout(n,{x:i,y:r,width:o,height:a})}},!0)}},this)}var u=n(1),h=n(4),c=h.parsePercent,d="__ec_stack_";l.getLayoutOnAxis=o,t.exports=l},,function(t,e){t.exports=function(t,e){var n={};e.eachRawSeriesByType(t,function(t){var i=t.getRawData(),r={};if(!e.isSeriesFiltered(t)){var o=t.getData();o.each(function(t){var e=o.getRawIndex(t);r[e]=t}),i.each(function(e){var a=r[e],s=null!=a&&o.getItemVisual(a,"color",!0);if(s)i.setItemVisual(e,"color",s);else{var l=i.getItemModel(e),u=l.get("itemStyle.normal.color")||t.getColorFromPalette(i.getName(e),n);i.setItemVisual(e,"color",u),null!=a&&o.setItemVisual(a,"color",u)}})}})}},function(t,e,n){var i=n(6),r=n(20),o={},a=Math.min,s=Math.max,l=Math.sin,u=Math.cos,h=i.create(),c=i.create(),d=i.create(),f=2*Math.PI;o.fromPoints=function(t,e,n){if(0!==t.length){var i,r=t[0],o=r[0],l=r[0],u=r[1],h=r[1];for(i=1;i<t.length;i++)r=t[i],o=a(o,r[0]),l=s(l,r[0]),u=a(u,r[1]),h=s(h,r[1]);e[0]=o,e[1]=u,n[0]=l,n[1]=h}},o.fromLine=function(t,e,n,i,r,o){r[0]=a(t,n),r[1]=a(e,i),o[0]=s(t,n),o[1]=s(e,i)};var p=[],g=[];o.fromCubic=function(t,e,n,i,o,l,u,h,c,d){var f,m=r.cubicExtrema,v=r.cubicAt,x=m(t,n,o,u,p);for(c[0]=1/0,c[1]=1/0,d[0]=-(1/0),d[1]=-(1/0),f=0;f<x;f++){var y=v(t,n,o,u,p[f]);c[0]=a(y,c[0]),d[0]=s(y,d[0])}for(x=m(e,i,l,h,g),f=0;f<x;f++){var _=v(e,i,l,h,g[f]);c[1]=a(_,c[1]),d[1]=s(_,d[1])}c[0]=a(t,c[0]),d[0]=s(t,d[0]),c[0]=a(u,c[0]),d[0]=s(u,d[0]),c[1]=a(e,c[1]),d[1]=s(e,d[1]),c[1]=a(h,c[1]),d[1]=s(h,d[1])},o.fromQuadratic=function(t,e,n,i,o,l,u,h){var c=r.quadraticExtremum,d=r.quadraticAt,f=s(a(c(t,n,o),1),0),p=s(a(c(e,i,l),1),0),g=d(t,n,o,f),m=d(e,i,l,p);u[0]=a(t,o,g),u[1]=a(e,l,m),h[0]=s(t,o,g),h[1]=s(e,l,m)},o.fromArc=function(t,e,n,r,o,a,s,p,g){var m=i.min,v=i.max,x=Math.abs(o-a);if(x%f<1e-4&&x>1e-4)return p[0]=t-n,p[1]=e-r,g[0]=t+n,void(g[1]=e+r);if(h[0]=u(o)*n+t,h[1]=l(o)*r+e,c[0]=u(a)*n+t,c[1]=l(a)*r+e,m(p,h,c),v(g,h,c),o%=f,o<0&&(o+=f),a%=f,a<0&&(a+=f),o>a&&!s?a+=f:o<a&&s&&(o+=f),s){var y=a;a=o,o=y}for(var _=0;_<a;_+=Math.PI/2)_>o&&(d[0]=u(_)*n+t,d[1]=l(_)*r+e,m(p,d,p),v(g,d,g))},t.exports=o},function(t,e,n){var i=n(38),r=n(1),o=n(16),a=n(40),s=function(t){i.call(this,t)};s.prototype={constructor:s,type:"text",brush:function(t,e){var n=this.style;this.__dirty&&a.normalizeTextStyle(n,!0),n.fill=n.stroke=n.shadowBlur=n.shadowColor=n.shadowOffsetX=n.shadowOffsetY=null;var i=n.text;null!=i&&(i+=""),n.bind(t,this,e),a.needDrawText(i,n)&&(this.setTransform(t),a.renderText(this,t,i,n),this.restoreTransform(t))},getBoundingRect:function(){var t=this.style;if(this.__dirty&&a.normalizeTextStyle(t,!0),!this._rect){var e=t.text;null!=e?e+="":e="";var n=o.getBoundingRect(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.rich);if(n.x+=t.x||0,n.y+=t.y||0,a.getStroke(t.textStroke,t.textLineWidth)){var i=t.textLineWidth;n.x-=i/2,n.y-=i/2,n.width+=i,n.height+=i}this._rect=n}return this._rect}},r.inherits(s,i),t.exports=s},function(t,e,n){var i=n(40),r=n(12),o=new r,a=function(){};a.prototype={constructor:a,drawRectText:function(t,e){var n=this.style;e=n.textRect||e,this.__dirty&&i.normalizeTextStyle(n,!0);var r=n.text;if(null!=r&&(r+=""),i.needDrawText(r,n)){t.save();var a=this.transform;n.transformText?this.setTransform(t):a&&(o.copy(e),o.applyTransform(a),e=o),i.renderText(this,t,r,n,e),t.restore()}}},t.exports=a},function(t,e,n){function i(t){delete f[t]}/*!
	 * ZRender, a high performance 2d drawing library.
	 *
	 * Copyright (c) 2013, Baidu Inc.
	 * All rights reserved.
	 *
	 * LICENSE
	 * https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
	 */
var r=n(73),o=n(10),a=n(1),s=n(158),l=n(161),u=n(162),h=n(169),c=!o.canvasSupported,d={canvas:n(160)},f={},p={};p.version="3.6.1",p.init=function(t,e){var n=new g(r(),t,e);return f[n.id]=n,n},p.dispose=function(t){if(t)t.dispose();else{for(var e in f)f.hasOwnProperty(e)&&f[e].dispose();f={}}return p},p.getInstance=function(t){return f[t]},p.registerPainter=function(t,e){d[t]=e};var g=function(t,e,n){n=n||{},this.dom=e,this.id=t;var i=this,r=new l,f=n.renderer;if(c){if(!d.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");f="vml"}else f&&d[f]||(f="canvas");var p=new d[f](e,r,n);this.storage=r,this.painter=p;var g=o.node?null:new h(p.getViewportRoot());this.handler=new s(r,p,g,p.root),this.animation=new u({stage:{update:a.bind(this.flush,this)}}),this.animation.start(),this._needsRefresh;var m=r.delFromStorage,v=r.addToStorage;r.delFromStorage=function(t){m.call(r,t),t&&t.removeSelfFromZr(i)},r.addToStorage=function(t){v.call(r,t),t.addSelfToZr(i)}};g.prototype={constructor:g,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer(t,e),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},refresh:function(){this._needsRefresh=!0},flush:function(){this._needsRefresh&&this.refreshImmediately(),this._needsRefreshHover&&this.refreshHoverImmediately()},addHover:function(t,e){this.painter.addHover&&(this.painter.addHover(t,e),this.refreshHover())},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,n){this.handler.on(t,e,n)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,i(this.id)}},t.exports=p},function(t,e,n){var i=n(2),r=n(1);t.exports=function(t,e){r.each(e,function(e){e.update="updateView",i.registerAction(e,function(n,i){var r={};return i.eachComponent({mainType:"series",subType:t,query:n},function(t){t[e.method]&&t[e.method](n.name,n.dataIndex);var i=t.getData();i.each(function(e){var n=i.getName(e);r[n]=t.isSelected(n)||!1})}),{name:n.name,selected:r}})})}},function(t,e,n){"use strict";var i=n(17),r=n(28);t.exports=i.extend({type:"series.__base_bar__",getInitialData:function(t,e){return r(t.data,this,e)},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var n=e.dataToPoint(t,!0),i=this.getData(),r=i.getLayout("offset"),o=i.getLayout("size"),a=e.getBaseAxis().isHorizontal()?0:1;return n[a]+=r+o/2,n}return[NaN,NaN]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,itemStyle:{normal:{},emphasis:{}}}})},function(t,e,n){function i(t,e){"outside"===t.textPosition&&(t.textPosition=e)}var r=n(3),o={};o.setLabel=function(t,e,n,o,a,s,l){var u=n.getModel("label.normal"),h=n.getModel("label.emphasis");r.setLabelStyle(t,e,u,h,{labelFetcher:a,labelDataIndex:s,defaultText:a.getRawValue(s),isRectText:!0,autoColor:o}),i(t),i(e)},t.exports=o},function(t,e,n){var i=n(5),r={};r.findLabelValueDim=function(t){var e,n=i.otherDimToDataDim(t,"label");if(n.length)e=n[0];else for(var r,o=t.dimensions.slice();o.length&&(e=o.pop(),r=t.getDimensionInfo(e).type,"ordinal"===r||"time"===r););return e},t.exports=r},function(t,e,n){function i(t){return isNaN(t[0])||isNaN(t[1])}function r(t,e,n,r,o,a,l,m,v,x,y){for(var _=0,b=n,w=0;w<r;w++){var S=e[b];if(b>=o||b<0)break;if(i(S)){if(y){b+=a;continue}break}if(b===n)t[a>0?"moveTo":"lineTo"](S[0],S[1]),d(p,S);else if(v>0){var M=b+a,T=e[M];if(y)for(;T&&i(e[M]);)M+=a,T=e[M];var I=.5,A=e[_],T=e[M];if(!T||i(T))d(g,S);else{i(T)&&!y&&(T=S),s.sub(f,T,A);var C,P;if("x"===x||"y"===x){var D="x"===x?0:1;C=Math.abs(S[D]-A[D]),P=Math.abs(S[D]-T[D])}else C=s.dist(S,A),P=s.dist(S,T);I=P/(P+C),c(g,S,f,-v*(1-I))}u(p,p,m),h(p,p,l),u(g,g,m),h(g,g,l),t.bezierCurveTo(p[0],p[1],g[0],g[1],S[0],S[1]),c(p,S,f,v*I)}else t.lineTo(S[0],S[1]);_=b,b+=a}return w}function o(t,e){var n=[1/0,1/0],i=[-(1/0),-(1/0)];if(e)for(var r=0;r<t.length;r++){var o=t[r];o[0]<n[0]&&(n[0]=o[0]),o[1]<n[1]&&(n[1]=o[1]),o[0]>i[0]&&(i[0]=o[0]),o[1]>i[1]&&(i[1]=o[1])}return{min:e?n:i,max:e?i:n}}var a=n(8),s=n(6),l=n(76),u=s.min,h=s.max,c=s.scaleAndAdd,d=s.copy,f=[],p=[],g=[];t.exports={Polyline:a.extend({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},brush:l(a.prototype.brush),buildPath:function(t,e){var n=e.points,a=0,s=n.length,l=o(n,e.smoothConstraint);if(e.connectNulls){for(;s>0&&i(n[s-1]);s--);for(;a<s&&i(n[a]);a++);}for(;a<s;)a+=r(t,n,a,s,s,1,l.min,l.max,e.smooth,e.smoothMonotone,e.connectNulls)+1}}),Polygon:a.extend({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},brush:l(a.prototype.brush),buildPath:function(t,e){var n=e.points,a=e.stackedOnPoints,s=0,l=n.length,u=e.smoothMonotone,h=o(n,e.smoothConstraint),c=o(a,e.smoothConstraint);if(e.connectNulls){for(;l>0&&i(n[l-1]);l--);for(;s<l&&i(n[s]);s++);}for(;s<l;){var d=r(t,n,s,l,l,1,h.min,h.max,e.smooth,u,e.connectNulls);r(t,a,s+d-1,d,l,-1,c.min,c.max,e.stackedOnSmooth,u,e.connectNulls),s+=d+1,t.closePath()}}})}},,function(t,e,n){function i(t){this.pointerChecker,this._zr=t,this._opt={};var e=d.bind,n=e(r,this),i=e(o,this),u=e(a,this),h=e(s,this),f=e(l,this);c.call(this),this.setPointerChecker=function(t){this.pointerChecker=t},this.enable=function(e,r){this.disable(),this._opt=d.defaults(d.clone(r)||{},{zoomOnMouseWheel:!0,moveOnMouseMove:!0,preventDefaultMouseMove:!0}),null==e&&(e=!0),e!==!0&&"move"!==e&&"pan"!==e||(t.on("mousedown",n),t.on("mousemove",i),t.on("mouseup",u)),e!==!0&&"scale"!==e&&"zoom"!==e||(t.on("mousewheel",h),t.on("pinch",f))},this.disable=function(){t.off("mousedown",n),t.off("mousemove",i),t.off("mouseup",u),t.off("mousewheel",h),t.off("pinch",f)},this.dispose=this.disable,this.isDragging=function(){return this._dragging},this.isPinching=function(){return this._pinching}}function r(t){if(!t.target||!t.target.draggable){var e=t.offsetX,n=t.offsetY;this.pointerChecker&&this.pointerChecker(t,e,n)&&(this._x=e,this._y=n,this._dragging=!0)}}function o(t){if(h(this,"moveOnMouseMove",t)&&this._dragging&&"pinch"!==t.gestureEvent&&!p.isTaken(this._zr,"globalPan")){var e=t.offsetX,n=t.offsetY,i=this._x,r=this._y,o=e-i,a=n-r;this._x=e,this._y=n,this._opt.preventDefaultMouseMove&&f.stop(t.event),this.trigger("pan",o,a,i,r,e,n)}}function a(t){this._dragging=!1}function s(t){if(h(this,"zoomOnMouseWheel",t)&&0!==t.wheelDelta){var e=t.wheelDelta>0?1.1:1/1.1;u.call(this,t,e,t.offsetX,t.offsetY)}}function l(t){if(!p.isTaken(this._zr,"globalPan")){var e=t.pinchScale>1?1.1:1/1.1;u.call(this,t,e,t.pinchX,t.pinchY)}}function u(t,e,n,i){this.pointerChecker&&this.pointerChecker(t,n,i)&&(f.stop(t.event),this.trigger("zoom",e,n,i))}function h(t,e,n){var i=t._opt[e];return i&&(!d.isString(i)||n.event[i+"Key"])}var c=n(23),d=n(1),f=n(21),p=n(133);d.mixin(i,c),t.exports=i},function(t,e,n){var i=n(1),r={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},o=i.merge({boundaryGap:!0,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},r),a=i.merge({boundaryGap:[0,0],splitNumber:5},r),s=i.defaults({scale:!0,min:"dataMin",max:"dataMax"},a),l=i.defaults({scale:!0,logBase:10},a);t.exports={categoryAxis:o,valueAxis:a,timeAxis:s,logAxis:l}},function(t,e){t.exports={containStroke:function(t,e,n,i,r,o,a){if(0===r)return!1;var s=r,l=0,u=t;if(a>e+s&&a>i+s||a<e-s&&a<i-s||o>t+s&&o>n+s||o<t-s&&o<n-s)return!1;if(t===n)return Math.abs(o-t)<=s/2;l=(e-i)/(t-n),u=(t*i-n*e)/(t-n);var h=l*o-a+u,c=h*h/(l*l+1);return c<=s/2*s/2}}},function(t,e,n){var i=n(20);t.exports={containStroke:function(t,e,n,r,o,a,s,l,u){if(0===s)return!1;var h=s;if(u>e+h&&u>r+h&&u>a+h||u<e-h&&u<r-h&&u<a-h||l>t+h&&l>n+h&&l>o+h||l<t-h&&l<n-h&&l<o-h)return!1;var c=i.quadraticProjectPoint(t,e,n,r,o,a,l,u,null);return c<=h/2}}},function(t,e){t.exports=function(t,e,n,i,r,o){if(o>e&&o>i||o<e&&o<i)return 0;if(i===e)return 0;var a=i<e?1:-1,s=(o-e)/(i-e);1!==s&&0!==s||(a=i<e?.5:-.5);var l=s*(n-t)+t;return l>r?a:0}},function(t,e,n){"use strict";var i=n(1),r=n(39),o=function(t,e,n,i,o,a){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==n?1:n,this.y2=null==i?0:i,this.type="linear",this.global=a||!1,r.call(this,o)};o.prototype={constructor:o},i.inherits(o,r),t.exports=o},function(t,e,n){"use strict";function i(t){r.each(o,function(e){this[e]=r.bind(t[e],t)},this)}var r=n(1),o=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"];t.exports=i},function(t,e,n){var i=n(1);n(59),n(107),n(108);var r=n(86),o=n(2);o.registerLayout(i.curry(r,"bar")),o.registerVisual(function(t){t.eachSeriesByType("bar",function(t){var e=t.getData();e.setVisual("legendSymbol","roundRect")})}),n(32)},function(t,e,n){t.exports=n(94).extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect"})},function(t,e,n){"use strict";function i(t,e,n){n.style.text=null,l.updateProps(n,{shape:{width:0}},e,t,function(){n.parent&&n.parent.remove(n)})}function r(t,e,n){n.style.text=null,l.updateProps(n,{shape:{r:n.shape.r0}},e,t,function(){n.parent&&n.parent.remove(n)})}function o(t,e,n,i,r,o,a,h){var c=e.getItemVisual(n,"color"),d=e.getItemVisual(n,"opacity"),f=i.getModel("itemStyle.normal"),p=i.getModel("itemStyle.emphasis").getBarItemStyle();h||t.setShape("r",f.get("barBorderRadius")||0),t.useStyle(s.defaults({fill:c,opacity:d},f.getBarItemStyle()));var g=i.getShallow("cursor");g&&t.attr("cursor",g);var m=a?r.height>0?"bottom":"top":r.width>0?"left":"right";h||u.setLabel(t.style,p,i,c,o,n,m),l.setHoverStyle(t,p)}function a(t,e){var n=t.get(h)||0;return Math.min(n,Math.abs(e.width),Math.abs(e.height))}var s=n(1),l=n(3),u=n(95),h=["itemStyle","normal","barBorderWidth"];s.extend(n(11).prototype,n(109));var c=n(2).extendChartView({type:"bar",render:function(t,e,n){var i=t.get("coordinateSystem");return"cartesian2d"!==i&&"polar"!==i||this._render(t,e,n),this.group},dispose:s.noop,_render:function(t,e,n){var a,s=this.group,u=t.getData(),h=this._data,c=t.coordinateSystem,p=c.getBaseAxis();"cartesian2d"===c.type?a=p.isHorizontal():"polar"===c.type&&(a="angle"===p.dim);var g=t.isAnimationEnabled()?t:null;u.diff(h).add(function(e){if(u.hasValue(e)){var n=u.getItemModel(e),i=f[c.type](u,e,n),r=d[c.type](u,e,n,i,a,g);u.setItemGraphicEl(e,r),s.add(r),o(r,u,e,n,i,t,a,"polar"===c.type)}}).update(function(e,n){var i=h.getItemGraphicEl(n);if(!u.hasValue(e))return void s.remove(i);var r=u.getItemModel(e),p=f[c.type](u,e,r);i?l.updateProps(i,{shape:p},g,e):i=d[c.type](u,e,r,p,a,g,!0),u.setItemGraphicEl(e,i),s.add(i),o(i,u,e,r,p,t,a,"polar"===c.type)}).remove(function(t){var e=h.getItemGraphicEl(t);"cartesian2d"===c.type?e&&i(t,g,e):e&&r(t,g,e)}).execute(),this._data=u},remove:function(t,e){var n=this.group,o=this._data;t.get("animation")?o&&o.eachItemGraphicEl(function(e){"sector"===e.type?r(e.dataIndex,t,e):i(e.dataIndex,t,e)}):n.removeAll()}}),d={cartesian2d:function(t,e,n,i,r,o,a){var u=new l.Rect({shape:s.extend({},i)});if(o){var h=u.shape,c=r?"height":"width",d={};h[c]=0,d[c]=i[c],l[a?"updateProps":"initProps"](u,{shape:d},o,e)}return u},polar:function(t,e,n,i,r,o,a){var u=new l.Sector({shape:s.extend({},i)});if(o){var h=u.shape,c=r?"r":"endAngle",d={};h[c]=r?0:i.startAngle,d[c]=i[c],l[a?"updateProps":"initProps"](u,{shape:d},o,e)}return u}},f={cartesian2d:function(t,e,n){var i=t.getItemLayout(e),r=a(n,i),o=i.width>0?1:-1,s=i.height>0?1:-1;return{x:i.x+o*r/2,y:i.y+s*r/2,width:i.width-o*r,height:i.height-s*r}},polar:function(t,e,n){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle}}};t.exports=c},function(t,e,n){var i=n(31)([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]);t.exports={getBarItemStyle:function(t){var e=i.call(this,t);if(this.getBorderLineDash){var n=this.getBorderLineDash();n&&(e.lineDash=n)}return e}}},function(t,e,n){function i(t){return"_"+t+"Type"}function r(t,e,n){var i=e.getItemVisual(n,"color"),r=e.getItemVisual(n,t),o=e.getItemVisual(n,t+"Size");if(r&&"none"!==r){f.isArray(o)||(o=[o,o]);var a=u.createSymbol(r,-o[0]/2,-o[1]/2,o[0],o[1],i);return a.name=t,a}}function o(t){var e=new c({name:"line"});return a(e.shape,t),e}function a(t,e){var n=e[0],i=e[1],r=e[2];t.x1=n[0],t.y1=n[1],t.x2=i[0],t.y2=i[1],t.percent=1,r?(t.cpx1=r[0],t.cpy1=r[1]):(t.cpx1=NaN,t.cpy1=NaN)}function s(){var t=this,e=t.childOfName("fromSymbol"),n=t.childOfName("toSymbol"),i=t.childOfName("label");if(e||n||!i.ignore){for(var r=1,o=this.parent;o;)o.scale&&(r/=o.scale[0]),o=o.parent;var a=t.childOfName("line");if(this.__dirty||a.__dirty){var s=a.shape.percent,l=a.pointAt(0),u=a.pointAt(s),c=h.sub([],u,l);if(h.normalize(c,c),e){e.attr("position",l);var d=a.tangentAt(0);e.attr("rotation",Math.PI/2-Math.atan2(d[1],d[0])),e.attr("scale",[r*s,r*s])}if(n){n.attr("position",u);var d=a.tangentAt(1);n.attr("rotation",-Math.PI/2-Math.atan2(d[1],d[0])),n.attr("scale",[r*s,r*s])}if(!i.ignore){i.attr("position",u);var f,p,g,m=5*r;if("end"===i.__position)f=[c[0]*m+u[0],c[1]*m+u[1]],p=c[0]>.8?"left":c[0]<-.8?"right":"center",g=c[1]>.8?"top":c[1]<-.8?"bottom":"middle";else if("middle"===i.__position){var v=s/2,d=a.tangentAt(v),x=[d[1],-d[0]],y=a.pointAt(v);x[1]>0&&(x[0]=-x[0],x[1]=-x[1]),f=[y[0]+x[0]*m,y[1]+x[1]*m],p="center",g="bottom";var _=-Math.atan2(d[1],d[0]);u[0]<l[0]&&(_=Math.PI+_),i.attr("rotation",_)}else f=[-c[0]*m+l[0],-c[1]*m+l[1]],p=c[0]>.8?"right":c[0]<-.8?"left":"center",g=c[1]>.8?"bottom":c[1]<-.8?"top":"middle";i.attr({style:{textVerticalAlign:i.__verticalAlign||g,textAlign:i.__textAlign||p},position:f,scale:[r,r]})}}}}function l(t,e,n){d.Group.call(this),this._createLine(t,e,n)}var u=n(24),h=n(6),c=n(195),d=n(3),f=n(1),p=n(4),g=["fromSymbol","toSymbol"],m=l.prototype;m.beforeUpdate=s,m._createLine=function(t,e,n){var a=t.hostModel,s=t.getItemLayout(e),l=o(s);l.shape.percent=0,d.initProps(l,{shape:{percent:1}},a,e),this.add(l);var u=new d.Text({name:"label"});this.add(u),f.each(g,function(n){var o=r(n,t,e);this.add(o),this[i(n)]=t.getItemVisual(e,n)},this),this._updateCommonStl(t,e,n)},m.updateData=function(t,e,n){var o=t.hostModel,s=this.childOfName("line"),l=t.getItemLayout(e),u={shape:{}};a(u.shape,l),d.updateProps(s,u,o,e),f.each(g,function(n){var o=t.getItemVisual(e,n),a=i(n);if(this[a]!==o){this.remove(this.childOfName(n));var s=r(n,t,e);this.add(s)}this[a]=o},this),this._updateCommonStl(t,e,n)},m._updateCommonStl=function(t,e,n){var i=t.hostModel,r=this.childOfName("line"),o=n&&n.lineStyle,a=n&&n.hoverLineStyle,s=n&&n.labelModel,l=n&&n.hoverLabelModel;if(!n||t.hasItemOption){var u=t.getItemModel(e);o=u.getModel("lineStyle.normal").getLineStyle(),a=u.getModel("lineStyle.emphasis").getLineStyle(),s=u.getModel("label.normal"),l=u.getModel("label.emphasis")}var h=t.getItemVisual(e,"color"),c=f.retrieve3(t.getItemVisual(e,"opacity"),o.opacity,1);r.useStyle(f.defaults({strokeNoScale:!0,fill:"none",stroke:h,opacity:c},o)),r.hoverStyle=a,f.each(g,function(t){var e=this.childOfName(t);e&&(e.setColor(h),e.setStyle({opacity:c}))},this);var m,v,x,y,_=s.getShallow("show"),b=l.getShallow("show"),w=this.childOfName("label");if(_||b){var S=i.getRawValue(e);v=null==S?v=t.getName(e):isFinite(S)?p.round(S):S,m=h||"#000",x=f.retrieve2(i.getFormattedLabel(e,"normal",t.dataType),v),y=f.retrieve2(i.getFormattedLabel(e,"emphasis",t.dataType),x)}if(_){var M=d.setTextStyle(w.style,s,{text:x},{autoColor:m});w.__textAlign=M.textAlign,w.__verticalAlign=M.textVerticalAlign,w.__position=s.get("position")||"middle"}else w.setStyle("text",null);b?w.hoverStyle={text:y,textFill:l.getTextColor(!0),fontStyle:l.getShallow("fontStyle"),fontWeight:l.getShallow("fontWeight"),fontSize:l.getShallow("fontSize"),fontFamily:l.getShallow("fontFamily")}:w.hoverStyle={text:null},w.ignore=!_&&!b,d.setHoverStyle(this)},m.highlight=function(){this.trigger("emphasis")},m.downplay=function(){this.trigger("normal")},m.updateLayout=function(t,e){this.setLinePoints(t.getItemLayout(e))},m.setLinePoints=function(t){var e=this.childOfName("line");a(e.shape,t),e.dirty()},f.inherits(l,d.Group),t.exports=l},function(t,e,n){function i(t){return isNaN(t[0])||isNaN(t[1])}function r(t){return!i(t[0])&&!i(t[1])}function o(t){this._ctor=t||s,this.group=new a.Group}var a=n(3),s=n(110),l=o.prototype;l.updateData=function(t){var e=this._lineData,n=this.group,i=this._ctor,o=t.hostModel,a={lineStyle:o.getModel("lineStyle.normal").getLineStyle(),hoverLineStyle:o.getModel("lineStyle.emphasis").getLineStyle(),labelModel:o.getModel("label.normal"),hoverLabelModel:o.getModel("label.emphasis")};t.diff(e).add(function(e){if(r(t.getItemLayout(e))){var o=new i(t,e,a);t.setItemGraphicEl(e,o),n.add(o)}}).update(function(o,s){var l=e.getItemGraphicEl(s);return r(t.getItemLayout(o))?(l?l.updateData(t,o,a):l=new i(t,o,a),t.setItemGraphicEl(o,l),void n.add(l)):void n.remove(l)}).remove(function(t){n.remove(e.getItemGraphicEl(t))}).execute(),this._lineData=t},l.updateLayout=function(){var t=this._lineData;t.eachItemGraphicEl(function(e,n){e.updateLayout(t,n)},this)},l.remove=function(){this.group.removeAll()},t.exports=o},function(t,e,n){var i=n(1),r=n(2),o=r.PRIORITY;n(113),n(114),r.registerVisual(i.curry(n(51),"line","circle","line")),r.registerLayout(i.curry(n(63),"line")),r.registerProcessor(o.PROCESSOR.STATISTIC,i.curry(n(153),"line")),n(32)},function(t,e,n){"use strict";var i=n(28),r=n(17);t.exports=r.extend({type:"series.line",dependencies:["grid","polar"],getInitialData:function(t,e){return i(t.data,this,e)},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clipOverflow:!0,label:{normal:{position:"top"}},lineStyle:{normal:{width:2,type:"solid"}},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:!1,connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}})},function(t,e,n){"use strict";function i(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++){var i=t[n],r=e[n];if(i[0]!==r[0]||i[1]!==r[1])return}return!0}}function r(t){return"number"==typeof t?t:t?.3:0}function o(t){var e=t.getGlobalExtent();if(t.onBand){var n=t.getBandWidth()/2-1,i=e[1]>e[0]?1:-1;e[0]+=i*n,e[1]-=i*n}return e}function a(t){return t>=0?1:-1}function s(t,e){var n=t.getBaseAxis(),i=t.getOtherAxis(n),r=n.onZero?0:i.scale.getExtent()[0],o=i.dim,s="x"===o||"radius"===o?1:0;return e.mapArray([o],function(i,l){for(var u,h=e.stackedOn;h&&a(h.get(o,l))===a(i);){u=h;break}var c=[];return c[s]=e.get(n.dim,l),c[1-s]=u?u.get(o,l,!0):r,t.dataToPoint(c)},!0)}function l(t,e,n){var i=o(t.getAxis("x")),r=o(t.getAxis("y")),a=t.getBaseAxis().isHorizontal(),s=Math.min(i[0],i[1]),l=Math.min(r[0],r[1]),u=Math.max(i[0],i[1])-s,h=Math.max(r[0],r[1])-l,c=n.get("lineStyle.normal.width")||2,d=n.get("clipOverflow")?c/2:Math.max(u,h);a?(l-=d,h+=2*d):(s-=d,u+=2*d);var f=new v.Rect({shape:{x:s,y:l,width:u,height:h}});return e&&(f.shape[a?"width":"height"]=0,v.initProps(f,{shape:{width:u,height:h}},n)),f}function u(t,e,n){var i=t.getAngleAxis(),r=t.getRadiusAxis(),o=r.getExtent(),a=i.getExtent(),s=Math.PI/180,l=new v.Sector({shape:{cx:t.cx,cy:t.cy,r0:o[0],r:o[1],startAngle:-a[0]*s,endAngle:-a[1]*s,clockwise:i.inverse}});return e&&(l.shape.endAngle=-a[0]*s,v.initProps(l,{shape:{endAngle:-a[1]*s}},n)),l}function h(t,e,n){return"polar"===t.type?u(t,e,n):l(t,e,n)}function c(t,e,n){for(var i=e.getBaseAxis(),r="x"===i.dim||"radius"===i.dim?0:1,o=[],a=0;a<t.length-1;a++){var s=t[a+1],l=t[a];o.push(l);var u=[];switch(n){case"end":u[r]=s[r],u[1-r]=l[1-r],o.push(u);break;case"middle":var h=(l[r]+s[r])/2,c=[];u[r]=c[r]=h,u[1-r]=l[1-r],c[1-r]=s[1-r],o.push(u),o.push(c);break;default:u[r]=l[r],u[1-r]=s[1-r],o.push(u)}}return t[a]&&o.push(t[a]),o}function d(t,e){var n=t.getVisual("visualMeta");if(n&&n.length&&t.count()){for(var i,r=n.length-1;r>=0;r--)if(n[r].dimension<2){i=n[r];break}if(i&&"cartesian2d"===e.type){var o=i.dimension,a=t.dimensions[o],s=e.getAxis(a),l=f.map(i.stops,function(t){return{coord:s.toGlobalCoord(s.dataToCoord(t.value)),color:t.color}}),u=l.length,h=i.outerColors.slice();u&&l[0].coord>l[u-1].coord&&(l.reverse(),h.reverse());var c=10,d=l[0].coord-c,p=l[u-1].coord+c,g=p-d;if(g<.001)return"transparent";f.each(l,function(t){t.offset=(t.coord-d)/g}),l.push({offset:u?l[u-1].offset:.5,color:h[1]||"transparent"}),l.unshift({offset:u?l[0].offset:.5,color:h[0]||"transparent"});var m=new v.LinearGradient(0,0,0,0,l,!0);return m[a]=d,m[a+"2"]=p,m}}}var f=n(1),p=n(46),g=n(56),m=n(115),v=n(3),x=n(5),y=n(97),_=n(30);t.exports=_.extend({type:"line",init:function(){var t=new v.Group,e=new p;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,e,n){var o=t.coordinateSystem,a=this.group,l=t.getData(),u=t.getModel("lineStyle.normal"),p=t.getModel("areaStyle.normal"),g=l.mapArray(l.getItemLayout,!0),m="polar"===o.type,v=this._coordSys,x=this._symbolDraw,y=this._polyline,_=this._polygon,b=this._lineGroup,w=t.get("animation"),S=!p.isEmpty(),M=s(o,l),T=t.get("showSymbol"),I=T&&!m&&!t.get("showAllSymbol")&&this._getSymbolIgnoreFunc(l,o),A=this._data;A&&A.eachItemGraphicEl(function(t,e){t.__temp&&(a.remove(t),A.setItemGraphicEl(e,null))}),T||x.remove(),a.add(b);var C=!m&&t.get("step");y&&v.type===o.type&&C===this._step?(S&&!_?_=this._newPolygon(g,M,o,w):_&&!S&&(b.remove(_),_=this._polygon=null),b.setClipPath(h(o,!1,t)),T&&x.updateData(l,I),l.eachItemGraphicEl(function(t){t.stopAnimation(!0)}),i(this._stackedOnPoints,M)&&i(this._points,g)||(w?this._updateAnimation(l,M,o,n,C):(C&&(g=c(g,o,C),M=c(M,o,C)),y.setShape({points:g}),_&&_.setShape({points:g,stackedOnPoints:M})))):(T&&x.updateData(l,I),C&&(g=c(g,o,C),M=c(M,o,C)),y=this._newPolyline(g,o,w),S&&(_=this._newPolygon(g,M,o,w)),b.setClipPath(h(o,!0,t)));var P=d(l,o)||l.getVisual("color");y.useStyle(f.defaults(u.getLineStyle(),{fill:"none",stroke:P,lineJoin:"bevel"}));var D=t.get("smooth");if(D=r(t.get("smooth")),y.setShape({smooth:D,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),_){var k=l.stackedOn,L=0;if(_.useStyle(f.defaults(p.getAreaStyle(),{fill:P,opacity:.7,lineJoin:"bevel"})),k){var O=k.hostModel;L=r(O.get("smooth"))}_.setShape({smooth:D,stackedOnSmooth:L,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})}this._data=l,this._coordSys=o,this._stackedOnPoints=M,this._points=g,this._step=C},dispose:function(){},highlight:function(t,e,n,i){var r=t.getData(),o=x.queryDataIndex(r,i);if(!(o instanceof Array)&&null!=o&&o>=0){var a=r.getItemGraphicEl(o);if(!a){var s=r.getItemLayout(o);if(!s)return;a=new g(r,o),a.position=s,a.setZ(t.get("zlevel"),t.get("z")),a.ignore=isNaN(s[0])||isNaN(s[1]),a.__temp=!0,r.setItemGraphicEl(o,a),a.stopSymbolAnimation(!0),this.group.add(a)}a.highlight()}else _.prototype.highlight.call(this,t,e,n,i)},downplay:function(t,e,n,i){var r=t.getData(),o=x.queryDataIndex(r,i);if(null!=o&&o>=0){var a=r.getItemGraphicEl(o);a&&(a.__temp?(r.setItemGraphicEl(o,null),this.group.remove(a)):a.downplay())}else _.prototype.downplay.call(this,t,e,n,i)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new y.Polyline({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e,e},_newPolygon:function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new y.Polygon({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(n),this._polygon=n,n},_getSymbolIgnoreFunc:function(t,e){var n=e.getAxesByScale("ordinal")[0];if(n&&n.isLabelIgnored)return f.bind(n.isLabelIgnored,n)},_updateAnimation:function(t,e,n,i,r){var o=this._polyline,a=this._polygon,s=t.hostModel,l=m(this._data,t,this._stackedOnPoints,e,this._coordSys,n),u=l.current,h=l.stackedOnCurrent,d=l.next,f=l.stackedOnNext;r&&(u=c(l.current,n,r),h=c(l.stackedOnCurrent,n,r),d=c(l.next,n,r),f=c(l.stackedOnNext,n,r)),o.shape.__points=l.current,o.shape.points=u,v.updateProps(o,{shape:{points:d}},s),a&&(a.setShape({points:u,stackedOnPoints:h}),v.updateProps(a,{shape:{points:d,stackedOnPoints:f}},s));for(var p=[],g=l.status,x=0;x<g.length;x++){var y=g[x].cmd;if("="===y){var _=t.getItemGraphicEl(g[x].idx1);_&&p.push({el:_,ptIdx:x})}}o.animators&&o.animators.length&&o.animators[0].during(function(){for(var t=0;t<p.length;t++){var e=p[t].el;e.attr("position",o.shape.__points[p[t].ptIdx])}})},remove:function(t){var e=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl(function(t,i){t.__temp&&(e.remove(t),n.setItemGraphicEl(i,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}})},function(t,e){function n(t){return t>=0?1:-1}function i(t,e,i){for(var r,o=t.getBaseAxis(),a=t.getOtherAxis(o),s=o.onZero?0:a.scale.getExtent()[0],l=a.dim,u="x"===l||"radius"===l?1:0,h=e.stackedOn,c=e.get(l,i);h&&n(h.get(l,i))===n(c);){r=h;break}var d=[];return d[u]=e.get(o.dim,i),d[1-u]=r?r.get(l,i,!0):s,t.dataToPoint(d)}function r(t,e){var n=[];return e.diff(t).add(function(t){n.push({cmd:"+",idx:t})}).update(function(t,e){n.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){n.push({cmd:"-",idx:t})}).execute(),n}t.exports=function(t,e,n,o,a,s){for(var l=r(t,e),u=[],h=[],c=[],d=[],f=[],p=[],g=[],m=s.dimensions,v=0;v<l.length;v++){var x=l[v],y=!0;switch(x.cmd){case"=":var _=t.getItemLayout(x.idx),b=e.getItemLayout(x.idx1);(isNaN(_[0])||isNaN(_[1]))&&(_=b.slice()),u.push(_),h.push(b),c.push(n[x.idx]),d.push(o[x.idx1]),g.push(e.getRawIndex(x.idx1));break;case"+":var w=x.idx;u.push(a.dataToPoint([e.get(m[0],w,!0),e.get(m[1],w,!0)])),h.push(e.getItemLayout(w).slice()),c.push(i(a,e,w)),d.push(o[w]),g.push(e.getRawIndex(w));break;case"-":var w=x.idx,S=t.getRawIndex(w);S!==w?(u.push(t.getItemLayout(w)),h.push(s.dataToPoint([t.get(m[0],w,!0),t.get(m[1],w,!0)])),c.push(n[w]),d.push(i(s,t,w)),g.push(S)):y=!1}y&&(f.push(x),p.push(p.length))}p.sort(function(t,e){return g[t]-g[e]});for(var M=[],T=[],I=[],A=[],C=[],v=0;v<p.length;v++){var w=p[v];M[v]=u[w],T[v]=h[w],I[v]=c[w],A[v]=d[w],C[v]=f[w]}return{current:M,next:T,stackedOnCurrent:I,stackedOnNext:A,status:C}}},function(t,e,n){var i=n(1),r=n(2);n(117),n(118),n(93)("pie",[{type:"pieToggleSelect",event:"pieselectchanged",method:"toggleSelected"},{type:"pieSelect",event:"pieselected",method:"select"},{type:"pieUnSelect",event:"pieunselected",method:"unSelect"}]),r.registerVisual(i.curry(n(88),"pie")),r.registerLayout(i.curry(n(120),"pie")),r.registerProcessor(i.curry(n(65),"pie"))},function(t,e,n){"use strict";var i=n(14),r=n(1),o=n(5),a=n(4),s=n(25),l=n(82),u=n(2).extendSeriesModel({type:"series.pie",init:function(t){u.superApply(this,"init",arguments),this.legendDataProvider=function(){return this.getRawData()},this.updateSelectedMap(t.data),this._defaultLabelLine(t)},mergeOption:function(t){u.superCall(this,"mergeOption",t),this.updateSelectedMap(this.option.data)},getInitialData:function(t,e){var n=s(["value"],t.data),r=new i(n,this);return r.initData(t.data),r},getDataParams:function(t){var e=this.getData(),n=u.superCall(this,"getDataParams",t),i=[];return e.each("value",function(t){i.push(t)}),n.percent=a.getPercentWithPrecision(i,t,e.hostModel.get("percentPrecision")),n.$vars.push("percent"),n},_defaultLabelLine:function(t){o.defaultEmphasis(t.labelLine,["show"]);var e=t.labelLine.normal,n=t.labelLine.emphasis;e.show=e.show&&t.label.normal.show,n.show=n.show&&t.label.emphasis.show},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,selectedOffset:10,avoidLabelOverlap:!0,percentPrecision:2,stillShowZeroSum:!0,label:{normal:{rotate:!1,show:!0,position:"outer"},emphasis:{}},labelLine:{normal:{show:!0,length:15,length2:15,smooth:!1,lineStyle:{width:1,type:"solid"}}},itemStyle:{normal:{borderWidth:1},emphasis:{}},animationType:"expansion",animationEasing:"cubicOut",data:[]}});r.mixin(u,l),t.exports=u},function(t,e,n){function i(t,e,n,i){var o=e.getData(),a=this.dataIndex,s=o.getName(a),l=e.get("selectedOffset");i.dispatchAction({type:"pieToggleSelect",from:t,name:s,seriesId:e.id}),o.each(function(t){r(o.getItemGraphicEl(t),o.getItemLayout(t),e.isSelected(o.getName(t)),l,n)})}function r(t,e,n,i,r){var o=(e.startAngle+e.endAngle)/2,a=Math.cos(o),s=Math.sin(o),l=n?i:0,u=[a*l,s*l];r?t.animate().when(200,{position:u}).start("bounceOut"):t.attr("position",u)}function o(t,e){function n(){o.ignore=o.hoverIgnore,s.ignore=s.hoverIgnore}function i(){o.ignore=o.normalIgnore,s.ignore=s.normalIgnore}a.Group.call(this);var r=new a.Sector({z2:2}),o=new a.Polyline,s=new a.Text;this.add(r),this.add(o),this.add(s),this.updateData(t,e,!0),this.on("emphasis",n).on("normal",i).on("mouseover",n).on("mouseout",i)}var a=n(3),s=n(1),l=o.prototype;l.updateData=function(t,e,n){function i(){l.stopAnimation(!0),l.animateTo({shape:{r:c.r+10}},300,"elasticOut")}function o(){l.stopAnimation(!0),l.animateTo({shape:{r:c.r}},300,"elasticOut")}var l=this.childAt(0),u=t.hostModel,h=t.getItemModel(e),c=t.getItemLayout(e),d=s.extend({},c);if(d.label=null,n){l.setShape(d);var f=u.getShallow("animationType");"scale"===f?(l.shape.r=c.r0,a.initProps(l,{shape:{r:c.r}},u,e)):(l.shape.endAngle=c.startAngle,a.updateProps(l,{shape:{endAngle:c.endAngle}},u,e))}else a.updateProps(l,{shape:d},u,e);var p=h.getModel("itemStyle"),g=t.getItemVisual(e,"color");l.useStyle(s.defaults({lineJoin:"bevel",fill:g},p.getModel("normal").getItemStyle())),l.hoverStyle=p.getModel("emphasis").getItemStyle();var m=h.getShallow("cursor");m&&l.attr("cursor",m),r(this,t.getItemLayout(e),h.get("selected"),u.get("selectedOffset"),u.get("animation")),l.off("mouseover").off("mouseout").off("emphasis").off("normal"),h.get("hoverAnimation")&&u.isAnimationEnabled()&&l.on("mouseover",i).on("mouseout",o).on("emphasis",i).on("normal",o),this._updateLabel(t,e),a.setHoverStyle(this)},l._updateLabel=function(t,e){var n=this.childAt(1),i=this.childAt(2),r=t.hostModel,o=t.getItemModel(e),s=t.getItemLayout(e),l=s.label,u=t.getItemVisual(e,"color");
a.updateProps(n,{shape:{points:l.linePoints||[[l.x,l.y],[l.x,l.y],[l.x,l.y]]}},r,e),a.updateProps(i,{style:{x:l.x,y:l.y}},r,e),i.attr({rotation:l.rotation,origin:[l.x,l.y],z2:10});var h=o.getModel("label.normal"),c=o.getModel("label.emphasis"),d=o.getModel("labelLine.normal"),f=o.getModel("labelLine.emphasis"),u=t.getItemVisual(e,"color");a.setLabelStyle(i.style,i.hoverStyle={},h,c,{labelFetcher:t.hostModel,labelDataIndex:e,defaultText:t.getName(e),autoColor:u,useInsideStyle:!!l.inside},{textAlign:l.textAlign,textVerticalAlign:l.verticalAlign,opacity:t.getItemVisual(e,"opacity")}),i.ignore=i.normalIgnore=!h.get("show"),i.hoverIgnore=!c.get("show"),n.ignore=n.normalIgnore=!d.get("show"),n.hoverIgnore=!f.get("show"),n.setStyle({stroke:u,opacity:t.getItemVisual(e,"opacity")}),n.setStyle(d.getModel("lineStyle").getLineStyle()),n.hoverStyle=f.getModel("lineStyle").getLineStyle();var p=d.get("smooth");p&&p===!0&&(p=.4),n.setShape({smooth:p})},s.inherits(o,a.Group);var u=n(30).extend({type:"pie",init:function(){var t=new a.Group;this._sectorGroup=t},render:function(t,e,n,r){if(!r||r.from!==this.uid){var a=t.getData(),l=this._data,u=this.group,h=e.get("animation"),c=!l,d=t.get("animationType"),f=s.curry(i,this.uid,t,h,n),p=t.get("selectedMode");if(a.diff(l).add(function(t){var e=new o(a,t);c&&"scale"!==d&&e.eachChild(function(t){t.stopAnimation(!0)}),p&&e.on("click",f),a.setItemGraphicEl(t,e),u.add(e)}).update(function(t,e){var n=l.getItemGraphicEl(e);n.updateData(a,t),n.off("click"),p&&n.on("click",f),u.add(n),a.setItemGraphicEl(t,n)}).remove(function(t){var e=l.getItemGraphicEl(t);u.remove(e)}).execute(),h&&c&&a.count()>0&&"scale"!==d){var g=a.getItemLayout(0),m=Math.max(n.getWidth(),n.getHeight())/2,v=s.bind(u.removeClipPath,u);u.setClipPath(this._createClipPath(g.cx,g.cy,m,g.startAngle,g.clockwise,v,t))}this._data=a}},dispose:function(){},_createClipPath:function(t,e,n,i,r,o,s){var l=new a.Sector({shape:{cx:t,cy:e,r0:0,r:n,startAngle:i,endAngle:i,clockwise:r}});return a.initProps(l,{shape:{endAngle:i+(r?1:-1)*Math.PI*2}},s,o),l},containPoint:function(t,e){var n=e.getData(),i=n.getItemLayout(0);if(i){var r=t[0]-i.cx,o=t[1]-i.cy,a=Math.sqrt(r*r+o*o);return a<=i.r&&a>=i.r0}}});t.exports=u},function(t,e,n){"use strict";function i(t,e,n,i,r,o,a){function s(e,n,i,r){for(var o=e;o<n;o++)if(t[o].y+=i,o>e&&o+1<n&&t[o+1].y>t[o].y+t[o].height)return void l(o,i/2);l(n-1,i/2)}function l(e,n){for(var i=e;i>=0&&(t[i].y-=n,!(i>0&&t[i].y>t[i-1].y+t[i-1].height));i--);}function u(t,e,n,i,r,o){for(var a=o>0?e?Number.MAX_VALUE:0:e?Number.MAX_VALUE:0,s=0,l=t.length;s<l;s++)if("center"!==t[s].position){var u=Math.abs(t[s].y-i),h=t[s].len,c=t[s].len2,d=u<r+h?Math.sqrt((r+h+c)*(r+h+c)-u*u):Math.abs(t[s].x-n);e&&d>=a&&(d=a-10),!e&&d<=a&&(d=a+10),t[s].x=n+d*o,a=d}}t.sort(function(t,e){return t.y-e.y});for(var h,c=0,d=t.length,f=[],p=[],g=0;g<d;g++)h=t[g].y-c,h<0&&s(g,d,-h,r),c=t[g].y+t[g].height;a-c<0&&l(d-1,c-a);for(var g=0;g<d;g++)t[g].y>=n?p.push(t[g]):f.push(t[g]);u(f,!1,e,n,i,r),u(p,!0,e,n,i,r)}function r(t,e,n,r,o,a){for(var s=[],l=[],u=0;u<t.length;u++)t[u].x<e?s.push(t[u]):l.push(t[u]);i(l,e,n,r,1,o,a),i(s,e,n,r,-1,o,a);for(var u=0;u<t.length;u++){var h=t[u].linePoints;if(h){var c=h[1][0]-h[2][0];t[u].x<e?h[2][0]=t[u].x+3:h[2][0]=t[u].x-3,h[1][1]=h[2][1]=t[u].y,h[1][0]=h[2][0]+c}}}var o=n(16);t.exports=function(t,e,n,i){var a,s,l=t.getData(),u=[],h=!1;l.each(function(n){var i,r,c,d,f=l.getItemLayout(n),p=l.getItemModel(n),g=p.getModel("label.normal"),m=g.get("position")||p.get("label.emphasis.position"),v=p.getModel("labelLine.normal"),x=v.get("length"),y=v.get("length2"),_=(f.startAngle+f.endAngle)/2,b=Math.cos(_),w=Math.sin(_);a=f.cx,s=f.cy;var S="inside"===m||"inner"===m;if("center"===m)i=f.cx,r=f.cy,d="center";else{var M=(S?(f.r+f.r0)/2*b:f.r*b)+a,T=(S?(f.r+f.r0)/2*w:f.r*w)+s;if(i=M+3*b,r=T+3*w,!S){var I=M+b*(x+e-f.r),A=T+w*(x+e-f.r),C=I+(b<0?-1:1)*y,P=A;i=C+(b<0?-5:5),r=P,c=[[M,T],[I,A],[C,P]]}d=S?"center":b>0?"left":"right"}var D=g.getFont(),k=g.get("rotate")?b<0?-_+Math.PI:-_:0,L=t.getFormattedLabel(n,"normal")||l.getName(n),O=o.getBoundingRect(L,D,d,"top");h=!!k,f.label={x:i,y:r,position:m,height:O.height,len:x,len2:y,linePoints:c,textAlign:d,verticalAlign:"middle",rotation:k,inside:S},S||u.push(f.label)}),!h&&t.get("avoidLabelOverlap")&&r(u,a,s,e,n,i)}},function(t,e,n){var i=n(4),r=i.parsePercent,o=n(119),a=n(1),s=2*Math.PI,l=Math.PI/180;t.exports=function(t,e,n,u){e.eachSeriesByType(t,function(t){var e=t.get("center"),u=t.get("radius");a.isArray(u)||(u=[0,u]),a.isArray(e)||(e=[e,e]);var h=n.getWidth(),c=n.getHeight(),d=Math.min(h,c),f=r(e[0],h),p=r(e[1],c),g=r(u[0],d/2),m=r(u[1],d/2),v=t.getData(),x=-t.get("startAngle")*l,y=t.get("minAngle")*l,_=0;v.each("value",function(t){!isNaN(t)&&_++});var b=v.getSum("value"),w=Math.PI/(b||_)*2,S=t.get("clockwise"),M=t.get("roseType"),T=t.get("stillShowZeroSum"),I=v.getDataExtent("value");I[0]=0;var A=s,C=0,P=x,D=S?1:-1;if(v.each("value",function(t,e){var n;if(isNaN(t))return void v.setItemLayout(e,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:S,cx:f,cy:p,r0:g,r:M?NaN:m});n="area"!==M?0===b&&T?w:t*w:s/_,n<y?(n=y,A-=y):C+=t;var r=P+D*n;v.setItemLayout(e,{angle:n,startAngle:P,endAngle:r,clockwise:S,cx:f,cy:p,r0:g,r:M?i.linearMap(t,I,[g,m]):m}),P=r},!0),A<s&&_)if(A<=.001){var k=s/_;v.each("value",function(t,e){if(!isNaN(t)){var n=v.getItemLayout(e);n.angle=k,n.startAngle=x+D*e*k,n.endAngle=x+D*(e+1)*k}})}else w=A/C,P=x,v.each("value",function(t,e){if(!isNaN(t)){var n=v.getItemLayout(e),i=n.angle===y?y:t*w;n.startAngle=P,n.endAngle=P+D*i,P+=D*i}});o(t,m,h,c)})}},function(t,e,n){"use strict";n(62),n(122)},function(t,e,n){var i=n(1),r=n(3),o=n(41),a=n(42),s=n(79),l=o.ifIgnoreOnTick,u=o.getInterval,h=["axisLine","axisLabel","axisTick","axisName"],c=["splitArea","splitLine"],d=a.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(t,e,n,a){this.group.removeAll();var l=this._axisGroup;if(this._axisGroup=new r.Group,this.group.add(this._axisGroup),t.get("show")){var u=t.getCoordSysModel(),f=s.layout(u,t),p=new o(t,f);i.each(h,p.add,p),this._axisGroup.add(p.getGroup()),i.each(c,function(e){t.get(e+".show")&&this["_"+e](t,u,f.labelInterval)},this),r.groupTransition(l,this._axisGroup,t),d.superCall(this,"render",t,e,n,a)}},_splitLine:function(t,e,n){var o=t.axis;if(!o.scale.isBlank()){var a=t.getModel("splitLine"),s=a.getModel("lineStyle"),h=s.get("color"),c=u(a,n);h=i.isArray(h)?h:[h];for(var d=e.coordinateSystem.getRect(),f=o.isHorizontal(),p=0,g=o.getTicksCoords(),m=o.scale.getTicks(),v=[],x=[],y=s.getLineStyle(),_=0;_<g.length;_++)if(!l(o,_,c)){var b=o.toGlobalCoord(g[_]);f?(v[0]=b,v[1]=d.y,x[0]=b,x[1]=d.y+d.height):(v[0]=d.x,v[1]=b,x[0]=d.x+d.width,x[1]=b);var w=p++%h.length;this._axisGroup.add(new r.Line(r.subPixelOptimizeLine({anid:"line_"+m[_],shape:{x1:v[0],y1:v[1],x2:x[0],y2:x[1]},style:i.defaults({stroke:h[w]},y),silent:!0})))}}},_splitArea:function(t,e,n){var o=t.axis;if(!o.scale.isBlank()){var a=t.getModel("splitArea"),s=a.getModel("areaStyle"),h=s.get("color"),c=e.coordinateSystem.getRect(),d=o.getTicksCoords(),f=o.scale.getTicks(),p=o.toGlobalCoord(d[0]),g=o.toGlobalCoord(d[0]),m=0,v=u(a,n),x=s.getAreaStyle();h=i.isArray(h)?h:[h];for(var y=1;y<d.length;y++)if(!l(o,y,v)){var _,b,w,S,M=o.toGlobalCoord(d[y]);o.isHorizontal()?(_=p,b=c.y,w=M-_,S=c.height):(_=c.x,b=g,w=c.width,S=M-b);var T=m++%h.length;this._axisGroup.add(new r.Rect({anid:"area_"+f[y],shape:{x:_,y:b,width:w,height:S},style:i.defaults({fill:h[T]},x),silent:!0})),p=_+w,g=b+S}}}});d.extend({type:"xAxis"}),d.extend({type:"yAxis"})},function(t,e,n){"use strict";function i(){}function r(t,e,n,i){o(d(n).lastProp,i)||(d(n).lastProp=i,e?c.updateProps(n,i,t):(n.stopAnimation(),n.attr(i)))}function o(t,e){if(u.isObject(t)&&u.isObject(e)){var n=!0;return u.each(e,function(e,i){n=n&&o(t[i],e)}),!!n}return t===e}function a(t,e){t[e.get("label.show")?"show":"hide"]()}function s(t){return{position:t.position.slice(),rotation:t.rotation||0}}function l(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)})}var u=n(1),h=n(15),c=n(3),d=n(5).makeGetter(),f=n(47),p=n(21),g=n(37),m=u.clone,v=u.bind;i.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,n,i){var o=e.get("value"),a=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=n,i||this._lastValue!==o||this._lastStatus!==a){this._lastValue=o,this._lastStatus=a;var s=this._group,h=this._handle;if(!a||"hide"===a)return s&&s.hide(),void(h&&h.hide());s&&s.show(),h&&h.show();var d={};this.makeElOption(d,o,t,e,n);var f=d.graphicKey;f!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=f;var p=this._moveAnimation=this.determineAnimation(t,e);if(s){var g=u.curry(r,e,p);this.updatePointerEl(s,d,g,e),this.updateLabelEl(s,d,g,e)}else s=this._group=new c.Group,this.createPointerEl(s,d,t,e),this.createLabelEl(s,d,t,e),n.getZr().add(s);l(s,e,!0),this._renderHandle(o)}},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var n=e.get("animation"),i=t.axis,r="category"===i.type,o=e.get("snap");if(!o&&!r)return!1;if("auto"===n||null==n){var a=this.animationThreshold;if(r&&i.getBandWidth()>a)return!0;if(o){var s=f.getAxisInfo(t).seriesDataCount,l=i.getExtent();return Math.abs(l[0]-l[1])/s>a}return!1}return n===!0},makeElOption:function(t,e,n,i,r){},createPointerEl:function(t,e,n,i){var r=e.pointer;if(r){var o=d(t).pointerEl=new c[r.type](m(e.pointer));t.add(o)}},createLabelEl:function(t,e,n,i){if(e.label){var r=d(t).labelEl=new c.Rect(m(e.label));t.add(r),a(r,i)}},updatePointerEl:function(t,e,n){var i=d(t).pointerEl;i&&(i.setStyle(e.pointer.style),n(i,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,n,i){var r=d(t).labelEl;r&&(r.setStyle(e.label.style),n(r,{shape:e.label.shape,position:e.label.position}),a(r,i))},_renderHandle:function(t){if(!this._dragging&&this.updateHandleTransform){var e=this._axisPointerModel,n=this._api.getZr(),i=this._handle,r=e.getModel("handle"),o=e.get("status");if(!r.get("show")||!o||"hide"===o)return i&&n.remove(i),void(this._handle=null);var a;this._handle||(a=!0,i=this._handle=c.createIcon(r.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){p.stop(t.event)},onmousedown:v(this._onHandleDragMove,this,0,0),drift:v(this._onHandleDragMove,this),ondragend:v(this._onHandleDragEnd,this)}),n.add(i)),l(i,e,!1);var s=["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];i.setStyle(r.getItemStyle(null,s));var h=r.get("size");u.isArray(h)||(h=[h,h]),i.attr("scale",[h[0]/2,h[1]/2]),g.createOrUpdate(this,"_doDispatchAxisPointer",r.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,a)}},_moveHandleToValue:function(t,e){r(this._axisPointerModel,!e&&this._moveAnimation,this._handle,s(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var n=this._handle;if(n){this._dragging=!0;var i=this.updateHandleTransform(s(n),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=i,n.stopAnimation(),n.attr(s(i)),d(n).lastProp=null,this._doDispatchAxisPointer()}},_doDispatchAxisPointer:function(){var t=this._handle;if(t){var e=this._payloadInfo,n=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:n.axis.dim,axisIndex:n.componentIndex}]})}},_onHandleDragEnd:function(t){this._dragging=!1;var e=this._handle;if(e){var n=this._axisPointerModel.get("value");this._moveHandleToValue(n),this._api.dispatchAction({type:"hideTip"})}},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),n=this._group,i=this._handle;e&&n&&(this._lastGraphicKey=null,n&&e.remove(n),i&&e.remove(i),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}}},i.prototype.constructor=i,h.enableClassExtend(i),t.exports=i},function(t,e,n){"use strict";function i(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}function r(t){return"x"===t.dim?0:1}var o=n(3),a=n(123),s=n(80),l=n(79),u=n(42),h=a.extend({makeElOption:function(t,e,n,r,o){var a=n.axis,u=a.grid,h=r.get("type"),d=i(u,a).getOtherAxis(a).getGlobalExtent(),f=a.toGlobalCoord(a.dataToCoord(e,!0));if(h&&"none"!==h){var p=s.buildElStyle(r),g=c[h](a,f,d,p);g.style=p,t.graphicKey=g.type,t.pointer=g}var m=l.layout(u.model,n);s.buildCartesianSingleLabelElOption(e,t,m,n,r,o)},getHandleTransform:function(t,e,n){var i=l.layout(e.axis.grid.model,e,{labelInside:!1});return i.labelMargin=n.get("handle.margin"),{position:s.getTransformedPosition(e.axis,t,i),rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,n,r){var o=n.axis,a=o.grid,s=o.getGlobalExtent(!0),l=i(a,o).getOtherAxis(o).getGlobalExtent(),u="x"===o.dim?0:1,h=t.position;h[u]+=e[u],h[u]=Math.min(s[1],h[u]),h[u]=Math.max(s[0],h[u]);var c=(l[1]+l[0])/2,d=[c,c];d[u]=h[u];var f=[{verticalAlign:"middle"},{align:"center"}];return{position:h,rotation:t.rotation,cursorPoint:d,tooltipOption:f[u]}}}),c={line:function(t,e,n,i){var a=s.makeLineShape([e,n[0]],[e,n[1]],r(t));return o.subPixelOptimizeLine({shape:a,style:i}),{type:"Line",shape:a}},shadow:function(t,e,n,i){var o=t.getBandWidth(),a=n[1]-n[0];return{type:"Rect",shape:s.makeRectShape([e-o/2,n[0]],[o,a],r(t))}}};u.registerAxisPointerClass("CartesianAxisPointer",h),t.exports=h},function(t,e,n){var i=n(1),r=n(5);t.exports=function(t,e){var n,o=[],a=t.seriesIndex;if(null==a||!(n=e.getSeriesByIndex(a)))return{point:[]};var s=n.getData(),l=r.queryDataIndex(s,t);if(null==l||i.isArray(l))return{point:[]};var u=s.getItemGraphicEl(l),h=n.coordinateSystem;if(n.getTooltipPosition)o=n.getTooltipPosition(l)||[];else if(h&&h.dataToPoint)o=h.dataToPoint(s.getValues(i.map(h.dimensions,function(t){return n.coordDimToDataDim(t)[0]}),l,!0))||[];else if(u){var c=u.getBoundingRect().clone();c.applyTransform(u.transform),o=[c.x+c.width/2,c.y+c.height/2]}return{point:o,el:u}}},function(t,e,n){function i(t,e){function n(n,i){t.on(n,function(n){var o=s(e);c(h(t).records,function(t){t&&i(t,n,o.dispatchAction)}),r(o.pendings,e)})}h(t).initialized||(h(t).initialized=!0,n("click",u.curry(a,"click")),n("mousemove",u.curry(a,"mousemove")),n("globalout",o))}function r(t,e){var n,i=t.showTip.length,r=t.hideTip.length;i?n=t.showTip[i-1]:r&&(n=t.hideTip[r-1]),n&&(n.dispatchAction=null,e.dispatchAction(n))}function o(t,e,n){t.handler("leave",null,n)}function a(t,e,n,i){e.handler(t,n,i)}function s(t){var e={showTip:[],hideTip:[]},n=function(i){var r=e[i.type];r?r.push(i):(i.dispatchAction=n,t.dispatchAction(i))};return{dispatchAction:n,pendings:e}}var l=n(10),u=n(1),h=n(5).makeGetter(),c=u.each,d={};d.register=function(t,e,n){if(!l.node){var r=e.getZr();h(r).records||(h(r).records={}),i(r,e);var o=h(r).records[t]||(h(r).records[t]={});o.handler=n}},d.unregister=function(t,e){if(!l.node){var n=e.getZr(),i=(h(n).records||{})[t];i&&(h(n).records[t]=null)}},t.exports=d},function(t,e,n){var i=n(1),r=n(81),o=n(2);o.registerAction("dataZoom",function(t,e){var n=r.createLinkedNodesFinder(i.bind(e.eachComponent,e,"dataZoom"),r.eachAxisDim,function(t,e){return t.get(e.axisIndex)}),o=[];e.eachComponent({mainType:"dataZoom",query:t},function(t,e){o.push.apply(o,n(t).nodes)}),i.each(o,function(e,n){e.setRawRange({start:t.start,end:t.end,startValue:t.startValue,endValue:t.endValue})})})},function(t,e,n){function i(t,e,n){n.getAxisProxy(t.name,e).reset(n)}function r(t,e,n){n.getAxisProxy(t.name,e).filterData(n)}var o=n(2);o.registerProcessor(function(t,e){t.eachComponent("dataZoom",function(t){t.eachTargetAxis(i),t.eachTargetAxis(r)}),t.eachComponent("dataZoom",function(t){var e=t.findRepresentativeAxisProxy(),n=e.getDataPercentWindow(),i=e.getDataValueWindow();t.setRawRange({start:n[0],end:n[1],startValue:i[0],endValue:i[1]},!0)})})},function(t,e,n){function i(t){var e=t[a];return e||(e=t[a]=[{}]),e}var r=n(1),o=r.each,a="\0_ec_hist_store",s={push:function(t,e){var n=i(t);o(e,function(e,i){for(var r=n.length-1;r>=0;r--){var o=n[r];if(o[i])break}if(r<0){var a=t.queryComponents({mainType:"dataZoom",subType:"select",id:i})[0];if(a){var s=a.getPercentRange();n[0][i]={dataZoomId:i,start:s[0],end:s[1]}}}}),n.push(e)},pop:function(t){var e=i(t),n=e[e.length-1];e.length>1&&e.pop();var r={};return o(n,function(t,n){for(var i=e.length-1;i>=0;i--){var t=e[i][n];if(t){r[n]=t;break}}}),r},clear:function(t){t[a]=null},count:function(t){return i(t).length}};t.exports=s},function(t,e,n){n(13).registerSubTypeDefaulter("dataZoom",function(){return"slider"})},function(t,e,n){function i(t){B.call(this),this._zr=t,this.group=new F.Group,this._brushType,this._brushOption,this._panels,this._track=[],this._dragging,this._covers=[],this._creatingCover,this._creatingPanel,this._enableGlobalPan,this._uid="brushController_"+nt++,this._handlers={},Z(it,function(t,e){this._handlers[e]=V.bind(t,this)},this)}function r(t,e){var n=t._zr;t._enableGlobalPan||G.take(n,Q,t._uid),Z(t._handlers,function(t,e){n.on(e,t)}),t._brushType=e.brushType,t._brushOption=V.merge(V.clone(et),e,!0)}function o(t){var e=t._zr;G.release(e,Q,t._uid),Z(t._handlers,function(t,n){e.off(n,t)}),t._brushType=t._brushOption=null}function a(t,e){var n=rt[e.brushType].createCover(t,e);return n.__brushOption=e,u(n,e),t.group.add(n),n}function s(t,e){var n=c(e);return n.endCreating&&(n.endCreating(t,e),u(e,e.__brushOption)),e}function l(t,e){var n=e.__brushOption;c(e).updateCoverShape(t,e,n.range,n)}function u(t,e){var n=e.z;null==n&&(n=U),t.traverse(function(t){t.z=n,t.z2=n})}function h(t,e){c(e).updateCommon(t,e),l(t,e)}function c(t){return rt[t.__brushOption.brushType]}function d(t,e,n){var i=t._panels;if(!i)return!0;var r,o=t._transform;return Z(i,function(t){t.isTargetByCursor(e,n,o)&&(r=t)}),r}function f(t,e){var n=t._panels;if(!n)return!0;var i=e.__brushOption.panelId;return null==i||n[i]}function p(t){var e=t._covers,n=e.length;return Z(e,function(e){t.group.remove(e)},t),e.length=0,!!n}function g(t,e){var n=q(t._covers,function(t){var e=t.__brushOption,n=V.clone(e.range);return{brushType:e.brushType,panelId:e.panelId,range:n}});t.trigger("brush",n,{isEnd:!!e.isEnd,removeOnClick:!!e.removeOnClick})}function m(t){var e=t._track;if(!e.length)return!1;var n=e[e.length-1],i=e[0],r=n[0]-i[0],o=n[1]-i[1],a=Y(r*r+o*o,.5);return a>$}function v(t){var e=t.length-1;return e<0&&(e=0),[t[0],t[e]]}function x(t,e,n,i){var r=new F.Group;return r.add(new F.Rect({name:"main",style:w(n),silent:!0,draggable:!0,cursor:"move",drift:W(t,e,r,"nswe"),ondragend:W(g,e,{isEnd:!0})})),Z(i,function(n){r.add(new F.Rect({name:n,style:{opacity:0},draggable:!0,silent:!0,invisible:!0,drift:W(t,e,r,n),ondragend:W(g,e,{isEnd:!0})}))}),r}function y(t,e,n,i){var r=i.brushStyle.lineWidth||0,o=X(r,K),a=n[0][0],s=n[1][0],l=a-r/2,u=s-r/2,h=n[0][1],c=n[1][1],d=h-o+r/2,f=c-o+r/2,p=h-a,g=c-s,m=p+r,v=g+r;b(t,e,"main",a,s,p,g),i.transformable&&(b(t,e,"w",l,u,o,v),b(t,e,"e",d,u,o,v),b(t,e,"n",l,u,m,o),b(t,e,"s",l,f,m,o),b(t,e,"nw",l,u,o,o),b(t,e,"ne",d,u,o,o),b(t,e,"sw",l,f,o,o),b(t,e,"se",d,f,o,o))}function _(t,e){var n=e.__brushOption,i=n.transformable,r=e.childAt(0);r.useStyle(w(n)),r.attr({silent:!i,cursor:i?"move":"default"}),Z(["w","e","n","s","se","sw","ne","nw"],function(n){var r=e.childOfName(n),o=T(t,n);r&&r.attr({silent:!i,invisible:!i,cursor:i?tt[o]+"-resize":null})})}function b(t,e,n,i,r,o,a){var s=e.childOfName(n);s&&s.setShape(D(P(t,e,[[i,r],[i+o,r+a]])))}function w(t){return V.defaults({strokeNoScale:!0},t.brushStyle)}function S(t,e,n,i){var r=[j(t,n),j(e,i)],o=[X(t,n),X(e,i)];return[[r[0],o[0]],[r[1],o[1]]]}function M(t){return F.getTransform(t.group)}function T(t,e){if(e.length>1){e=e.split("");var n=[T(t,e[0]),T(t,e[1])];return("e"===n[0]||"w"===n[0])&&n.reverse(),n.join("")}var i={w:"left",e:"right",n:"top",s:"bottom"},r={left:"w",right:"e",top:"n",bottom:"s"},n=F.transformDirection(i[e],M(t));return r[n]}function I(t,e,n,i,r,o,a,s){var l=i.__brushOption,u=t(l.range),c=C(n,o,a);Z(r.split(""),function(t){var e=J[t];u[e[0]][e[1]]+=c[e[0]]}),l.range=e(S(u[0][0],u[1][0],u[0][1],u[1][1])),h(n,i),g(n,{isEnd:!1})}function A(t,e,n,i,r){var o=e.__brushOption.range,a=C(t,n,i);Z(o,function(t){t[0]+=a[0],t[1]+=a[1]}),h(t,e),g(t,{isEnd:!1})}function C(t,e,n){var i=t.group,r=i.transformCoordToLocal(e,n),o=i.transformCoordToLocal(0,0);return[r[0]-o[0],r[1]-o[1]]}function P(t,e,n){var i=f(t,e);return i&&i!==!0?i.clipPath(n,t._transform):V.clone(n)}function D(t){var e=j(t[0][0],t[1][0]),n=j(t[0][1],t[1][1]),i=X(t[0][0],t[1][0]),r=X(t[0][1],t[1][1]);return{x:e,y:n,width:i-e,height:r-n}}function k(t,e,n){if(t._brushType){var i=t._zr,r=t._covers,o=d(t,e,n);if(!t._dragging)for(var a=0;a<r.length;a++){var s=r[a].__brushOption;if(o&&(o===!0||s.panelId===o.panelId)&&rt[s.brushType].contain(r[a],n[0],n[1]))return}o&&i.setCursorStyle("crosshair")}}function L(t){var e=t.event;e.preventDefault&&e.preventDefault()}function O(t,e,n){return t.childOfName("main").contain(e,n)}function z(t,e,n,i){var r,o=t._creatingCover,u=t._creatingPanel,h=t._brushOption;if(t._track.push(n.slice()),m(t)||o){if(u&&!o){"single"===h.brushMode&&p(t);var c=V.clone(h);c.brushType=E(c.brushType,u),c.panelId=u===!0?null:u.panelId,o=t._creatingCover=a(t,c),t._covers.push(o)}if(o){var f=rt[E(t._brushType,u)],g=o.__brushOption;g.range=f.getCreatingRange(P(t,o,t._track)),i&&(s(t,o),f.updateCommon(t,o)),l(t,o),r={isEnd:i}}}else i&&"single"===h.brushMode&&h.removeOnClick&&d(t,e,n)&&p(t)&&(r={isEnd:i,removeOnClick:!0});return r}function E(t,e){return"auto"===t?e.defaultBrushType:t}function R(t){if(this._dragging){L(t);var e=this.group.transformCoordToLocal(t.offsetX,t.offsetY),n=z(this,t,e,!0);this._dragging=!1,this._track=[],this._creatingCover=null,n&&g(this,n)}}function N(t){return{createCover:function(e,n){return x(W(I,function(e){var n=[e,[0,100]];return t&&n.reverse(),n},function(e){return e[t]}),e,n,[["w","e"],["n","s"]][t])},getCreatingRange:function(e){var n=v(e),i=j(n[0][t],n[1][t]),r=X(n[0][t],n[1][t]);return[i,r]},updateCoverShape:function(e,n,i,r){var o,a=f(e,n);if(a!==!0&&a.getLinearBrushOtherExtent)o=a.getLinearBrushOtherExtent(t,e._transform);else{var s=e._zr;o=[0,[s.getWidth(),s.getHeight()][1-t]]}var l=[i,o];t&&l.reverse(),y(e,n,l,r)},updateCommon:_,contain:O}}var B=n(23),V=n(1),F=n(3),G=n(133),H=n(44),W=V.curry,Z=V.each,q=V.map,j=Math.min,X=Math.max,Y=Math.pow,U=1e4,$=6,K=6,Q="globalPan",J={w:[0,0],e:[0,1],n:[1,0],s:[1,1]},tt={w:"ew",e:"ew",n:"ns",s:"ns",ne:"nesw",sw:"nesw",nw:"nwse",se:"nwse"},et={brushStyle:{lineWidth:2,stroke:"rgba(0,0,0,0.3)",fill:"rgba(0,0,0,0.1)"},transformable:!0,brushMode:"single",removeOnClick:!1},nt=0;i.prototype={constructor:i,enableBrush:function(t){return this._brushType&&o(this),t.brushType&&r(this,t),this},setPanels:function(t){if(t&&t.length){var e=this._panels={};V.each(t,function(t){e[t.panelId]=V.clone(t)})}else this._panels=null;return this},mount:function(t){t=t||{},this._enableGlobalPan=t.enableGlobalPan;var e=this.group;return this._zr.add(e),e.attr({position:t.position||[0,0],rotation:t.rotation||0,scale:t.scale||[1,1]}),this._transform=e.getLocalTransform(),this},eachCover:function(t,e){Z(this._covers,t,e)},updateCovers:function(t){function e(t,e){return(null!=t.id?t.id:o+e)+"-"+t.brushType}function n(t,n){return e(t.__brushOption,n)}function i(e,n){var i=t[e];if(null!=n&&l[n]===d)u[e]=l[n];else{var r=u[e]=null!=n?(l[n].__brushOption=i,l[n]):s(c,a(c,i));h(c,r)}}function r(t){l[t]!==d&&c.group.remove(l[t])}t=V.map(t,function(t){return V.merge(V.clone(et),t,!0)});var o="\0-brush-index-",l=this._covers,u=this._covers=[],c=this,d=this._creatingCover;return new H(l,t,n,e).add(i).update(i).remove(r).execute(),this},unmount:function(){return this.enableBrush(!1),p(this),this._zr.remove(this.group),this},dispose:function(){this.unmount(),this.off()}},V.mixin(i,B);var it={mousedown:function(t){if(this._dragging)R.call(this,t);else if(!t.target||!t.target.draggable){L(t);var e=this.group.transformCoordToLocal(t.offsetX,t.offsetY);this._creatingCover=null;var n=this._creatingPanel=d(this,t,e);n&&(this._dragging=!0,this._track=[e.slice()])}},mousemove:function(t){var e=this.group.transformCoordToLocal(t.offsetX,t.offsetY);if(k(this,t,e),this._dragging){L(t);var n=z(this,t,e,!1);n&&g(this,n)}},mouseup:R},rt={lineX:N(0),lineY:N(1),rect:{createCover:function(t,e){return x(W(I,function(t){return t},function(t){return t}),t,e,["w","e","n","s","se","sw","ne","nw"])},getCreatingRange:function(t){var e=v(t);return S(e[1][0],e[1][1],e[0][0],e[0][1])},updateCoverShape:function(t,e,n,i){y(t,e,n,i)},updateCommon:_,contain:O},polygon:{createCover:function(t,e){var n=new F.Group;return n.add(new F.Polyline({name:"main",style:w(e),silent:!0})),n},getCreatingRange:function(t){return t},endCreating:function(t,e){e.remove(e.childAt(0)),e.add(new F.Polygon({name:"main",draggable:!0,drift:W(A,t,e),ondragend:W(g,t,{isEnd:!0})}))},updateCoverShape:function(t,e,n,i){e.childAt(0).setShape({points:P(t,e,n)})},updateCommon:_,contain:O}};t.exports=i},function(t,e){var n={},i={axisPointer:1,tooltip:1,brush:1};n.onIrrelevantElement=function(t,e,n){var r=e.getComponentByElement(t.topTarget),o=r&&r.coordinateSystem;return r&&r!==n&&!i[r.mainType]&&o&&o.model!==n},t.exports=n},function(t,e,n){function i(t){return t[r]||(t[r]={})}var r="\0_ec_interaction_mutex",o={take:function(t,e,n){var r=i(t);r[e]=n},release:function(t,e,n){var r=i(t),o=r[e];o===n&&(r[e]=null)},isTaken:function(t,e){return!!i(t)[e]}};n(2).registerAction({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},function(){}),t.exports=o},function(t,e,n){var i=n(9),r=n(7),o=n(3);t.exports={layout:function(t,e,n){var r=e.getBoxLayoutParams(),o=e.get("padding"),a={width:n.getWidth(),height:n.getHeight()},s=i.getLayoutRect(r,a,o);i.box(e.get("orient"),t,e.get("itemGap"),s.width,s.height),i.positionElement(t,r,a,o)},makeBackground:function(t,e){var n=r.normalizeCssArray(e.get("padding")),i=e.getItemStyle(["color","opacity"]);i.fill=e.get("backgroundColor");var t=new o.Rect({shape:{x:t.x-n[3],y:t.y-n[0],width:t.width+n[1]+n[3],height:t.height+n[0]+n[2],r:e.get("borderRadius")},style:i,silent:!0,z2:-1});return t}}},function(t,e,n){"use strict";var i=n(1),r=n(11),o=n(2).extendComponentModel({type:"legend.plain",dependencies:["series"],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,n){this.mergeDefaultAndTheme(t,n),t.selected=t.selected||{}},mergeOption:function(t){o.superCall(this,"mergeOption",t)},optionUpdated:function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,n=0;n<t.length;n++){var i=t[n].get("name");if(this.isSelected(i)){this.select(i),e=!0;break}}!e&&this.select(t[0].get("name"))}},_updateData:function(t){var e=i.map(this.get("data")||[],function(t){return"string"!=typeof t&&"number"!=typeof t||(t={name:t}),new r(t,this,this.ecModel)},this);this._data=e;var n=i.map(t.getSeries(),function(t){return t.name});t.eachSeries(function(t){if(t.legendDataProvider){var e=t.legendDataProvider();n=n.concat(e.mapArray(e.getName))}}),this._availableNames=n},getData:function(){return this._data},select:function(t){var e=this.option.selected,n=this.get("selectedMode");if("single"===n){var r=this._data;i.each(r,function(t){e[t.get("name")]=!1})}e[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},toggleSelected:function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},isSelected:function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&i.indexOf(this._availableNames,t)>=0},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",textStyle:{color:"#333"},selectedMode:!0,tooltip:{show:!1}}});t.exports=o},function(t,e,n){function i(t,e){e.dispatchAction({type:"legendToggleSelect",name:t})}function r(t,e,n){var i=n.getZr().storage.getDisplayList()[0];i&&i.useHoverLayer||t.get("legendHoverLink")&&n.dispatchAction({type:"highlight",seriesName:t.name,name:e})}function o(t,e,n){var i=n.getZr().storage.getDisplayList()[0];i&&i.useHoverLayer||t.get("legendHoverLink")&&n.dispatchAction({type:"downplay",seriesName:t.name,name:e})}var a=n(1),s=n(24),l=n(3),u=n(134),h=n(9),c=a.curry,d=a.each,f=l.Group;t.exports=n(2).extendComponentView({type:"legend.plain",newlineDisabled:!1,init:function(){this.group.add(this._contentGroup=new f),this._backgroundEl},getContentGroup:function(){return this._contentGroup},render:function(t,e,n){if(this.resetInner(),t.get("show",!0)){var i=t.get("align");i&&"auto"!==i||(i="right"===t.get("left")&&"vertical"===t.get("orient")?"right":"left"),this.renderInner(i,t,e,n);var r=t.getBoxLayoutParams(),o={width:n.getWidth(),height:n.getHeight()},s=t.get("padding"),l=h.getLayoutRect(r,o,s),c=this.layoutInner(t,i,l),d=h.getLayoutRect(a.defaults({width:c.width,height:c.height},r),o,s);this.group.attr("position",[d.x-c.x,d.y-c.y]),this.group.add(this._backgroundEl=u.makeBackground(c,t))}},resetInner:function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl)},renderInner:function(t,e,n,s){var l=this.getContentGroup(),u=a.createHashMap(),h=e.get("selectedMode");d(e.getData(),function(a,d){var p=a.get("name");if(!this.newlineDisabled&&(""===p||"\n"===p))return void l.add(new f({newline:!0}));var g=n.getSeriesByName(p)[0];if(!u.get(p))if(g){var m=g.getData(),v=m.getVisual("color");"function"==typeof v&&(v=v(g.getDataParams(0)));var x=m.getVisual("legendSymbol")||"roundRect",y=m.getVisual("symbol"),_=this._createItem(p,d,a,e,x,y,t,v,h);_.on("click",c(i,p,s)).on("mouseover",c(r,g,null,s)).on("mouseout",c(o,g,null,s)),u.set(p,!0)}else n.eachRawSeries(function(n){if(!u.get(p)&&n.legendDataProvider){var l=n.legendDataProvider(),f=l.indexOfName(p);if(f<0)return;var g=l.getItemVisual(f,"color"),m="roundRect",v=this._createItem(p,d,a,e,m,null,t,g,h);v.on("click",c(i,p,s)).on("mouseover",c(r,n,p,s)).on("mouseout",c(o,n,p,s)),u.set(p,!0)}},this)},this)},_createItem:function(t,e,n,i,r,o,u,h,c){var d=i.get("itemWidth"),p=i.get("itemHeight"),g=i.get("inactiveColor"),m=i.isSelected(t),v=new f,x=n.getModel("textStyle"),y=n.get("icon"),_=n.getModel("tooltip"),b=_.parentModel;if(r=y||r,v.add(s.createSymbol(r,0,0,d,p,m?h:g)),!y&&o&&(o!==r||"none"==o)){var w=.8*p;"none"===o&&(o="circle"),v.add(s.createSymbol(o,(d-w)/2,(p-w)/2,w,w,m?h:g))}var S="left"===u?d+5:-5,M=u,T=i.get("formatter"),I=t;"string"==typeof T&&T?I=T.replace("{name}",null!=t?t:""):"function"==typeof T&&(I=T(t)),v.add(new l.Text({style:l.setTextStyle({},x,{text:I,x:S,y:p/2,textFill:m?x.getTextColor():g,textAlign:M,textVerticalAlign:"middle"})}));var A=new l.Rect({shape:v.getBoundingRect(),invisible:!0,tooltip:_.get("show")?a.extend({content:t,formatter:b.get("formatter",!0)||function(){return t},formatterParams:{componentType:"legend",legendIndex:i.componentIndex,name:t,$vars:["name"]}},_.option):null});return v.add(A),v.eachChild(function(t){t.silent=!0}),A.silent=!c,this.getContentGroup().add(v),l.setHoverStyle(v),v.__legendDataIndex=e,v},layoutInner:function(t,e,n){var i=this.getContentGroup();h.box(t.get("orient"),i,t.get("itemGap"),n.width,n.height);var r=i.getBoundingRect();return i.attr("position",[-r.x,-r.y]),this.group.getBoundingRect()}})},function(t,e,n){var i=n(1),r=n(33),o=function(t,e,n,i,o){r.call(this,t,e,n),this.type=i||"value",this.position=o||"bottom"};o.prototype={constructor:o,index:0,onZero:!1,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t;
},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},isLabelIgnored:function(t){if("category"===this.type){var e=this.getLabelInterval();return"function"==typeof e&&!e(t,this.scale.getLabel(t))||t%(e+1)}},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},i.inherits(o,r),t.exports=o},function(t,e,n){"use strict";function i(t){return this._axes[t]}var r=n(1),o=function(t){this._axes={},this._dimList=[],this.name=t||""};o.prototype={constructor:o,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return r.map(this._dimList,i,this)},getAxesByScale:function(t){return t=t.toLowerCase(),r.filter(this.getAxes(),function(e){return e.scale.type===t})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var n=this._dimList,i=t instanceof Array?[]:{},r=0;r<n.length;r++){var o=n[r],a=this._axes[o];i[o]=a[e](t[o])}return i}},t.exports=o},function(t,e,n){"use strict";function i(t){o.call(this,t)}var r=n(1),o=n(138);i.prototype={constructor:i,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e){var n=this.getAxis("x"),i=this.getAxis("y");return[n.toGlobalCoord(n.dataToCoord(t[0],e)),i.toGlobalCoord(i.dataToCoord(t[1],e))]},pointToData:function(t,e){var n=this.getAxis("x"),i=this.getAxis("y");return[n.coordToData(n.toLocalCoord(t[0]),e),i.coordToData(i.toLocalCoord(t[1]),e)]},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")}},r.inherits(i,o),t.exports=i},function(t,e,n){"use strict";n(62);var i=n(13);t.exports=i.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}})},function(t,e,n){var i=n(28),r=n(24),o=n(18),a=n(43),s=n(11),l=n(1);t.exports={createList:function(t){var e=t.get("data");return i(e,t,t.ecModel)},completeDimensions:n(25),createSymbol:r.createSymbol,createScale:function(t,e){var n=e;e instanceof s||(n=new s(e),l.mixin(n,a));var i=o.createScaleByModel(n);return i.setExtent(t[0],t[1]),o.niceScaleExtent(i,n),i},mixinAxisModelCommonMethods:function(t){l.mixin(t,a)}}},function(t,e,n){var i=n(3),r=n(1),o=Math.PI;t.exports=function(t,e){e=e||{},r.defaults(e,{text:"loading",color:"#c23531",textColor:"#000",maskColor:"rgba(255, 255, 255, 0.8)",zlevel:0});var n=new i.Rect({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4}),a=new i.Arc({shape:{startAngle:-o/2,endAngle:-o/2+.1,r:10},style:{stroke:e.color,lineCap:"round",lineWidth:5},zlevel:e.zlevel,z:10001}),s=new i.Rect({style:{fill:"none",text:e.text,textPosition:"right",textDistance:10,textFill:e.textColor},zlevel:e.zlevel,z:10001});a.animateShape(!0).when(1e3,{endAngle:3*o/2}).start("circularInOut"),a.animateShape(!0).when(1e3,{startAngle:3*o/2}).delay(300).start("circularInOut");var l=new i.Group;return l.add(a),l.add(s),l.add(n),l.resize=function(){var e=t.getWidth()/2,i=t.getHeight()/2;a.setShape({cx:e,cy:i});var r=a.shape.r;s.setShape({x:e-r,y:i-r,width:2*r,height:2*r}),n.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},l.resize(),l}},function(t,e,n){function i(t,e){h.each(e,function(e,n){y.hasClass(n)||("object"==typeof e?t[n]=t[n]?h.merge(t[n],e,!1):h.clone(e):null==t[n]&&(t[n]=e))})}function r(t){t=t,this.option={},this.option[b]=1,this._componentsMap=h.createHashMap({series:[]}),this._seriesIndices=null,i(t,this._theme.option),h.merge(t,_,!1),this.mergeOption(t)}function o(t,e){h.isArray(e)||(e=e?[e]:[]);var n={};return f(e,function(e){n[e]=(t.get(e)||[]).slice()}),n}function a(t,e,n){var i=e.type?e.type:n?n.subType:y.determineSubType(t,e);return i}function s(t){return g(t,function(t){return t.componentIndex})||[]}function l(t,e){return e.hasOwnProperty("subType")?p(t,function(t){return t.subType===e.subType}):t}function u(t){}var h=n(1),c=n(5),d=n(11),f=h.each,p=h.filter,g=h.map,m=h.isArray,v=h.indexOf,x=h.isObject,y=n(13),_=n(145),b="\0_ec_inner",w=d.extend({constructor:w,init:function(t,e,n,i){n=n||{},this.option=null,this._theme=new d(n),this._optionManager=i},setOption:function(t,e){h.assert(!(b in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e=!1,n=this._optionManager;if(!t||"recreate"===t){var i=n.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(i)):r.call(this,i),e=!0}if("timeline"!==t&&"media"!==t||this.restoreData(),!t||"recreate"===t||"timeline"===t){var o=n.getTimelineOption(this);o&&(this.mergeOption(o),e=!0)}if(!t||"recreate"===t||"media"===t){var a=n.getMediaOption(this,this._api);a.length&&f(a,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(t){function e(e,r){var l=c.normalizeToArray(t[e]),u=c.mappingToExists(i.get(e),l);c.makeIdAndName(u),f(u,function(t,n){var i=t.option;x(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=a(e,i,t.exist))});var d=o(i,r);n[e]=[],i.set(e,[]),f(u,function(t,r){var o=t.exist,a=t.option;if(h.assert(x(a)||o,"Empty component definition"),a){var s=y.getClass(e,t.keyInfo.subType,!0);if(o&&o instanceof s)o.name=t.keyInfo.name,o.mergeOption(a,this),o.optionUpdated(a,!1);else{var l=h.extend({dependentModels:d,componentIndex:r},t.keyInfo);o=new s(a,this,this,l),h.extend(o,l),o.init(a,this,this,l),o.optionUpdated(null,!0)}}else o.mergeOption({},this),o.optionUpdated({},!1);i.get(e)[r]=o,n[e][r]=o.option},this),"series"===e&&(this._seriesIndices=s(i.get("series")))}var n=this.option,i=this._componentsMap,r=[];f(t,function(t,e){null!=t&&(y.hasClass(e)?r.push(e):n[e]=null==n[e]?h.clone(t):h.merge(n[e],t,!0))}),y.topologicalTravel(r,y.getAllClassMainTypes(),e,this),this._seriesIndices=this._seriesIndices||[]},getOption:function(){var t=h.clone(this.option);return f(t,function(e,n){if(y.hasClass(n)){for(var e=c.normalizeToArray(e),i=e.length-1;i>=0;i--)c.isIdInner(e[i])&&e.splice(i,1);t[n]=e}}),delete t[b],t},getTheme:function(){return this._theme},getComponent:function(t,e){var n=this._componentsMap.get(t);if(n)return n[e||0]},queryComponents:function(t){var e=t.mainType;if(!e)return[];var n=t.index,i=t.id,r=t.name,o=this._componentsMap.get(e);if(!o||!o.length)return[];var a;if(null!=n)m(n)||(n=[n]),a=p(g(n,function(t){return o[t]}),function(t){return!!t});else if(null!=i){var s=m(i);a=p(o,function(t){return s&&v(i,t.id)>=0||!s&&t.id===i})}else if(null!=r){var u=m(r);a=p(o,function(t){return u&&v(r,t.name)>=0||!u&&t.name===r})}else a=o.slice();return l(a,t)},findComponents:function(t){function e(t){var e=r+"Index",n=r+"Id",i=r+"Name";return!t||null==t[e]&&null==t[n]&&null==t[i]?null:{mainType:r,index:t[e],id:t[n],name:t[i]}}function n(e){return t.filter?p(e,t.filter):e}var i=t.query,r=t.mainType,o=e(i),a=o?this.queryComponents(o):this._componentsMap.get(r);return n(l(a,t))},eachComponent:function(t,e,n){var i=this._componentsMap;if("function"==typeof t)n=e,e=t,i.each(function(t,i){f(t,function(t,r){e.call(n,i,t,r)})});else if(h.isString(t))f(i.get(t),e,n);else if(x(t)){var r=this.findComponents(t);f(r,e,n)}},getSeriesByName:function(t){var e=this._componentsMap.get("series");return p(e,function(e){return e.name===t})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(t){var e=this._componentsMap.get("series");return p(e,function(e){return e.subType===t})},getSeries:function(){return this._componentsMap.get("series").slice()},eachSeries:function(t,e){u(this),f(this._seriesIndices,function(n){var i=this._componentsMap.get("series")[n];t.call(e,i,n)},this)},eachRawSeries:function(t,e){f(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(t,e,n){u(this),f(this._seriesIndices,function(i){var r=this._componentsMap.get("series")[i];r.subType===t&&e.call(n,r,i)},this)},eachRawSeriesByType:function(t,e,n){return f(this.getSeriesByType(t),e,n)},isSeriesFiltered:function(t){return u(this),h.indexOf(this._seriesIndices,t.componentIndex)<0},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){u(this);var n=p(this._componentsMap.get("series"),t,e);this._seriesIndices=s(n)},restoreData:function(){var t=this._componentsMap;this._seriesIndices=s(t.get("series"));var e=[];t.each(function(t,n){e.push(n)}),y.topologicalTravel(e,y.getAllClassMainTypes(),function(e,n){f(t.get(e),function(t){t.restoreData()})})}});h.mixin(w,n(64)),t.exports=w},function(t,e,n){function i(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function r(t,e,n){var i,r,o=[],a=[],s=t.timeline;if(t.baseOption&&(r=t.baseOption),(s||t.options)&&(r=r||{},o=(t.options||[]).slice()),t.media){r=r||{};var l=t.media;d(l,function(t){t&&t.option&&(t.query?a.push(t):i||(i=t))})}return r||(r=t),r.timeline||(r.timeline=s),d([r].concat(o).concat(u.map(a,function(t){return t.option})),function(t){d(e,function(e){e(t,n)})}),{baseOption:r,timelineOptions:o,mediaDefault:i,mediaList:a}}function o(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return u.each(t,function(t,e){var n=e.match(m);if(n&&n[1]&&n[2]){var o=n[1],s=n[2].toLowerCase();a(i[s],t,o)||(r=!1)}}),r}function a(t,e,n){return"min"===n?t>=e:"max"===n?t<=e:t===e}function s(t,e){return t.join(",")===e.join(",")}function l(t,e){e=e||{},d(e,function(e,n){if(null!=e){var i=t[n];if(c.hasClass(n)){e=h.normalizeToArray(e),i=h.normalizeToArray(i);var r=h.mappingToExists(i,e);t[n]=p(r,function(t){return t.option&&t.exist?g(t.exist,t.option,!0):t.exist||t.option})}else t[n]=g(i,e,!0)}})}var u=n(1),h=n(5),c=n(13),d=u.each,f=u.clone,p=u.map,g=u.merge,m=/^(min|max)?(.+)$/;i.prototype={constructor:i,setOption:function(t,e){t=f(t,!0);var n=this._optionBackup,i=r.call(this,t,e,!n);this._newBaseOption=i.baseOption,n?(l(n.baseOption,i.baseOption),i.timelineOptions.length&&(n.timelineOptions=i.timelineOptions),i.mediaList.length&&(n.mediaList=i.mediaList),i.mediaDefault&&(n.mediaDefault=i.mediaDefault)):this._optionBackup=i},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=p(e.timelineOptions,f),this._mediaList=p(e.mediaList,f),this._mediaDefault=f(e.mediaDefault),this._currentMediaIndices=[],f(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=f(n[i.getCurrentIndex()],!0))}return e},getMediaOption:function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,a=[],l=[];if(!i.length&&!r)return l;for(var u=0,h=i.length;u<h;u++)o(i[u].query,e,n)&&a.push(u);return!a.length&&r&&(a=[-1]),a.length&&!s(a,this._currentMediaIndices)&&(l=p(a,function(t){return f(t===-1?r.option:i[t].option)})),this._currentMediaIndices=a,l}},t.exports=i},function(t,e){var n="";"undefined"!=typeof navigator&&(n=navigator.platform||""),t.exports={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],textStyle:{fontFamily:n.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1}},function(t,e,n){t.exports={getAreaStyle:n(31)([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]])}},function(t,e){t.exports={getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}}},function(t,e,n){var i=n(31)([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]);t.exports={getItemStyle:function(t,e){var n=i.call(this,t,e),r=this.getBorderLineDash();return r&&(n.lineDash=r),n},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}}},function(t,e,n){var i=n(31)([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]);t.exports={getLineStyle:function(t){var e=i.call(this,t),n=this.getLineDash(e.lineWidth);return n&&(e.lineDash=n),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),n=Math.max(t,2),i=4*t;return"solid"===e||null==e?null:"dashed"===e?[i,i]:[n,n]}}},function(t,e,n){var i=n(16),r=n(3),o=["textStyle","color"];t.exports={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(o):null)},getFont:function(){return r.getFont({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return i.getBoundingRect(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("rich"),this.getShallow("truncateText"))}}},function(t,e,n){function i(t,e){e=e.split(",");for(var n=t,i=0;i<e.length&&(n=n&&n[e[i]],null!=n);i++);return n}function r(t,e,n,i){e=e.split(",");for(var r,o=t,a=0;a<e.length-1;a++)r=e[a],null==o[r]&&(o[r]={}),o=o[r];(i||null==o[e[a]])&&(o[e[a]]=n)}function o(t){c(l,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}var a=n(1),s=n(152),l=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],u=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],h=["bar","boxplot","candlestick","chord","effectScatter","funnel","gauge","lines","graph","heatmap","line","map","parallel","pie","radar","sankey","scatter","treemap"],c=a.each;t.exports=function(t,e){s(t,e);var n=t.series;c(a.isArray(n)?n:[n],function(t){if(a.isObject(t)){var e=t.type;if("pie"!==e&&"gauge"!==e||null!=t.clockWise&&(t.clockwise=t.clockWise),"gauge"===e){var n=i(t,"pointer.color");null!=n&&r(t,"itemStyle.normal.color",n)}for(var s=0;s<h.length;s++)if(h[s]===t.type){o(t);break}}}),t.dataRange&&(t.visualMap=t.dataRange),c(u,function(e){var n=t[e];n&&(a.isArray(n)||(n=[n]),c(n,function(t){o(t)}))})}},function(t,e,n){function i(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=f.length;n<i;n++){var r=f[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?u.merge(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?u.merge(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function r(t,e){var n=d(t)&&t[e],i=d(n)&&n.textStyle;if(i)for(var r=0,o=h.TEXT_STYLE_OPTIONS.length;r<o;r++){var e=h.TEXT_STYLE_OPTIONS[r];i.hasOwnProperty(e)&&(n[e]=i[e])}}function o(t){d(t)&&(r(t,"normal"),r(t,"emphasis"))}function a(t){if(d(t)){i(t),o(t.label),o(t.upperLabel),o(t.edgeLabel);var e=t.markPoint;i(e),o(e&&e.label);var n=t.markLine;i(t.markLine),o(n&&n.label);var a=t.markArea;o(a&&a.label),r(t,"axisLabel"),r(t,"title"),r(t,"detail");var s=t.data;if(s){for(var l=0;l<s.length;l++)i(s[l]),o(s[l]&&s[l].label);var e=t.markPoint;if(e&&e.data)for(var h=e.data,l=0;l<h.length;l++)i(h[l]),o(h[l]&&h[l].label);var n=t.markLine;if(n&&n.data)for(var c=n.data,l=0;l<c.length;l++)u.isArray(c[l])?(i(c[l][0]),o(c[l][0]&&c[l][0].label),i(c[l][1]),o(c[l][1]&&c[l][1].label)):(i(c[l]),o(c[l]&&c[l].label))}}}function s(t){return u.isArray(t)?t:t?[t]:[]}function l(t){return(u.isArray(t)?t[0]:t)||{}}var u=n(1),h=n(5),c=u.each,d=u.isObject,f=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];t.exports=function(t,e){c(s(t.series),function(t){d(t)&&a(t)});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),c(n,function(e){c(s(t[e]),function(t){t&&(r(t,"axisLabel"),r(t.axisPointer,"label"))})}),c(s(t.parallel),function(t){var e=t&&t.parallelAxisDefault;r(e,"axisLabel"),r(e&&e.axisPointer,"label")}),c(s(t.calendar),function(t){r(t,"dayLabel"),r(t,"monthLabel"),r(t,"yearLabel")}),c(s(t.radar),function(t){r(t,"name")}),c(s(t.geo),function(t){d(t)&&(o(t.label),c(s(t.regions),function(t){o(t.label)}))}),o(l(t.timeline).label),r(l(t.axisPointer),"label"),r(l(t.tooltip).axisPointer,"label")}},function(t,e){var n={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-(1/0),n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return e},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return e},nearest:function(t){return t[0]}},i=function(t,e){return Math.round(t.length/2)};t.exports=function(t,e,r){e.eachSeriesByType(t,function(t){var e=t.getData(),r=t.get("sampling"),o=t.coordinateSystem;if("cartesian2d"===o.type&&r){var a=o.getBaseAxis(),s=o.getOtherAxis(a),l=a.getExtent(),u=l[1]-l[0],h=Math.round(e.count()/u);if(h>1){var c;"string"==typeof r?c=n[r]:"function"==typeof r&&(c=r),c&&(e=e.downSample(s.dim,1/h,c,i),t.setData(e))}}},this)}},function(t,e,n){function i(t,e){return c(t,h(e))}var r=n(1),o=n(34),a=n(4),s=n(45),l=o.prototype,u=s.prototype,h=a.getPrecisionSafe,c=a.round,d=Math.floor,f=Math.ceil,p=Math.pow,g=Math.log,m=o.extend({type:"log",base:10,$constructor:function(){o.apply(this,arguments),this._originalScale=new s},getTicks:function(){var t=this._originalScale,e=this._extent,n=t.getExtent();return r.map(u.getTicks.call(this),function(r){var o=a.round(p(this.base,r));return o=r===e[0]&&t.__fixMin?i(o,n[0]):o,o=r===e[1]&&t.__fixMax?i(o,n[1]):o},this)},getLabel:u.getLabel,scale:function(t){return t=l.scale.call(this,t),p(this.base,t)},setExtent:function(t,e){var n=this.base;t=g(t)/g(n),e=g(e)/g(n),u.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=l.getExtent.call(this);e[0]=p(t,e[0]),e[1]=p(t,e[1]);var n=this._originalScale,r=n.getExtent();return n.__fixMin&&(e[0]=i(e[0],r[0])),n.__fixMax&&(e[1]=i(e[1],r[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=g(t[0])/g(e),t[1]=g(t[1])/g(e),l.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getDataExtent(e,!0,function(t){return t>0}))},niceTicks:function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n===1/0||n<=0)){var i=a.quantity(n),r=t/n*i;for(r<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&Math.abs(i)>0;)i*=10;var o=[a.round(f(e[0]/i)*i),a.round(d(e[1]/i)*i)];this._interval=i,this._niceExtent=o}},niceExtent:function(t){u.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});r.each(["contain","normalize"],function(t){m.prototype[t]=function(e){return e=g(e)/g(this.base),l[t].call(this,e)}}),m.create=function(){return new m},t.exports=m},function(t,e,n){var i=n(1),r=n(34),o=r.prototype,a=r.extend({type:"ordinal",init:function(t,e){this._data=t,this._extent=e||[0,t.length-1]},parse:function(t){return"string"==typeof t?i.indexOf(this._data,t):Math.round(t)},contain:function(t){return t=this.parse(t),o.contain.call(this,t)&&null!=this._data[t]},normalize:function(t){return o.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(o.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push(n),n++;return t},getLabel:function(t){return this._data[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getDataExtent(e,!1))},niceTicks:i.noop,niceExtent:i.noop});a.create=function(){return new a},t.exports=a},function(t,e,n){var i=n(1),r=n(4),o=n(7),a=n(66),s=n(45),l=s.prototype,u=Math.ceil,h=Math.floor,c=1e3,d=60*c,f=60*d,p=24*f,g=function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][2]<e?n=r+1:i=r}return n},m=s.extend({type:"time",getLabel:function(t){var e=this._stepLvl,n=new Date(t);return o.formatTime(e[0],n,this.getSetting("useUTC"))},niceExtent:function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=p,e[1]+=p),e[1]===-(1/0)&&e[0]===1/0){var n=new Date;e[1]=new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-p}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var i=this._interval;t.fixMin||(e[0]=r.round(h(e[0]/i)*i)),t.fixMax||(e[1]=r.round(u(e[1]/i)*i))},niceTicks:function(t,e,n){var i=this.getSetting("useUTC")?0:60*r.getTimezoneOffset()*1e3;t=t||10;var o=this._extent,s=o[1]-o[0],l=s/t;null!=e&&l<e&&(l=e),null!=n&&l>n&&(l=n);var c=v.length,d=g(v,l,0,c),f=v[Math.min(d,c-1)],p=f[2];if("year"===f[0]){var m=s/p,x=r.nice(m/t,!0);p*=x}var y=[Math.round(u((o[0]-i)/p)*p+i),Math.round(h((o[1]-i)/p)*p+i)];a.fixExtent(y,o),this._stepLvl=f,this._interval=p,this._niceExtent=y},parse:function(t){return+r.parseDate(t)}});i.each(["contain","normalize"],function(t){m.prototype[t]=function(e){return l[t].call(this,this.parse(e))}});var v=[["hh:mm:ss",1,c],["hh:mm:ss",5,5*c],["hh:mm:ss",10,10*c],["hh:mm:ss",15,15*c],["hh:mm:ss",30,30*c],["hh:mm\nMM-dd",1,d],["hh:mm\nMM-dd",5,5*d],["hh:mm\nMM-dd",10,10*d],["hh:mm\nMM-dd",15,15*d],["hh:mm\nMM-dd",30,30*d],["hh:mm\nMM-dd",1,f],["hh:mm\nMM-dd",2,2*f],["hh:mm\nMM-dd",6,6*f],["hh:mm\nMM-dd",12,12*f],["MM-dd\nyyyy",1,p],["week",7,7*p],["month",1,31*p],["quarter",3,380*p/4],["half-year",6,380*p/2],["year",1,380*p]];m.create=function(t){return new m({useUTC:t.ecModel.get("useUTC")})},t.exports=m},function(t,e,n){var i=n(39);t.exports=function(t){function e(e){var n=(e.visualColorAccessPath||"itemStyle.normal.color").split("."),r=e.getData(),o=e.get(n)||e.getColorFromPalette(e.get("name"));r.setVisual("color",o),t.isSeriesFiltered(e)||("function"!=typeof o||o instanceof i||r.each(function(t){r.setItemVisual(t,"color",o(e.getDataParams(t)))}),r.each(function(t){var e=r.getItemModel(t),i=e.get(n,!0);null!=i&&r.setItemVisual(t,"color",i)}))}t.eachRawSeries(e)}},function(t,e,n){"use strict";function i(t,e,n){return{type:t,event:n,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch}}function r(){}function o(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i,r=t;r;){if(r.clipPath&&!r.clipPath.contain(e,n))return!1;r.silent&&(i=!0),r=r.parent}return!i||u}return!1}var a=n(1),s=n(184),l=n(23),u="silent";r.prototype.dispose=function(){};var h=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],c=function(t,e,n,i){l.call(this),this.storage=t,this.painter=e,this.painterRoot=i,n=n||new r,this.proxy=n,n.handler=this,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,s.call(this),a.each(h,function(t){n.on&&n.on(t,this[t],this)},this)};c.prototype={constructor:c,mousemove:function(t){var e=t.zrX,n=t.zrY,i=this._hovered,r=i.target;r&&!r.__zr&&(i=this.findHover(i.x,i.y),r=i.target);var o=this._hovered=this.findHover(e,n),a=o.target,s=this.proxy;s.setCursor&&s.setCursor(a?a.cursor:"default"),r&&a!==r&&this.dispatchToElement(i,"mouseout",t),this.dispatchToElement(o,"mousemove",t),a&&a!==r&&this.dispatchToElement(o,"mouseover",t)},mouseout:function(t){this.dispatchToElement(this._hovered,"mouseout",t);var e,n=t.toElement||t.relatedTarget;do n=n&&n.parentNode;while(n&&9!=n.nodeType&&!(e=n===this.painterRoot));!e&&this.trigger("globalout",{event:t})},resize:function(t){this._hovered={}},dispatch:function(t,e){var n=this[t];n&&n.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,n){t=t||{};var r=t.target;if(!r||!r.silent){for(var o="on"+e,a=i(e,t,n);r&&(r[o]&&(a.cancelBubble=r[o].call(r,a)),r.trigger(e,a),r=r.parent,!a.cancelBubble););a.cancelBubble||(this.trigger(e,a),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[o]&&t[o].call(t,a),t.trigger&&t.trigger(e,a)}))}},findHover:function(t,e,n){for(var i=this.storage.getDisplayList(),r={x:t,y:e},a=i.length-1;a>=0;a--){var s;if(i[a]!==n&&!i[a].ignore&&(s=o(i[a],t,e))&&(!r.topTarget&&(r.topTarget=i[a]),s!==u)){r.target=i[a];break}}return r}},a.each(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){c.prototype[t]=function(e){var n=this.findHover(e.zrX,e.zrY),i=n.target;if("mousedown"===t)this._downel=i,this._upel=i;else if("mosueup"===t)this._upel=i;else if("click"===t&&this._downel!==this._upel)return;this.dispatchToElement(n,t,e)}}),a.mixin(c,l),a.mixin(c,s),t.exports=c},function(t,e,n){function i(){return!1}function r(t,e,n,i){var r=document.createElement(e),o=n.getWidth(),a=n.getHeight(),s=r.style;return s.position="absolute",s.left=0,s.top=0,s.width=o+"px",s.height=a+"px",r.width=o*i,r.height=a*i,r.setAttribute("data-zr-dom-id",t),r}var o=n(1),a=n(35),s=n(75),l=n(74),u=function(t,e,n){var s;n=n||a.devicePixelRatio,"string"==typeof t?s=r(t,"canvas",e,n):o.isObject(t)&&(s=t,t=s.id),this.id=t,this.dom=s;var l=s.style;l&&(s.onselectstart=i,l["-webkit-user-select"]="none",l["user-select"]="none",l["-webkit-touch-callout"]="none",l["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",l.padding=0,l.margin=0,l["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=n};u.prototype={constructor:u,elCount:0,__dirty:!0,initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.__currentValues={},this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=r("back-"+this.id,"canvas",this.painter,t),this.ctxBack=this.domBack.getContext("2d"),this.ctxBack.__currentValues={},1!=t&&this.ctxBack.scale(t,t)},resize:function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r.width=t+"px",r.height=e+"px",i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!=n&&this.ctxBack.scale(n,n))},clear:function(t){var e=this.dom,n=this.ctx,i=e.width,r=e.height,o=this.clearColor,a=this.motionBlur&&!t,u=this.lastFrameAlpha,h=this.dpr;if(a&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(e,0,0,i/h,r/h)),n.clearRect(0,0,i,r),o){var c;o.colorStops?(c=o.__canvasGradient||s.getGradient(n,o,{x:0,y:0,width:i,height:r}),o.__canvasGradient=c):o.image&&(c=l.prototype.getCanvasPattern.call(o,n)),n.save(),n.fillStyle=c||o,n.fillRect(0,0,i,r),n.restore()}if(a){var d=this.domBack;n.save(),n.globalAlpha=u,n.drawImage(d,0,0,i,r),n.restore()}}},t.exports=u},function(t,e,n){"use strict";function i(t){return parseInt(t,10)}function r(t){return!!t&&(!!t.__builtin__||"function"==typeof t.resize&&"function"==typeof t.refresh)}function o(t){t.__unusedCount++}function a(t){1==t.__unusedCount&&t.clear()}function s(t,e,n){return y.copy(t.getBoundingRect()),t.transform&&y.applyTransform(t.transform),_.width=e,_.height=n,!y.intersect(_)}function l(t,e){if(t==e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0}function u(t,e){for(var n=0;n<t.length;n++){var i=t[n];i.setTransform(e),e.beginPath(),i.buildPath(e,i.shape),e.clip(),i.restoreTransform(e)}}function h(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","overflow:hidden","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}var c=n(35),d=n(1),f=n(54),p=n(12),g=n(52),m=n(159),v=n(70),x=5,y=new p(0,0,0,0),_=new p(0,0,0,0),b=function(t,e,n){var i=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=d.extend({},n||{}),this.dpr=n.devicePixelRatio||c.devicePixelRatio,this._singleCanvas=i,this.root=t;var r=t.style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var o=this._zlevelList=[],a=this._layers={};if(this._layerConfig={},i){null!=n.width&&(t.width=n.width),null!=n.height&&(t.height=n.height);var s=t.width,l=t.height;this._width=s,this._height=l;var u=new m(t,this,1);u.initContext(),a[0]=u,o.push(0),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var f=this._domRoot=h(this._width,this._height);t.appendChild(f)}this._progressiveLayers=[],this._hoverlayer,this._hoverElements=[]};b.prototype={constructor:b,isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(t){var e=this.storage.getDisplayList(!0),n=this._zlevelList;this._paintList(e,t);for(var i=0;i<n.length;i++){var r=n[i],o=this._layers[r];!o.__builtin__&&o.refresh&&o.refresh()}return this.refreshHover(),this._progressiveLayers.length&&this._startProgessive(),this},addHover:function(t,e){if(!t.__hoverMir){var n=new t.constructor({style:t.style,shape:t.shape});n.__from=t,t.__hoverMir=n,n.setStyle(e),this._hoverElements.push(n)}},removeHover:function(t){var e=t.__hoverMir,n=this._hoverElements,i=d.indexOf(n,e);i>=0&&n.splice(i,1),t.__hoverMir=null},clearHover:function(t){for(var e=this._hoverElements,n=0;n<e.length;n++){var i=e[n].__from;i&&(i.__hoverMir=null)}e.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){g(t,this.storage.displayableSortFunc),n||(n=this._hoverlayer=this.getLayer(1e5));var i={};n.ctx.save();for(var r=0;r<e;){var o=t[r],a=o.__from;a&&a.__zr?(r++,a.invisible||(o.transform=a.transform,o.invTransform=a.invTransform,o.__clipPaths=a.__clipPaths,this._doPaintEl(o,n,!0,i))):(t.splice(r,1),a.__hoverMir=null,e--)}n.ctx.restore()}},_startProgessive:function(){function t(){n===e._progressiveToken&&e.storage&&(e._doPaintList(e.storage.getDisplayList()),e._furtherProgressive?(e._progress++,v(t)):e._progressiveToken=-1)}var e=this;if(e._furtherProgressive){var n=e._progressiveToken=+new Date;e._progress++,v(t)}},_clearProgressive:function(){this._progressiveToken=-1,this._progress=0,d.each(this._progressiveLayers,function(t){t.__dirty&&t.clear()})},_paintList:function(t,e){null==e&&(e=!1),this._updateLayerStatus(t),this._clearProgressive(),this.eachBuiltinLayer(o),this._doPaintList(t,e),this.eachBuiltinLayer(a)},_doPaintList:function(t,e){function n(t){var e=o.dpr||1;o.save(),o.globalAlpha=1,o.shadowBlur=0,i.__dirty=!0,o.setTransform(1,0,0,1,0,0),o.drawImage(t.dom,0,0,h*e,c*e),o.restore()}for(var i,r,o,a,s,l,u=0,h=this._width,c=this._height,p=this._progress,g=0,m=t.length;g<m;g++){var v=t[g],y=this._singleCanvas?0:v.zlevel,_=v.__frame;if(_<0&&s&&(n(s),s=null),r!==y&&(o&&o.restore(),a={},r=y,i=this.getLayer(r),i.__builtin__||f("ZLevel "+r+" has been used by unkown layer "+i.id),o=i.ctx,o.save(),i.__unusedCount=0,(i.__dirty||e)&&i.clear()),i.__dirty||e){if(_>=0){if(!s){if(s=this._progressiveLayers[Math.min(u++,x-1)],s.ctx.save(),s.renderScope={},s&&s.__progress>s.__maxProgress){g=s.__nextIdxNotProg-1;continue}l=s.__progress,s.__dirty||(p=l),s.__progress=p+1}_===p&&this._doPaintEl(v,s,!0,s.renderScope)}else this._doPaintEl(v,i,e,a);v.__dirty=!1}}s&&n(s),o&&o.restore(),this._furtherProgressive=!1,d.each(this._progressiveLayers,function(t){t.__maxProgress>=t.__progress&&(this._furtherProgressive=!0);
},this)},_doPaintEl:function(t,e,n,i){var r=e.ctx,o=t.transform;if((e.__dirty||n)&&!t.invisible&&0!==t.style.opacity&&(!o||o[0]||o[3])&&(!t.culling||!s(t,this._width,this._height))){var a=t.__clipPaths;(i.prevClipLayer!==e||l(a,i.prevElClipPaths))&&(i.prevElClipPaths&&(i.prevClipLayer.ctx.restore(),i.prevClipLayer=i.prevElClipPaths=null,i.prevEl=null),a&&(r.save(),u(a,r),i.prevClipLayer=e,i.prevElClipPaths=a)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,i.prevEl||null),i.prevEl=t,t.afterBrush&&t.afterBrush(r)}},getLayer:function(t){if(this._singleCanvas)return this._layers[0];var e=this._layers[t];return e||(e=new m("zr_"+t,this,this.dpr),e.__builtin__=!0,this._layerConfig[t]&&d.merge(e,this._layerConfig[t],!0),this.insertLayer(t,e),e.initContext()),e},insertLayer:function(t,e){var n=this._layers,i=this._zlevelList,o=i.length,a=null,s=-1,l=this._domRoot;if(n[t])return void f("ZLevel "+t+" has been used already");if(!r(e))return void f("Layer of zlevel "+t+" is not valid");if(o>0&&t>i[0]){for(s=0;s<o-1&&!(i[s]<t&&i[s+1]>t);s++);a=n[i[s]]}if(i.splice(s+1,0,t),n[t]=e,!e.virtual)if(a){var u=a.dom;u.nextSibling?l.insertBefore(e.dom,u.nextSibling):l.appendChild(e.dom)}else l.firstChild?l.insertBefore(e.dom,l.firstChild):l.appendChild(e.dom)},eachLayer:function(t,e){var n,i,r=this._zlevelList;for(i=0;i<r.length;i++)n=r[i],t.call(e,this._layers[n],n)},eachBuiltinLayer:function(t,e){var n,i,r,o=this._zlevelList;for(r=0;r<o.length;r++)i=o[r],n=this._layers[i],n.__builtin__&&t.call(e,n,i)},eachOtherLayer:function(t,e){var n,i,r,o=this._zlevelList;for(r=0;r<o.length;r++)i=o[r],n=this._layers[i],n.__builtin__||t.call(e,n,i)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){var e=this._layers,n=this._progressiveLayers,i={},r={};this.eachBuiltinLayer(function(t,e){i[e]=t.elCount,t.elCount=0,t.__dirty=!1}),d.each(n,function(t,e){r[e]=t.elCount,t.elCount=0,t.__dirty=!1});for(var o,a,s=0,l=0,u=0,h=t.length;u<h;u++){var c=t[u],f=this._singleCanvas?0:c.zlevel,p=e[f],g=c.progressive;if(p&&(p.elCount++,p.__dirty=p.__dirty||c.__dirty),g>=0){a!==g&&(a=g,l++);var v=c.__frame=l-1;if(!o){var y=Math.min(s,x-1);o=n[y],o||(o=n[y]=new m("progressive",this,this.dpr),o.initContext()),o.__maxProgress=0}o.__dirty=o.__dirty||c.__dirty,o.elCount++,o.__maxProgress=Math.max(o.__maxProgress,v),o.__maxProgress>=o.__progress&&(p.__dirty=!0)}else c.__frame=-1,o&&(o.__nextIdxNotProg=u,s++,o=null)}o&&(s++,o.__nextIdxNotProg=u),this.eachBuiltinLayer(function(t,e){i[e]!==t.elCount&&(t.__dirty=!0)}),n.length=Math.min(s,x),d.each(n,function(t,e){r[e]!==t.elCount&&(c.__dirty=!0),t.__dirty&&(t.__progress=0)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},configLayer:function(t,e){if(e){var n=this._layerConfig;n[t]?d.merge(n[t],e,!0):n[t]=e;var i=this._layers[t];i&&d.merge(i,n[t],!0)}},delLayer:function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(d.indexOf(n,t),1))},resize:function(t,e){var n=this._domRoot;n.style.display="none";var i=this._opts;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=this._getSize(0),e=this._getSize(1),n.style.display="",this._width!=t||e!=this._height){n.style.width=t+"px",n.style.height=e+"px";for(var r in this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);d.each(this._progressiveLayers,function(n){n.resize(t,e)}),this.refresh(!0)}return this._width=t,this._height=e,this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){function e(t,e){var i=a._zlevelList;null==t&&(t=-(1/0));for(var r,o=0;o<i.length;o++){var s=i[o],l=a._layers[s];if(!l.__builtin__&&s>t&&s<e){r=l;break}}r&&r.renderToCanvas&&(n.ctx.save(),r.renderToCanvas(n.ctx),n.ctx.restore())}if(t=t||{},this._singleCanvas)return this._layers[0].dom;var n=new m("image",this,t.pixelRatio||this.dpr);n.initContext(),n.clearColor=t.backgroundColor,n.clear();for(var i,r=this.storage.getDisplayList(!0),o={},a=this,s=0;s<r.length;s++){var l=r[s];l.zlevel!==i&&(e(i,l.zlevel),i=l.zlevel),this._doPaintEl(l,n,!0,o)}return e(i,1/0),n.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,n=["width","height"][t],r=["clientWidth","clientHeight"][t],o=["paddingLeft","paddingTop"][t],a=["paddingRight","paddingBottom"][t];if(null!=e[n]&&"auto"!==e[n])return parseFloat(e[n]);var s=this.root,l=document.defaultView.getComputedStyle(s);return(s[r]||i(l[n])||i(s.style[n]))-(i(l[o])||0)-(i(l[a])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var i=document.createElement("canvas"),r=i.getContext("2d"),o=t.getBoundingRect(),a=t.style,s=a.shadowBlur,l=a.shadowOffsetX,u=a.shadowOffsetY,h=a.hasStroke()?a.lineWidth:0,c=Math.max(h/2,-l+s),d=Math.max(h/2,l+s),f=Math.max(h/2,-u+s),p=Math.max(h/2,u+s),g=o.width+c+d,m=o.height+f+p;i.width=g*e,i.height=m*e,r.scale(e,e),r.clearRect(0,0,g,m),r.dpr=e;var v={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[c-o.x,f-o.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(r);var x=n(55),y=new x({style:{x:0,y:0,image:i}});return null!=v.position&&(y.position=t.position=v.position),null!=v.rotation&&(y.rotation=t.rotation=v.rotation),null!=v.scale&&(y.scale=t.scale=v.scale),y}},t.exports=b},function(t,e,n){"use strict";function i(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var r=n(1),o=n(10),a=n(36),s=n(52),l=function(){this._roots=[],this._displayList=[],this._displayListLen=0};l.prototype={constructor:l,traverse:function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,r=0,a=e.length;r<a;r++)this._updateAndAddDisplayable(e[r],null,t);n.length=this._displayListLen,o.canvasSupported&&s(n,i)},_updateAndAddDisplayable:function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var i=t.clipPath;if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),o=r,r=r.clipPath}if(t.isGroup){for(var a=t._children,s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,n)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof a&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var n=this._roots[e];n instanceof a&&n.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array)for(var e=0,i=t.length;e<i;e++)this.delRoot(t[e]);else{var o=r.indexOf(this._roots,t);o>=0&&(this.delFromStorage(t),this._roots.splice(o,1),t instanceof a&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t.__storage=this,t.dirty(!1),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:i},t.exports=l},function(t,e,n){"use strict";var i=n(1),r=n(21).Dispatcher,o=n(70),a=n(69),s=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,r.call(this)};s.prototype={constructor:s,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),n=0;n<e.length;n++)this.addClip(e[n])},removeClip:function(t){var e=i.indexOf(this._clips,t);e>=0&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),n=0;n<e.length;n++)this.removeClip(e[n]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,n=this._clips,i=n.length,r=[],o=[],a=0;a<i;a++){var s=n[a],l=s.step(t,e);l&&(r.push(l),o.push(s))}for(var a=0;a<i;)n[a]._needsRemove?(n[a]=n[i-1],n.pop(),i--):a++;i=r.length;for(var a=0;a<i;a++)o[a].fire(r[a]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){function t(){e._running&&(o(t),!e._paused&&e._update())}var e=this;this._running=!0,o(t)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},animate:function(t,e){e=e||{};var n=new a(t,e.loop,e.getter,e.setter);return this.addAnimator(n),n}},i.mixin(s,r),t.exports=s},function(t,e,n){function i(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null!=t.loop&&t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}var r=n(164);i.prototype={constructor:i,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)return void(this._pausedTime+=e);var n=(t-this._startTime-this._pausedTime)/this._life;if(!(n<0)){n=Math.min(n,1);var i=this.easing,o="string"==typeof i?r[i]:i,a="function"==typeof o?o(n):n;return this.fire("frame",a),1==n?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}},t.exports=i},function(t,e){var n={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)))},elasticOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/i)+1)},elasticInOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?-.5*(n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)):n*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?.5*(t*t*((e+1)*t-e)):.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-n.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*n.bounceIn(2*t):.5*n.bounceOut(2*t-1)+.5}};t.exports=n},function(t,e,n){var i=n(71).normalizeRadian,r=2*Math.PI;t.exports={containStroke:function(t,e,n,o,a,s,l,u,h){if(0===l)return!1;var c=l;u-=t,h-=e;var d=Math.sqrt(u*u+h*h);if(d-c>n||d+c<n)return!1;if(Math.abs(o-a)%r<1e-4)return!0;if(s){var f=o;o=i(a),a=i(f)}else o=i(o),a=i(a);o>a&&(a+=r);var p=Math.atan2(h,u);return p<0&&(p+=r),p>=o&&p<=a||p+r>=o&&p+r<=a}}},function(t,e,n){var i=n(20);t.exports={containStroke:function(t,e,n,r,o,a,s,l,u,h,c){if(0===u)return!1;var d=u;if(c>e+d&&c>r+d&&c>a+d&&c>l+d||c<e-d&&c<r-d&&c<a-d&&c<l-d||h>t+d&&h>n+d&&h>o+d&&h>s+d||h<t-d&&h<n-d&&h<o-d&&h<s-d)return!1;var f=i.cubicProjectPoint(t,e,n,r,o,a,s,l,h,c,null);return f<=d/2}}},function(t,e,n){"use strict";function i(t,e){return Math.abs(t-e)<y}function r(){var t=b[0];b[0]=b[1],b[1]=t}function o(t,e,n,i,o,a,s,l,u,h){if(h>e&&h>i&&h>a&&h>l||h<e&&h<i&&h<a&&h<l)return 0;var c=g.cubicRootAt(e,i,a,l,h,_);if(0===c)return 0;for(var d,f,p=0,m=-1,v=0;v<c;v++){var x=_[v],y=0===x||1===x?.5:1,w=g.cubicAt(t,n,o,s,x);w<u||(m<0&&(m=g.cubicExtrema(e,i,a,l,b),b[1]<b[0]&&m>1&&r(),d=g.cubicAt(e,i,a,l,b[0]),m>1&&(f=g.cubicAt(e,i,a,l,b[1]))),p+=2==m?x<b[0]?d<e?y:-y:x<b[1]?f<d?y:-y:l<f?y:-y:x<b[0]?d<e?y:-y:l<d?y:-y)}return p}function a(t,e,n,i,r,o,a,s){if(s>e&&s>i&&s>o||s<e&&s<i&&s<o)return 0;var l=g.quadraticRootAt(e,i,o,s,_);if(0===l)return 0;var u=g.quadraticExtremum(e,i,o);if(u>=0&&u<=1){for(var h=0,c=g.quadraticAt(e,i,o,u),d=0;d<l;d++){var f=0===_[d]||1===_[d]?.5:1,p=g.quadraticAt(t,n,r,_[d]);p<a||(h+=_[d]<u?c<e?f:-f:o<c?f:-f)}return h}var f=0===_[0]||1===_[0]?.5:1,p=g.quadraticAt(t,n,r,_[0]);return p<a?0:o<e?f:-f}function s(t,e,n,i,r,o,a,s){if(s-=e,s>n||s<-n)return 0;var l=Math.sqrt(n*n-s*s);_[0]=-l,_[1]=l;var u=Math.abs(i-r);if(u<1e-4)return 0;if(u%x<1e-4){i=0,r=x;var h=o?1:-1;return a>=_[0]+t&&a<=_[1]+t?h:0}if(o){var l=i;i=p(r),r=p(l)}else i=p(i),r=p(r);i>r&&(r+=x);for(var c=0,d=0;d<2;d++){var f=_[d];if(f+t>a){var g=Math.atan2(s,f),h=o?1:-1;g<0&&(g=x+g),(g>=i&&g<=r||g+x>=i&&g+x<=r)&&(g>Math.PI/2&&g<1.5*Math.PI&&(h=-h),c+=h)}}return c}function l(t,e,n,r,l){for(var h=0,p=0,g=0,x=0,y=0,_=0;_<t.length;){var b=t[_++];switch(b===u.M&&_>1&&(n||(h+=m(p,g,x,y,r,l))),1==_&&(p=t[_],g=t[_+1],x=p,y=g),b){case u.M:x=t[_++],y=t[_++],p=x,g=y;break;case u.L:if(n){if(v(p,g,t[_],t[_+1],e,r,l))return!0}else h+=m(p,g,t[_],t[_+1],r,l)||0;p=t[_++],g=t[_++];break;case u.C:if(n){if(c.containStroke(p,g,t[_++],t[_++],t[_++],t[_++],t[_],t[_+1],e,r,l))return!0}else h+=o(p,g,t[_++],t[_++],t[_++],t[_++],t[_],t[_+1],r,l)||0;p=t[_++],g=t[_++];break;case u.Q:if(n){if(d.containStroke(p,g,t[_++],t[_++],t[_],t[_+1],e,r,l))return!0}else h+=a(p,g,t[_++],t[_++],t[_],t[_+1],r,l)||0;p=t[_++],g=t[_++];break;case u.A:var w=t[_++],S=t[_++],M=t[_++],T=t[_++],I=t[_++],A=t[_++],C=(t[_++],1-t[_++]),P=Math.cos(I)*M+w,D=Math.sin(I)*T+S;_>1?h+=m(p,g,P,D,r,l):(x=P,y=D);var k=(r-w)*T/M+w;if(n){if(f.containStroke(w,S,T,I,I+A,C,e,k,l))return!0}else h+=s(w,S,T,I,I+A,C,k,l);p=Math.cos(I+A)*M+w,g=Math.sin(I+A)*T+S;break;case u.R:x=p=t[_++],y=g=t[_++];var L=t[_++],O=t[_++],P=x+L,D=y+O;if(n){if(v(x,y,P,y,e,r,l)||v(P,y,P,D,e,r,l)||v(P,D,x,D,e,r,l)||v(x,D,x,y,e,r,l))return!0}else h+=m(P,y,P,D,r,l),h+=m(x,D,x,y,r,l);break;case u.Z:if(n){if(v(p,g,x,y,e,r,l))return!0}else h+=m(p,g,x,y,r,l);p=x,g=y}}return n||i(g,y)||(h+=m(p,g,x,y,r,l)||0),0!==h}var u=n(27).CMD,h=n(101),c=n(166),d=n(102),f=n(165),p=n(71).normalizeRadian,g=n(20),m=n(103),v=h.containStroke,x=2*Math.PI,y=1e-4,_=[-1,-1,-1],b=[-1,-1];t.exports={contain:function(t,e,n){return l(t,0,!1,e,n)},containStroke:function(t,e,n,i){return l(t,e,!0,n,i)}}},function(t,e,n){"use strict";function i(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}function r(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}var o=n(21),a=function(){this._track=[]};a.prototype={constructor:a,recognize:function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},a=0,s=i.length;a<s;a++){var l=i[a],u=o.clientToLocal(n,l,{});r.points.push([u.zrX,u.zrY]),r.touches.push(l)}this._track.push(r)}},_recognize:function(t){for(var e in s)if(s.hasOwnProperty(e)){var n=s[e](this._track,t);if(n)return n}}};var s={pinch:function(t,e){var n=t.length;if(n){var o=(t[n-1]||{}).points,a=(t[n-2]||{}).points||o;if(a&&a.length>1&&o&&o.length>1){var s=i(o)/i(a);!isFinite(s)&&(s=1),e.pinchScale=s;var l=r(o);return e.pinchX=l[0],e.pinchY=l[1],{type:"pinch",target:t[0].target,event:e}}}}};t.exports=a},function(t,e,n){function i(t){return"mousewheel"===t&&d.browser.firefox?"DOMMouseScroll":t}function r(t,e,n){var i=t._gestureMgr;"start"===n&&i.clear();var r=i.recognize(e,t.handler.findHover(e.zrX,e.zrY,null).target,t.dom);if("end"===n&&i.clear(),r){var o=r.type;e.gestureEvent=o,t.handler.dispatchToElement({target:r.target},o,r.event)}}function o(t){t._touching=!0,clearTimeout(t._touchTimer),t._touchTimer=setTimeout(function(){t._touching=!1},700)}function a(t){var e=t.pointerType;return"pen"===e||"touch"===e}function s(t){function e(t,e){return function(){if(!e._touching)return t.apply(e,arguments)}}h.each(y,function(e){t._handlers[e]=h.bind(w[e],t)}),h.each(b,function(e){t._handlers[e]=h.bind(w[e],t)}),h.each(x,function(n){t._handlers[n]=e(w[n],t)})}function l(t){function e(e,n){h.each(e,function(e){p(t,i(e),n._handlers[e])},n)}c.call(this),this.dom=t,this._touching=!1,this._touchTimer,this._gestureMgr=new f,this._handlers={},s(this),d.pointerEventsSupported?e(b,this):(d.touchEventsSupported&&e(y,this),e(x,this))}var u=n(21),h=n(1),c=n(23),d=n(10),f=n(168),p=u.addEventListener,g=u.removeEventListener,m=u.normalizeEvent,v=300,x=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],y=["touchstart","touchend","touchmove"],_={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},b=h.map(x,function(t){var e=t.replace("mouse","pointer");return _[e]?e:t}),w={mousemove:function(t){t=m(this.dom,t),this.trigger("mousemove",t)},mouseout:function(t){t=m(this.dom,t);var e=t.toElement||t.relatedTarget;if(e!=this.dom)for(;e&&9!=e.nodeType;){if(e===this.dom)return;e=e.parentNode}this.trigger("mouseout",t)},touchstart:function(t){t=m(this.dom,t),t.zrByTouch=!0,this._lastTouchMoment=new Date,r(this,t,"start"),w.mousemove.call(this,t),w.mousedown.call(this,t),o(this)},touchmove:function(t){t=m(this.dom,t),t.zrByTouch=!0,r(this,t,"change"),w.mousemove.call(this,t),o(this)},touchend:function(t){t=m(this.dom,t),t.zrByTouch=!0,r(this,t,"end"),w.mouseup.call(this,t),+new Date-this._lastTouchMoment<v&&w.click.call(this,t),o(this)},pointerdown:function(t){w.mousedown.call(this,t)},pointermove:function(t){a(t)||w.mousemove.call(this,t)},pointerup:function(t){w.mouseup.call(this,t)},pointerout:function(t){a(t)||w.mouseout.call(this,t)}};h.each(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){w[t]=function(e){e=m(this.dom,e),this.trigger(t,e)}});var S=l.prototype;S.dispose=function(){for(var t=x.concat(y),e=0;e<t.length;e++){var n=t[e];g(this.dom,i(n),this._handlers[n])}},S.setCursor=function(t){this.dom.style.cursor=t||"default"},h.mixin(l,c),t.exports=l},function(t,e,n){var i=n(8);t.exports=i.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,n=0;n<e.length;n++)t=t||e[n].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1])},buildPath:function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},afterBrush:function(){for(var t=this.shape.paths,e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),i.prototype.getBoundingRect.call(this)}})},function(t,e,n){"use strict";var i=n(1),r=n(39),o=function(t,e,n,i,o){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==n?.5:n,this.type="radial",this.global=o||!1,r.call(this,i)};o.prototype={constructor:o},i.inherits(o,r),t.exports=o},function(t,e,n){var i=n(6),r=i.min,o=i.max,a=i.scale,s=i.distance,l=i.add;t.exports=function(t,e,n,u){var h,c,d,f,p=[],g=[],m=[],v=[];if(u){d=[1/0,1/0],f=[-(1/0),-(1/0)];for(var x=0,y=t.length;x<y;x++)r(d,d,t[x]),o(f,f,t[x]);r(d,d,u[0]),o(f,f,u[1])}for(var x=0,y=t.length;x<y;x++){var _=t[x];if(n)h=t[x?x-1:y-1],c=t[(x+1)%y];else{if(0===x||x===y-1){p.push(i.clone(t[x]));continue}h=t[x-1],c=t[x+1]}i.sub(g,c,h),a(g,g,e);var b=s(_,h),w=s(_,c),S=b+w;0!==S&&(b/=S,w/=S),a(m,g,-b),a(v,g,w);var M=l([],_,m),T=l([],_,v);u&&(o(M,M,d),r(M,M,f),o(T,T,d),r(T,T,f)),p.push(M),p.push(T)}return n&&p.push(p.shift()),p}},function(t,e,n){function i(t,e,n,i,r,o,a){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*a+(-3*(e-n)-2*s-l)*o+s*r+e}var r=n(6);t.exports=function(t,e){for(var n=t.length,o=[],a=0,s=1;s<n;s++)a+=r.distance(t[s-1],t[s]);var l=a/2;l=l<n?n:l;for(var s=0;s<l;s++){var u,h,c,d=s/(l-1)*(e?n:n-1),f=Math.floor(d),p=d-f,g=t[f%n];e?(u=t[(f-1+n)%n],h=t[(f+1)%n],c=t[(f+2)%n]):(u=t[0===f?f:f-1],h=t[f>n-2?n-1:f+1],c=t[f>n-3?n-1:f+2]);var m=p*p,v=p*m;o.push([i(u[0],g[0],h[0],c[0],p,m,v),i(u[1],g[1],h[1],c[1],p,m,v)])}return o}},function(t,e,n){t.exports=n(8).extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,l=Math.cos(o),u=Math.sin(o);t.moveTo(l*r+n,u*r+i),t.arc(n,i,r,o,a,!s)}})},function(t,e,n){"use strict";function i(t,e,n){var i=t.cpx2,r=t.cpy2;return null===i||null===r?[(n?c:u)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?c:u)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?h:l)(t.x1,t.cpx1,t.x2,e),(n?h:l)(t.y1,t.cpy1,t.y2,e)]}var r=n(20),o=n(6),a=r.quadraticSubdivide,s=r.cubicSubdivide,l=r.quadraticAt,u=r.cubicAt,h=r.quadraticDerivativeAt,c=r.cubicDerivativeAt,d=[];t.exports=n(8).extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,l=e.cpx1,u=e.cpy1,h=e.cpx2,c=e.cpy2,f=e.percent;0!==f&&(t.moveTo(n,i),null==h||null==c?(f<1&&(a(n,l,r,f,d),l=d[1],r=d[2],a(i,u,o,f,d),u=d[1],o=d[2]),t.quadraticCurveTo(l,u,r,o)):(f<1&&(s(n,l,h,r,f,d),l=d[1],h=d[2],r=d[3],s(i,u,c,o,f,d),u=d[1],c=d[2],o=d[3]),t.bezierCurveTo(l,u,h,c,r,o)))},pointAt:function(t){return i(this.shape,t,!1)},tangentAt:function(t){var e=i(this.shape,t,!0);return o.normalize(e,e)}})},function(t,e,n){"use strict";t.exports=n(8).extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,n){n&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}})},function(t,e,n){t.exports=n(8).extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.percent;0!==a&&(t.moveTo(n,i),a<1&&(r=n*(1-a)+r*a,o=i*(1-a)+o*a),t.lineTo(r,o))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}})},function(t,e,n){var i=n(77);t.exports=n(8).extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){i.buildPath(t,e,!0)}})},function(t,e,n){var i=n(77);t.exports=n(8).extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){i.buildPath(t,e,!1)}})},function(t,e,n){var i=n(78);t.exports=n(8).extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,r=e.y,o=e.width,a=e.height;e.r?i.buildPath(t,e):t.rect(n,r,o,a),t.closePath()}})},function(t,e,n){t.exports=n(8).extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)}})},function(t,e,n){var i=n(8),r=n(76);t.exports=i.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:r(i.prototype.brush),buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=e.startAngle,s=e.endAngle,l=e.clockwise,u=Math.cos(a),h=Math.sin(a);t.moveTo(u*r+n,h*r+i),t.lineTo(u*o+n,h*o+i),t.arc(n,i,o,a,s,!l),t.lineTo(Math.cos(s)*r+n,Math.sin(s)*r+i),0!==r&&t.arc(n,i,r,s,a,l),t.closePath()}})},function(t,e,n){"use strict";var i=n(69),r=n(1),o=r.isString,a=r.isFunction,s=r.isObject,l=n(54),u=function(){this.animators=[]};u.prototype={constructor:u,animate:function(t,e){var n,o=!1,a=this,s=this.__zr;if(t){var u=t.split("."),h=a;o="shape"===u[0];for(var c=0,d=u.length;c<d;c++)h&&(h=h[u[c]]);h&&(n=h)}else n=a;if(!n)return void l('Property "'+t+'" is not existed in element '+a.id);var f=a.animators,p=new i(n,e);return p.during(function(t){a.dirty(o)}).done(function(){f.splice(r.indexOf(f,p),1)}),f.push(p),s&&s.animation.addAnimator(p),p},stopAnimation:function(t){for(var e=this.animators,n=e.length,i=0;i<n;i++)e[i].stop(t);return e.length=0,this},animateTo:function(t,e,n,i,r,s){function l(){h--,h||r&&r()}o(n)?(r=i,i=n,n=0):a(i)?(r=i,i="linear",n=0):a(n)?(r=n,n=0):a(e)?(r=e,e=500):e||(e=500),this.stopAnimation(),this._animateToShallow("",this,t,e,n,i,r);var u=this.animators.slice(),h=u.length;h||r&&r();for(var c=0;c<u.length;c++)u[c].done(l).start(i,s)},_animateToShallow:function(t,e,n,i,o){var a={},l=0;for(var u in n)if(n.hasOwnProperty(u))if(null!=e[u])s(n[u])&&!r.isArrayLike(n[u])?this._animateToShallow(t?t+"."+u:u,e[u],n[u],i,o):(a[u]=n[u],l++);else if(null!=n[u])if(t){var h={};h[t]={},h[t][u]=n[u],this.attr(h)}else this.attr(u,n[u]);return l>0&&this.animate(t,!1).when(null==i?500:i,a).delay(o||0),this}},t.exports=u},function(t,e){function n(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this),this.on("globalout",this._dragEnd,this)}function i(t,e){return{target:t,topTarget:e&&e.topTarget}}n.prototype={constructor:n,_dragStart:function(t){var e=t.target;e&&e.draggable&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(i(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,r=t.offsetY,o=n-this._x,a=r-this._y;this._x=n,this._y=r,e.drift(o,a,t),this.dispatchToElement(i(e,t),"drag",t.event);var s=this.findHover(n,r,e).target,l=this._dropTarget;this._dropTarget=s,e!==s&&(l&&s!==l&&this.dispatchToElement(i(l,t),"dragleave",t.event),s&&s!==l&&this.dispatchToElement(i(s,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(i(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(i(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}},t.exports=n},function(t,e,n){function i(t,e,n,i,r,o,a,s,l,u,p){var v=l*(f/180),x=d(v)*(t-n)/2+c(v)*(e-i)/2,y=-1*c(v)*(t-n)/2+d(v)*(e-i)/2,_=x*x/(a*a)+y*y/(s*s);_>1&&(a*=h(_),s*=h(_));var b=(r===o?-1:1)*h((a*a*(s*s)-a*a*(y*y)-s*s*(x*x))/(a*a*(y*y)+s*s*(x*x)))||0,w=b*a*y/s,S=b*-s*x/a,M=(t+n)/2+d(v)*w-c(v)*S,T=(e+i)/2+c(v)*w+d(v)*S,I=m([1,0],[(x-w)/a,(y-S)/s]),A=[(x-w)/a,(y-S)/s],C=[(-1*x-w)/a,(-1*y-S)/s],P=m(A,C);g(A,C)<=-1&&(P=f),g(A,C)>=1&&(P=0),0===o&&P>0&&(P-=2*f),1===o&&P<0&&(P+=2*f),p.addData(u,M,T,a,s,I,P,v,o)}function r(t){if(!t)return[];var e,n=t.replace(/-/g," -").replace(/  /g," ").replace(/ /g,",").replace(/,,/g,",");for(e=0;e<u.length;e++)n=n.replace(new RegExp(u[e],"g"),"|"+u[e]);var r,o=n.split("|"),a=0,l=0,h=new s,c=s.CMD;for(e=1;e<o.length;e++){var d,f=o[e],p=f.charAt(0),g=0,m=f.slice(1).replace(/e,-/g,"e-").split(",");m.length>0&&""===m[0]&&m.shift();for(var v=0;v<m.length;v++)m[v]=parseFloat(m[v]);for(;g<m.length&&!isNaN(m[g])&&!isNaN(m[0]);){var x,y,_,b,w,S,M,T=a,I=l;switch(p){case"l":a+=m[g++],l+=m[g++],d=c.L,h.addData(d,a,l);break;case"L":a=m[g++],l=m[g++],d=c.L,h.addData(d,a,l);break;case"m":a+=m[g++],l+=m[g++],d=c.M,h.addData(d,a,l),p="l";break;case"M":a=m[g++],l=m[g++],d=c.M,h.addData(d,a,l),p="L";break;case"h":a+=m[g++],d=c.L,h.addData(d,a,l);break;case"H":a=m[g++],d=c.L,h.addData(d,a,l);break;case"v":l+=m[g++],d=c.L,h.addData(d,a,l);break;case"V":l=m[g++],d=c.L,h.addData(d,a,l);break;case"C":d=c.C,h.addData(d,m[g++],m[g++],m[g++],m[g++],m[g++],m[g++]),a=m[g-2],l=m[g-1];break;case"c":d=c.C,h.addData(d,m[g++]+a,m[g++]+l,m[g++]+a,m[g++]+l,m[g++]+a,m[g++]+l),a+=m[g-2],l+=m[g-1];break;case"S":x=a,y=l;var A=h.len(),C=h.data;r===c.C&&(x+=a-C[A-4],y+=l-C[A-3]),d=c.C,T=m[g++],I=m[g++],a=m[g++],l=m[g++],h.addData(d,x,y,T,I,a,l);break;case"s":x=a,y=l;var A=h.len(),C=h.data;r===c.C&&(x+=a-C[A-4],y+=l-C[A-3]),d=c.C,T=a+m[g++],I=l+m[g++],a+=m[g++],l+=m[g++],h.addData(d,x,y,T,I,a,l);break;case"Q":T=m[g++],I=m[g++],a=m[g++],l=m[g++],d=c.Q,h.addData(d,T,I,a,l);break;case"q":T=m[g++]+a,I=m[g++]+l,a+=m[g++],l+=m[g++],d=c.Q,h.addData(d,T,I,a,l);break;case"T":x=a,y=l;var A=h.len(),C=h.data;r===c.Q&&(x+=a-C[A-4],y+=l-C[A-3]),a=m[g++],l=m[g++],d=c.Q,h.addData(d,x,y,a,l);break;case"t":x=a,y=l;var A=h.len(),C=h.data;r===c.Q&&(x+=a-C[A-4],y+=l-C[A-3]),a+=m[g++],l+=m[g++],d=c.Q,h.addData(d,x,y,a,l);break;case"A":_=m[g++],b=m[g++],w=m[g++],S=m[g++],M=m[g++],T=a,I=l,a=m[g++],l=m[g++],d=c.A,i(T,I,a,l,S,M,_,b,w,d,h);break;case"a":_=m[g++],b=m[g++],w=m[g++],S=m[g++],M=m[g++],T=a,I=l,a+=m[g++],l+=m[g++],d=c.A,i(T,I,a,l,S,M,_,b,w,d,h)}}"z"!==p&&"Z"!==p||(d=c.Z,h.addData(d)),r=d}return h.toStatic(),h}function o(t,e){var n=r(t);return e=e||{},e.buildPath=function(t){if(t.setData){t.setData(n.data);var e=t.getContext();e&&t.rebuildPath(e)}else{var e=t;n.rebuildPath(e)}},e.applyTransform=function(t){l(n,t),this.dirty(!0)},e}var a=n(8),s=n(27),l=n(186),u=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"],h=Math.sqrt,c=Math.sin,d=Math.cos,f=Math.PI,p=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},g=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(p(t)*p(e))},m=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(g(t,e))};t.exports={createFromString:function(t,e){return new a(o(t,e))},extendFromString:function(t,e){return a.extend(o(t,e))},mergePath:function(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];o.path||o.createPathProxy(),o.__dirtyPath&&o.buildPath(o.path,o.shape,!0),n.push(o.path)}var s=new a(e);return s.createPathProxy(),s.buildPath=function(t){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e)},s}}},function(t,e,n){function i(t,e){var n,i,o,h,c,d,f=t.data,p=r.M,g=r.C,m=r.L,v=r.R,x=r.A,y=r.Q;for(o=0,h=0;o<f.length;){switch(n=f[o++],h=o,i=0,n){case p:i=1;break;case m:i=1;break;case g:i=3;break;case y:i=2;break;case x:var _=e[4],b=e[5],w=l(e[0]*e[0]+e[1]*e[1]),S=l(e[2]*e[2]+e[3]*e[3]),M=u(-e[1]/S,e[0]/w);f[o]*=w,f[o++]+=_,f[o]*=S,f[o++]+=b,f[o++]*=w,f[o++]*=S,f[o++]+=M,f[o++]+=M,o+=2,h=o;break;case v:d[0]=f[o++],d[1]=f[o++],a(d,d,e),f[h++]=d[0],f[h++]=d[1],d[0]+=f[o++],d[1]+=f[o++],a(d,d,e),f[h++]=d[0],f[h++]=d[1]}for(c=0;c<i;c++){var d=s[c];d[0]=f[o++],d[1]=f[o++],a(d,d,e),f[h++]=d[0],f[h++]=d[1]}}}var r=n(27).CMD,o=n(6),a=o.applyTransform,s=[[],[],[]],l=Math.sqrt,u=Math.atan2;t.exports=i},function(t,e,n){if(!n(10).canvasSupported){var i,r="urn:schemas-microsoft-com:vml",o=window,a=o.document,s=!1;try{!a.namespaces.zrvml&&a.namespaces.add("zrvml",r),i=function(t){return a.createElement("<zrvml:"+t+' class="zrvml">')}}catch(t){i=function(t){return a.createElement("<"+t+' xmlns="'+r+'" class="zrvml">')}}var l=function(){if(!s){s=!0;var t=a.styleSheets;t.length<31?a.createStyleSheet().addRule(".zrvml","behavior:url(#default#VML)"):t[0].addRule(".zrvml","behavior:url(#default#VML)")}};t.exports={doc:a,initVML:l,createNode:i}}},,function(t,e,n){function i(t,e,n){var i=this._targetInfoList=[],r={},a=o(e,t);
p(_,function(t,e){(!n||!n.include||g(n.include,e)>=0)&&t(a,i,r)})}function r(t){return t[0]>t[1]&&t.reverse(),t}function o(t,e){return d.parseFinder(t,e,{includeMainTypes:x})}function a(t,e,n,i){var o=n.getAxis(["x","y"][t]),a=r(h.map([0,1],function(t){return e?o.coordToData(o.toLocalCoord(i[t])):o.toGlobalCoord(o.dataToCoord(i[t]))})),s=[];return s[t]=a,s[1-t]=[NaN,NaN],{values:a,xyMinMax:s}}function s(t,e,n,i){return[e[0]-i[t]*n[0],e[1]-i[t]*n[1]]}function l(t,e){var n=u(t),i=u(e),r=[n[0]/i[0],n[1]/i[1]];return isNaN(r[0])&&(r[0]=1),isNaN(r[1])&&(r[1]=1),r}function u(t){return t?[t[0][1]-t[0][0],t[1][1]-t[1][0]]:[NaN,NaN]}var h=n(1),c=n(3),d=n(5),f=n(190),p=h.each,g=h.indexOf,m=h.curry,v=["dataToPoint","pointToData"],x=["grid","xAxis","yAxis","geo","graph","polar","radiusAxis","angleAxis","bmap"],y=i.prototype;y.setOutputRanges=function(t,e){this.matchOutputRanges(t,e,function(t,e,n){if((t.coordRanges||(t.coordRanges=[])).push(e),!t.coordRange){t.coordRange=e;var i=S[t.brushType](0,n,e);t.__rangeOffset={offset:M[t.brushType](i.values,t.range,[1,1]),xyMinMax:i.xyMinMax}}})},y.matchOutputRanges=function(t,e,n){p(t,function(t){var i=this.findTargetInfo(t,e);i&&i!==!0&&h.each(i.coordSyses,function(i){var r=S[t.brushType](1,i,t.range);n(t,r.values,i,e)})},this)},y.setInputRanges=function(t,e){p(t,function(t){var n=this.findTargetInfo(t,e);if(t.range=t.range||[],n&&n!==!0){t.panelId=n.panelId;var i=S[t.brushType](0,n.coordSys,t.coordRange),r=t.__rangeOffset;t.range=r?M[t.brushType](i.values,r.offset,l(i.xyMinMax,r.xyMinMax)):i.values}},this)},y.makePanelOpts=function(t,e){return h.map(this._targetInfoList,function(n){var i=n.getPanelRect();return{panelId:n.panelId,defaultBrushType:e&&e(n),clipPath:f.makeRectPanelClipPath(i),isTargetByCursor:f.makeRectIsTargetByCursor(i,t,n.coordSysModel),getLinearBrushOtherExtent:f.makeLinearBrushOtherExtent(i)}})},y.controlSeries=function(t,e,n){var i=this.findTargetInfo(t,n);return i===!0||i&&g(i.coordSyses,e.coordinateSystem)>=0},y.findTargetInfo=function(t,e){for(var n=this._targetInfoList,i=o(e,t),r=0;r<n.length;r++){var a=n[r],s=t.panelId;if(s){if(a.panelId===s)return a}else for(var r=0;r<b.length;r++)if(b[r](i,a))return a}return!0};var _={grid:function(t,e){var n=t.xAxisModels,i=t.yAxisModels,r=t.gridModels,o=h.createHashMap(),a={},s={};(n||i||r)&&(p(n,function(t){var e=t.axis.grid.model;o.set(e.id,e),a[e.id]=!0}),p(i,function(t){var e=t.axis.grid.model;o.set(e.id,e),s[e.id]=!0}),p(r,function(t){o.set(t.id,t),a[t.id]=!0,s[t.id]=!0}),o.each(function(t){var r=t.coordinateSystem,o=[];p(r.getCartesians(),function(t,e){(g(n,t.getAxis("x").model)>=0||g(i,t.getAxis("y").model)>=0)&&o.push(t)}),e.push({panelId:"grid--"+t.id,gridModel:t,coordSysModel:t,coordSys:o[0],coordSyses:o,getPanelRect:w.grid,xAxisDeclared:a[t.id],yAxisDeclared:s[t.id]})}))},geo:function(t,e){p(t.geoModels,function(t){var n=t.coordinateSystem;e.push({panelId:"geo--"+t.id,geoModel:t,coordSysModel:t,coordSys:n,coordSyses:[n],getPanelRect:w.geo})})}},b=[function(t,e){var n=t.xAxisModel,i=t.yAxisModel,r=t.gridModel;return!r&&n&&(r=n.axis.grid.model),!r&&i&&(r=i.axis.grid.model),r&&r===e.gridModel},function(t,e){var n=t.geoModel;return n&&n===e.geoModel}],w={grid:function(){return this.coordSys.grid.getRect().clone()},geo:function(){var t=this.coordSys,e=t.getBoundingRect().clone();return e.applyTransform(c.getTransform(t)),e}},S={lineX:m(a,0),lineY:m(a,1),rect:function(t,e,n){var i=e[v[t]]([n[0][0],n[1][0]]),o=e[v[t]]([n[0][1],n[1][1]]),a=[r([i[0],o[0]]),r([i[1],o[1]])];return{values:a,xyMinMax:a}},polygon:function(t,e,n){var i=[[1/0,-(1/0)],[1/0,-(1/0)]],r=h.map(n,function(n){var r=e[v[t]](n);return i[0][0]=Math.min(i[0][0],r[0]),i[1][0]=Math.min(i[1][0],r[1]),i[0][1]=Math.max(i[0][1],r[0]),i[1][1]=Math.max(i[1][1],r[1]),r});return{values:r,xyMinMax:i}}},M={lineX:m(s,0),lineY:m(s,1),rect:function(t,e,n){return[[t[0][0]-n[0]*e[0][0],t[0][1]-n[0]*e[0][1]],[t[1][0]-n[1]*e[1][0],t[1][1]-n[1]*e[1][1]]]},polygon:function(t,e,n){return h.map(t,function(t,i){return[t[0]-n[0]*e[i][0],t[1]-n[1]*e[i][1]]})}};t.exports=i},function(t,e,n){function i(t){return o.create(t)}var r=n(132),o=n(12),a=n(3),s={};s.makeRectPanelClipPath=function(t){return t=i(t),function(e,n){return a.clipPointsByRect(e,t)}},s.makeLinearBrushOtherExtent=function(t,e){return t=i(t),function(n){var i=null!=e?e:n,r=i?t.width:t.height,o=i?t.x:t.y;return[o,o+(r||0)]}},s.makeRectIsTargetByCursor=function(t,e,n){return t=i(t),function(i,o,a){return t.contain(o[0],o[1])&&!r.onIrrelevantElement(i,e,n)}},t.exports=s},,,function(t,e){function n(){throw new Error("setTimeout has not been defined")}function i(){throw new Error("clearTimeout has not been defined")}function r(t){if(h===setTimeout)return setTimeout(t,0);if((h===n||!h)&&setTimeout)return h=setTimeout,setTimeout(t,0);try{return h(t,0)}catch(e){try{return h.call(null,t,0)}catch(e){return h.call(this,t,0)}}}function o(t){if(c===clearTimeout)return clearTimeout(t);if((c===i||!c)&&clearTimeout)return c=clearTimeout,clearTimeout(t);try{return c(t)}catch(e){try{return c.call(null,t)}catch(e){return c.call(this,t)}}}function a(){g&&f&&(g=!1,f.length?p=f.concat(p):m=-1,p.length&&s())}function s(){if(!g){var t=r(a);g=!0;for(var e=p.length;e;){for(f=p,p=[];++m<e;)f&&f[m].run();m=-1,e=p.length}f=null,g=!1,o(t)}}function l(t,e){this.fun=t,this.array=e}function u(){}var h,c,d=t.exports={};!function(){try{h="function"==typeof setTimeout?setTimeout:n}catch(t){h=n}try{c="function"==typeof clearTimeout?clearTimeout:i}catch(t){c=i}}();var f,p=[],g=!1,m=-1;d.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];p.push(new l(t,e)),1!==p.length||g||r(s)},l.prototype.run=function(){this.fun.apply(null,this.array)},d.title="browser",d.browser=!0,d.env={},d.argv=[],d.version="",d.versions={},d.on=u,d.addListener=u,d.once=u,d.off=u,d.removeListener=u,d.removeAllListeners=u,d.emit=u,d.prependListener=u,d.prependOnceListener=u,d.listeners=function(t){return[]},d.binding=function(t){throw new Error("process.binding is not supported")},d.cwd=function(){return"/"},d.chdir=function(t){throw new Error("process.chdir is not supported")},d.umask=function(){return 0}},function(t,e,n){function i(){this.group=new r.Group,this._symbolEl=new a({})}var r=n(3),o=n(24),a=r.extendShape({shape:{points:null,sizes:null},symbolProxy:null,buildPath:function(t,e){for(var n=e.points,i=e.sizes,r=this.symbolProxy,o=r.shape,a=0;a<n.length;a++){var s=n[a];if(!isNaN(s[0])&&!isNaN(s[1])){var l=i[a];l[0]<4?t.rect(s[0]-l[0]/2,s[1]-l[1]/2,l[0],l[1]):(o.x=s[0]-l[0]/2,o.y=s[1]-l[1]/2,o.width=l[0],o.height=l[1],r.buildPath(t,o,!0))}}},findDataIndex:function(t,e){for(var n=this.shape,i=n.points,r=n.sizes,o=i.length-1;o>=0;o--){var a=i[o],s=r[o],l=a[0]-s[0]/2,u=a[1]-s[1]/2;if(t>=l&&e>=u&&t<=l+s[0]&&e<=u+s[1])return o}return-1}}),s=i.prototype;s.updateData=function(t){this.group.removeAll();var e=this._symbolEl,n=t.hostModel;e.setShape({points:t.mapArray(t.getItemLayout),sizes:t.mapArray(function(e){var n=t.getItemVisual(e,"symbolSize");return n instanceof Array||(n=[n,n]),n})}),e.symbolProxy=o.createSymbol(t.getVisual("symbol"),0,0,0,0),e.setColor=e.symbolProxy.setColor,e.useStyle(n.getModel("itemStyle.normal").getItemStyle(["color"]));var i=t.getVisual("color");i&&e.setColor(i),e.seriesIndex=n.seriesIndex,e.on("mousemove",function(t){e.dataIndex=null;var n=e.findDataIndex(t.offsetX,t.offsetY);n>=0&&(e.dataIndex=n)}),this.group.add(e)},s.updateLayout=function(t){var e=t.getData();this._symbolEl.setShape({points:e.mapArray(e.getItemLayout)})},s.remove=function(){this.group.removeAll()},t.exports=i},function(t,e,n){function i(t){return isNaN(+t.cpx1)||isNaN(+t.cpy1)}var r=n(3),o=n(6),a=r.Line.prototype,s=r.BezierCurve.prototype;t.exports=r.extendShape({type:"ec-line",style:{stroke:"#000",fill:null},shape:{x1:0,y1:0,x2:0,y2:0,percent:1,cpx1:null,cpy1:null},buildPath:function(t,e){(i(e)?a:s).buildPath(t,e)},pointAt:function(t){return i(this.shape)?a.pointAt.call(this,t):s.pointAt.call(this,t)},tangentAt:function(t){var e=this.shape,n=i(e)?[e.x2-e.x1,e.y2-e.y1]:s.tangentAt.call(this,t);return o.normalize(n,n)}})},function(t,e,n){var i=n(1),r=n(2);n(197),n(198),r.registerVisual(i.curry(n(51),"scatter","circle",null)),r.registerLayout(i.curry(n(63),"scatter")),n(32)},function(t,e,n){"use strict";var i=n(28),r=n(17);t.exports=r.extend({type:"series.scatter",dependencies:["grid","polar","geo","singleAxis","calendar"],getInitialData:function(t,e){return i(t.data,this,e)},brushSelector:"point",defaultOption:{coordinateSystem:"cartesian2d",zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{normal:{opacity:.8}}}})},function(t,e,n){var i=n(46),r=n(194);n(2).extendChartView({type:"scatter",init:function(){this._normalSymbolDraw=new i,this._largeSymbolDraw=new r},render:function(t,e,n){var i=t.getData(),r=this._largeSymbolDraw,o=this._normalSymbolDraw,a=this.group,s=t.get("large")&&i.count()>t.get("largeThreshold")?r:o;this._symbolDraw=s,s.updateData(i),a.add(s.group),a.remove(s===r?o.group:r.group)},updateLayout:function(t){this._symbolDraw.updateLayout(t)},remove:function(t,e){this._symbolDraw&&this._symbolDraw.remove(e,!0)},dispose:function(){}})},function(t,e,n){var i=n(2),r=i.extendComponentModel({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}});t.exports=r},function(t,e,n){var i=n(126),r=n(2).extendComponentView({type:"axisPointer",render:function(t,e,n){var r=e.getComponent("tooltip"),o=t.get("triggerOn")||r&&r.get("triggerOn")||"mousemove|click";i.register("axisPointer",n,function(t,e,n){"none"!==o&&("leave"===t||o.indexOf(t)>=0)&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},remove:function(t,e){i.disopse(e.getZr(),"axisPointer"),r.superApply(this._model,"remove",arguments)},dispose:function(t,e){i.unregister("axisPointer",e),r.superApply(this._model,"dispose",arguments)}})},function(t,e,n){function i(t,e,n){var i=t.currTrigger,o=[t.x,t.y],g=t,m=t.dispatchAction||p.bind(n.dispatchAction,n),_=e.getComponent("axisPointer").coordSysAxesInfo;if(_){f(o)&&(o=v({seriesIndex:g.seriesIndex,dataIndex:g.dataIndex},e).point);var b=f(o),w=g.axesInfo,S=_.axesInfo,M="leave"===i||f(o),T={},I={},A={list:[],map:{}},C={showPointer:y(a,I),showTooltip:y(s,A)};x(_.coordSysMap,function(t,e){var n=b||t.containPoint(o);x(_.coordSysAxesInfo[e],function(t,e){var i=t.axis,a=c(w,t);if(!M&&n&&(!w||a)){var s=a&&a.value;null!=s||b||(s=i.pointToData(o)),null!=s&&r(t,s,C,!1,T)}})});var P={};return x(S,function(t,e){var n=t.linkGroup;n&&!I[e]&&x(n.axesInfo,function(e,i){var r=I[i];if(e!==t&&r){var o=r.value;n.mapper&&(o=t.axis.scale.parse(n.mapper(o,d(e),d(t)))),P[t.key]=o}})}),x(P,function(t,e){r(S[e],t,C,!0,T)}),l(I,S,T),u(A,o,t,m),h(S,m,n),T}}function r(t,e,n,i,r){var a=t.axis;if(!a.scale.isBlank()&&a.containData(e)){if(!t.involveSeries)return void n.showPointer(t,e);var s=o(e,t),l=s.payloadBatch,u=s.snapToValue;l[0]&&null==r.seriesIndex&&p.extend(r,l[0]),!i&&t.snap&&a.containData(u)&&null!=u&&(e=u),n.showPointer(t,e,l,r),n.showTooltip(t,s,u)}}function o(t,e){var n=e.axis,i=n.dim,r=t,o=[],a=Number.MAX_VALUE,s=-1;return x(e.seriesModels,function(e,l){var u,h,c=e.coordDimToDataDim(i);if(e.getAxisTooltipData){var d=e.getAxisTooltipData(c,t,n);h=d.dataIndices,u=d.nestestValue}else{if(h=e.getData().indicesOfNearest(c[0],t,!1,"category"===n.type?.5:null),!h.length)return;u=e.getData().get(c[0],h[0])}if(null!=u&&isFinite(u)){var f=t-u,p=Math.abs(f);p<=a&&((p<a||f>=0&&s<0)&&(a=p,s=f,r=u,o.length=0),x(h,function(t){o.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}}),{payloadBatch:o,snapToValue:r}}function a(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function s(t,e,n,i){var r=n.payloadBatch,o=e.axis,a=o.model,s=e.axisPointerModel;if(e.triggerTooltip&&r.length){var l=e.coordSys.model,u=m.makeKey(l),h=t.map[u];h||(h=t.map[u]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},t.list.push(h)),h.dataByAxis.push({axisDim:o.dim,axisIndex:a.componentIndex,axisType:a.type,axisId:a.id,value:i,valueLabelOpt:{precision:s.get("label.precision"),formatter:s.get("label.formatter")},seriesDataIndices:r.slice()})}}function l(t,e,n){var i=n.axesInfo=[];x(e,function(e,n){var r=e.axisPointerModel.option,o=t[n];o?(!e.useHandle&&(r.status="show"),r.value=o.value,r.seriesDataIndices=(o.payloadBatch||[]).slice()):!e.useHandle&&(r.status="hide"),"show"===r.status&&i.push({axisDim:e.axis.dim,axisIndex:e.axis.model.componentIndex,value:r.value})})}function u(t,e,n,i){if(f(e)||!t.list.length)return void i({type:"hideTip"});var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:n.tooltipOption,position:n.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}function h(t,e,n){var i=n.getZr(),r="axisPointerLastHighlights",o=_(i)[r]||{},a=_(i)[r]={};x(t,function(t,e){var n=t.axisPointerModel.option;"show"===n.status&&x(n.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;a[e]=t})});var s=[],l=[];p.each(o,function(t,e){!a[e]&&l.push(t)}),p.each(a,function(t,e){!o[e]&&s.push(t)}),l.length&&n.dispatchAction({type:"downplay",escapeConnect:!0,batch:l}),s.length&&n.dispatchAction({type:"highlight",escapeConnect:!0,batch:s})}function c(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}function d(t){var e=t.axis.model,n={},i=n.axisDim=t.axis.dim;return n.axisIndex=n[i+"AxisIndex"]=e.componentIndex,n.axisName=n[i+"AxisName"]=e.name,n.axisId=n[i+"AxisId"]=e.id,n}function f(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}var p=n(1),g=n(5),m=n(47),v=n(125),x=p.each,y=p.curry,_=g.makeGetter();t.exports=i},function(t,e,n){n(130),n(48),n(49),n(208),n(209),n(204),n(205),n(128),n(127)},function(t,e,n){function i(t,e,n){var i=[1/0,-(1/0)];return h(n,function(t){var n=t.getData();n&&h(t.coordDimToDataDim(e),function(t){var e=n.getDataExtent(t);e[0]<i[0]&&(i[0]=e[0]),e[1]>i[1]&&(i[1]=e[1])})}),i[1]<i[0]&&(i=[NaN,NaN]),r(t,i),i}function r(t,e){var n=t.getAxisModel(),i=n.getMin(!0),r="category"===n.get("type"),o=r&&(n.get("data")||[]).length;null!=i&&"dataMin"!==i&&"function"!=typeof i?e[0]=i:r&&(e[0]=o>0?0:NaN);var a=n.getMax(!0);return null!=a&&"dataMax"!==a&&"function"!=typeof a?e[1]=a:r&&(e[1]=o>0?o-1:NaN),n.get("scale",!0)||(e[0]>0&&(e[0]=0),e[1]<0&&(e[1]=0)),e}function o(t,e){var n=t.getAxisModel(),i=t._percentWindow,r=t._valueWindow;if(i){var o=l.getPixelPrecision(r,[0,500]);o=Math.min(o,20);var a=e||0===i[0]&&100===i[1];n.setRange(a?null:+r[0].toFixed(o),a?null:+r[1].toFixed(o))}}function a(t){var e=t._minMaxSpan={},n=t._dataZoomModel;h(["min","max"],function(i){e[i+"Span"]=n.get(i+"Span");var r=n.get(i+"ValueSpan");null!=r&&(e[i+"ValueSpan"]=r,r=t.getAxisModel().axis.scale.parse(r),null!=r&&(e[i+"Span"]=l.linearMap(r,t._dataExtent,[0,100],!0)))})}var s=n(1),l=n(4),u=n(81),h=s.each,c=l.asc,d=function(t,e,n,i){this._dimName=t,this._axisIndex=e,this._valueWindow,this._percentWindow,this._dataExtent,this._minMaxSpan,this.ecModel=i,this._dataZoomModel=n};d.prototype={constructor:d,hostedBy:function(t){return this._dataZoomModel===t},getDataValueWindow:function(){return this._valueWindow.slice()},getDataPercentWindow:function(){return this._percentWindow.slice()},getTargetSeriesModels:function(){var t=[],e=this.ecModel;return e.eachSeries(function(n){if(u.isCoordSupported(n.get("coordinateSystem"))){var i=this._dimName,r=e.queryComponents({mainType:i+"Axis",index:n.get(i+"AxisIndex"),id:n.get(i+"AxisId")})[0];this._axisIndex===(r&&r.componentIndex)&&t.push(n)}},this),t},getAxisModel:function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},getOtherAxisModel:function(){var t,e,n=this._dimName,i=this.ecModel,r=this.getAxisModel(),o="x"===n||"y"===n;o?(e="gridIndex",t="x"===n?"y":"x"):(e="polarIndex",t="angle"===n?"radius":"angle");var a;return i.eachComponent(t+"Axis",function(t){(t.get(e)||0)===(r.get(e)||0)&&(a=t)}),a},getMinMaxSpan:function(){return s.clone(this._minMaxSpan)},calculateDataWindow:function(t){var e=this._dataExtent,n=this.getAxisModel(),i=n.axis.scale,r=this._dataZoomModel.getRangePropMode(),o=[0,100],a=[t.start,t.end],s=[];return h(["startValue","endValue"],function(e){s.push(null!=t[e]?i.parse(t[e]):null)}),h([0,1],function(t){var n=s[t],u=a[t];"percent"===r[t]?(null==u&&(u=o[t]),n=i.parse(l.linearMap(u,o,e,!0))):u=l.linearMap(n,e,o,!0),s[t]=n,a[t]=u}),{valueWindow:c(s),percentWindow:c(a)}},reset:function(t){if(t===this._dataZoomModel){this._dataExtent=i(this,this._dimName,this.getTargetSeriesModels());var e=this.calculateDataWindow(t.option);this._valueWindow=e.valueWindow,this._percentWindow=e.percentWindow,a(this),o(this)}},restore:function(t){t===this._dataZoomModel&&(this._valueWindow=this._percentWindow=null,o(this,!0))},filterData:function(t){function e(t){return t>=o[0]&&t<=o[1]}if(t===this._dataZoomModel){var n=this._dimName,i=this.getTargetSeriesModels(),r=t.get("filterMode"),o=this._valueWindow;if("none"!==r){var a=this.getOtherAxisModel();t.get("$fromToolbox")&&a&&"category"===a.get("type")&&(r="empty"),h(i,function(t){var i=t.getData(),a=t.coordDimToDataDim(n);"weakFilter"===r?i&&i.filterSelf(function(t){for(var e,n,r,s=0;s<a.length;s++){var l=i.get(a[s],t),u=!isNaN(l),h=l<o[0],c=l>o[1];if(u&&!h&&!c)return!0;u&&(r=!0),h&&(e=!0),c&&(n=!0)}return r&&e&&n}):i&&h(a,function(n){"empty"===r?t.setData(i.map(n,function(t){return e(t)?t:NaN})):i.filterSelf(n,e)})})}}}},t.exports=d},function(t,e,n){t.exports=n(48).extend({type:"dataZoom.inside",defaultOption:{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,preventDefaultMouseMove:!0}})},function(t,e,n){var i=n(49),r=n(1),o=n(58),a=n(210),s=r.bind,l=i.extend({type:"dataZoom.inside",init:function(t,e){this._range},render:function(t,e,n,i){l.superApply(this,"render",arguments),a.shouldRecordRange(i,t.id)&&(this._range=t.getPercentRange()),r.each(this.getTargetCoordInfo(),function(e,i){var o=r.map(e,function(t){return a.generateCoordId(t.model)});r.each(e,function(e){var r=e.model,l=t.option;a.register(n,{coordId:a.generateCoordId(r),allCoordIds:o,containsPoint:function(t,e,n){return r.coordinateSystem.containPoint([e,n])},dataZoomId:t.id,throttleRate:t.get("throttle",!0),panGetRange:s(this._onPan,this,e,i),zoomGetRange:s(this._onZoom,this,e,i),zoomLock:l.zoomLock,disabled:l.disabled,roamControllerOpt:{zoomOnMouseWheel:l.zoomOnMouseWheel,moveOnMouseMove:l.moveOnMouseMove,preventDefaultMouseMove:l.preventDefaultMouseMove}})},this)},this)},dispose:function(){a.unregister(this.api,this.dataZoomModel.id),l.superApply(this,"dispose",arguments),this._range=null},_onPan:function(t,e,n,i,r,a,s,l,h){var c=this._range.slice(),d=t.axisModels[0];if(d){var f=u[e]([a,s],[l,h],d,n,t),p=f.signal*(c[1]-c[0])*f.pixel/f.pixelLength;return o(p,c,[0,100],"all"),this._range=c}},_onZoom:function(t,e,n,i,r,a){var s=this._range.slice(),l=t.axisModels[0];if(l){var h=u[e](null,[r,a],l,n,t),c=(h.signal>0?h.pixelStart+h.pixelLength-h.pixel:h.pixel-h.pixelStart)/h.pixelLength*(s[1]-s[0])+s[0];i=Math.max(1/i,0),s[0]=(s[0]-c)*i+c,s[1]=(s[1]-c)*i+c;var d=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();return o(0,s,[0,100],0,d.minSpan,d.maxSpan),this._range=s}}}),u={grid:function(t,e,n,i,r){var o=n.axis,a={},s=r.model.coordinateSystem.getRect();return t=t||[0,0],"x"===o.dim?(a.pixel=e[0]-t[0],a.pixelLength=s.width,a.pixelStart=s.x,a.signal=o.inverse?1:-1):(a.pixel=e[1]-t[1],a.pixelLength=s.height,a.pixelStart=s.y,a.signal=o.inverse?-1:1),a},polar:function(t,e,n,i,r){var o=n.axis,a={},s=r.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),u=s.getAngleAxis().getExtent();return t=t?s.pointToCoord(t):[0,0],e=s.pointToCoord(e),"radiusAxis"===n.mainType?(a.pixel=e[0]-t[0],a.pixelLength=l[1]-l[0],a.pixelStart=l[0],a.signal=o.inverse?1:-1):(a.pixel=e[1]-t[1],a.pixelLength=u[1]-u[0],a.pixelStart=u[0],a.signal=o.inverse?-1:1),a},singleAxis:function(t,e,n,i,r){var o=n.axis,a=r.model.coordinateSystem.getRect(),s={};return t=t||[0,0],"horizontal"===o.orient?(s.pixel=e[0]-t[0],s.pixelLength=a.width,s.pixelStart=a.x,s.signal=o.inverse?1:-1):(s.pixel=e[1]-t[1],s.pixelLength=a.height,s.pixelStart=a.y,s.signal=o.inverse?-1:1),s}};t.exports=l},function(t,e,n){var i=n(48);t.exports=i.extend({type:"dataZoom.select"})},function(t,e,n){t.exports=n(49).extend({type:"dataZoom.select"})},function(t,e,n){var i=n(48),r=i.extend({type:"dataZoom.slider",layoutMode:"box",defaultOption:{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#2f4554",width:.5,opacity:.3},areaStyle:{color:"rgba(47,69,84,0.3)",opacity:.3}},borderColor:"#ddd",fillerColor:"rgba(167,183,204,0.4)",handleIcon:"M8.2,13.6V3.9H6.3v9.7H3.1v14.9h3.3v9.7h1.8v-9.7h3.3V13.6H8.2z M9.7,24.4H4.8v-1.4h4.9V24.4z M9.7,19.1H4.8v-1.4h4.9V19.1z",handleSize:"100%",handleStyle:{color:"#a7b7cc"},labelPrecision:null,labelFormatter:null,showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#333"}}});t.exports=r},function(t,e,n){function i(t){var e={x:"y",y:"x",radius:"angle",angle:"radius"};return e[t]}function r(t){return"vertical"===t?"ns-resize":"ew-resize"}var o=n(1),a=n(3),s=n(37),l=n(49),u=a.Rect,h=n(4),c=h.linearMap,d=n(9),f=n(58),p=n(21),g=h.asc,m=o.bind,v=o.each,x=7,y=1,_=30,b="horizontal",w="vertical",S=5,M=["line","bar","candlestick","scatter"],T=l.extend({type:"dataZoom.slider",init:function(t,e){this._displayables={},this._orient,this._range,this._handleEnds,this._size,this._handleWidth,this._handleHeight,this._location,this._dragging,this._dataShadowInfo,this.api=e},render:function(t,e,n,i){return T.superApply(this,"render",arguments),s.createOrUpdate(this,"_dispatchZoomAction",this.dataZoomModel.get("throttle"),"fixRate"),this._orient=t.get("orient"),this.dataZoomModel.get("show")===!1?void this.group.removeAll():(i&&"dataZoom"===i.type&&i.from===this.uid||this._buildView(),void this._updateView())},remove:function(){T.superApply(this,"remove",arguments),s.clear(this,"_dispatchZoomAction")},dispose:function(){T.superApply(this,"dispose",arguments),s.clear(this,"_dispatchZoomAction")},_buildView:function(){var t=this.group;t.removeAll(),this._resetLocation(),this._resetInterval();var e=this._displayables.barGroup=new a.Group;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(e),this._positionGroup()},_resetLocation:function(){var t=this.dataZoomModel,e=this.api,n=this._findCoordRect(),i={width:e.getWidth(),height:e.getHeight()},r=this._orient===b?{right:i.width-n.x-n.width,top:i.height-_-x,width:n.width,height:_}:{right:x,top:n.y,width:_,height:n.height},a=d.getLayoutParams(t.option);o.each(["right","top","width","height"],function(t){"ph"===a[t]&&(a[t]=r[t])});var s=d.getLayoutRect(a,i,t.padding);this._location={x:s.x,y:s.y},this._size=[s.width,s.height],this._orient===w&&this._size.reverse()},_positionGroup:function(){var t=this.group,e=this._location,n=this._orient,i=this.dataZoomModel.getFirstTargetAxisModel(),r=i&&i.get("inverse"),o=this._displayables.barGroup,a=(this._dataShadowInfo||{}).otherAxisInverse;o.attr(n!==b||r?n===b&&r?{scale:a?[-1,1]:[-1,-1]}:n!==w||r?{scale:a?[-1,-1]:[-1,1],rotation:Math.PI/2}:{scale:a?[1,-1]:[1,1],rotation:Math.PI/2}:{scale:a?[1,1]:[1,-1]});var s=t.getBoundingRect([o]);t.attr("position",[e.x-s.x,e.y-s.y])},_getViewExtent:function(){return[0,this._size[0]]},_renderBackground:function(){var t=this.dataZoomModel,e=this._size,n=this._displayables.barGroup;n.add(new u({silent:!0,shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:t.get("backgroundColor")},z2:-40})),n.add(new u({shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:"transparent"},z2:0,onclick:o.bind(this._onClickPanelClick,this)}))},_renderDataShadow:function(){var t=this._dataShadowInfo=this._prepareDataShadowInfo();if(t){var e=this._size,n=t.series,i=n.getRawData(),r=n.getShadowDim?n.getShadowDim():t.otherDim;if(null!=r){var s=i.getDataExtent(r),l=.3*(s[1]-s[0]);s=[s[0]-l,s[1]+l];var u,h=[0,e[1]],d=[0,e[0]],f=[[e[0],0],[0,0]],p=[],g=d[1]/(i.count()-1),m=0,v=Math.round(i.count()/e[0]);i.each([r],function(t,e){if(v>0&&e%v)return void(m+=g);var n=null==t||isNaN(t)||""===t,i=n?0:c(t,s,h,!0);n&&!u&&e?(f.push([f[f.length-1][0],0]),p.push([p[p.length-1][0],0])):!n&&u&&(f.push([m,0]),p.push([m,0])),f.push([m,i]),p.push([m,i]),m+=g,u=n});var x=this.dataZoomModel;this._displayables.barGroup.add(new a.Polygon({shape:{points:f},style:o.defaults({fill:x.get("dataBackgroundColor")},x.getModel("dataBackground.areaStyle").getAreaStyle()),silent:!0,z2:-20})),this._displayables.barGroup.add(new a.Polyline({shape:{points:p},style:x.getModel("dataBackground.lineStyle").getLineStyle(),silent:!0,z2:-19}))}}},_prepareDataShadowInfo:function(){var t=this.dataZoomModel,e=t.get("showDataShadow");if(e!==!1){var n,r=this.ecModel;return t.eachTargetAxis(function(a,s){var l=t.getAxisProxy(a.name,s).getTargetSeriesModels();o.each(l,function(t){if(!(n||e!==!0&&o.indexOf(M,t.get("type"))<0)){var l,u=r.getComponent(a.axis,s).axis,h=i(a.name),c=t.coordinateSystem;null!=h&&c.getOtherAxis&&(l=c.getOtherAxis(u).inverse),n={thisAxis:u,series:t,thisDim:a.name,otherDim:h,otherAxisInverse:l}}},this)},this),n}},_renderHandle:function(){var t=this._displayables,e=t.handles=[],n=t.handleLabels=[],i=this._displayables.barGroup,o=this._size,s=this.dataZoomModel;i.add(t.filler=new u({draggable:!0,cursor:r(this._orient),drift:m(this._onDragMove,this,"all"),onmousemove:function(t){p.stop(t.event)},ondragstart:m(this._showDataInfo,this,!0),ondragend:m(this._onDragEnd,this),onmouseover:m(this._showDataInfo,this,!0),onmouseout:m(this._showDataInfo,this,!1),style:{fill:s.get("fillerColor"),textPosition:"inside"}})),i.add(new u(a.subPixelOptimizeRect({silent:!0,shape:{x:0,y:0,width:o[0],height:o[1]},style:{stroke:s.get("dataBackgroundColor")||s.get("borderColor"),lineWidth:y,fill:"rgba(0,0,0,0)"}}))),v([0,1],function(t){var o=a.createIcon(s.get("handleIcon"),{cursor:r(this._orient),draggable:!0,drift:m(this._onDragMove,this,t),onmousemove:function(t){p.stop(t.event)},ondragend:m(this._onDragEnd,this),onmouseover:m(this._showDataInfo,this,!0),onmouseout:m(this._showDataInfo,this,!1)},{x:-1,y:0,width:2,height:2}),l=o.getBoundingRect();this._handleHeight=h.parsePercent(s.get("handleSize"),this._size[1]),this._handleWidth=l.width/l.height*this._handleHeight,o.setStyle(s.getModel("handleStyle").getItemStyle());var u=s.get("handleColor");null!=u&&(o.style.fill=u),i.add(e[t]=o);var c=s.textStyleModel;this.group.add(n[t]=new a.Text({silent:!0,invisible:!0,style:{x:0,y:0,text:"",textVerticalAlign:"middle",textAlign:"center",textFill:c.getTextColor(),textFont:c.getFont()},z2:10}))},this)},_resetInterval:function(){var t=this._range=this.dataZoomModel.getPercentRange(),e=this._getViewExtent();this._handleEnds=[c(t[0],[0,100],e,!0),c(t[1],[0,100],e,!0)]},_updateInterval:function(t,e){var n=this.dataZoomModel,i=this._handleEnds,r=this._getViewExtent(),o=n.findRepresentativeAxisProxy().getMinMaxSpan(),a=[0,100];f(e,i,r,n.get("zoomLock")?"all":t,null!=o.minSpan?c(o.minSpan,a,r,!0):null,null!=o.maxSpan?c(o.maxSpan,a,r,!0):null),this._range=g([c(i[0],r,a,!0),c(i[1],r,a,!0)])},_updateView:function(t){var e=this._displayables,n=this._handleEnds,i=g(n.slice()),r=this._size;v([0,1],function(t){var i=e.handles[t],o=this._handleHeight;i.attr({scale:[o/2,o/2],position:[n[t],r[1]/2-o/2]})},this),e.filler.setShape({x:i[0],y:0,width:i[1]-i[0],height:r[1]}),this._updateDataInfo(t)},_updateDataInfo:function(t){function e(t){var e=a.getTransform(i.handles[t].parent,this.group),n=a.transformDirection(0===t?"right":"left",e),l=this._handleWidth/2+S,u=a.applyTransform([d[t]+(0===t?-l:l),this._size[1]/2],e);r[t].setStyle({x:u[0],y:u[1],textVerticalAlign:o===b?"middle":n,textAlign:o===b?n:"center",text:s[t]})}var n=this.dataZoomModel,i=this._displayables,r=i.handleLabels,o=this._orient,s=["",""];if(n.get("showDetail")){var l=n.findRepresentativeAxisProxy();if(l){var u=l.getAxisModel().axis,h=this._range,c=t?l.calculateDataWindow({start:h[0],end:h[1]}).valueWindow:l.getDataValueWindow();s=[this._formatLabel(c[0],u),this._formatLabel(c[1],u)]}}var d=g(this._handleEnds.slice());e.call(this,0),e.call(this,1)},_formatLabel:function(t,e){var n=this.dataZoomModel,i=n.get("labelFormatter"),r=n.get("labelPrecision");null!=r&&"auto"!==r||(r=e.getPixelPrecision());var a=null==t||isNaN(t)?"":"category"===e.type||"time"===e.type?e.scale.getLabel(Math.round(t)):t.toFixed(Math.min(r,20));return o.isFunction(i)?i(t,a):o.isString(i)?i.replace("{value}",a):a},_showDataInfo:function(t){t=this._dragging||t;var e=this._displayables.handleLabels;e[0].attr("invisible",!t),e[1].attr("invisible",!t)},_onDragMove:function(t,e,n){this._dragging=!0;var i=this._displayables.barGroup.getLocalTransform(),r=a.applyTransform([e,n],i,!0);this._updateInterval(t,r[0]);var o=this.dataZoomModel.get("realtime");this._updateView(!o),o&&o&&this._dispatchZoomAction()},_onDragEnd:function(){this._dragging=!1,this._showDataInfo(!1),this._dispatchZoomAction()},_onClickPanelClick:function(t){var e=this._size,n=this._displayables.barGroup.transformCoordToLocal(t.offsetX,t.offsetY);if(!(n[0]<0||n[0]>e[0]||n[1]<0||n[1]>e[1])){var i=this._handleEnds,r=(i[0]+i[1])/2;this._updateInterval("all",n[0]-r),this._updateView(),this._dispatchZoomAction()}},_dispatchZoomAction:function(){var t=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,start:t[0],end:t[1]})},_findCoordRect:function(){var t;if(v(this.getTargetCoordInfo(),function(e){if(!t&&e.length){var n=e[0].model.coordinateSystem;t=n.getRect&&n.getRect()}}),!t){var e=this.api.getWidth(),n=this.api.getHeight();t={x:.2*e,y:.2*n,width:.6*e,height:.6*n}}return t}});t.exports=T},function(t,e,n){function i(t){var e=t.getZr();return e[g]||(e[g]={})}function r(t,e){var n=new d(t.getZr());return n.on("pan",p(a,e)),n.on("zoom",p(s,e)),n}function o(t){c.each(t,function(e,n){e.count||(e.controller.dispose(),delete t[n])})}function a(t,e,n,i,r,o,a){l(t,function(s){return s.panGetRange(t.controller,e,n,i,r,o,a)})}function s(t,e,n,i){l(t,function(r){return r.zoomGetRange(t.controller,e,n,i)})}function l(t,e){var n=[];c.each(t.dataZoomInfos,function(t){var i=e(t);!t.disabled&&i&&n.push({dataZoomId:t.dataZoomId,start:i[0],end:i[1]})}),t.dispatchAction(n)}function u(t,e){t.dispatchAction({type:"dataZoom",batch:e})}function h(t){var e,n={},i={true:2,move:1,false:0,undefined:-1};return c.each(t,function(t){var r=!t.disabled&&(!t.zoomLock||"move");i[r]>i[e]&&(e=r),c.extend(n,t.roamControllerOpt)}),{controlType:e,opt:n}}var c=n(1),d=n(99),f=n(37),p=c.curry,g="\0_ec_dataZoom_roams",m={register:function(t,e){var n=i(t),a=e.dataZoomId,s=e.coordId;c.each(n,function(t,n){var i=t.dataZoomInfos;i[a]&&c.indexOf(e.allCoordIds,s)<0&&(delete i[a],t.count--)}),o(n);var l=n[s];l||(l=n[s]={coordId:s,dataZoomInfos:{},count:0},l.controller=r(t,l),l.dispatchAction=c.curry(u,t)),!l.dataZoomInfos[a]&&l.count++,
l.dataZoomInfos[a]=e;var d=h(l.dataZoomInfos);l.controller.enable(d.controlType,d.opt),l.controller.setPointerChecker(e.containsPoint),f.createOrUpdate(l,"dispatchAction",e.throttleRate,"fixRate")},unregister:function(t,e){var n=i(t);c.each(n,function(t){t.controller.dispose();var n=t.dataZoomInfos;n[e]&&(delete n[e],t.count--)}),o(n)},shouldRecordRange:function(t,e){if(t&&"dataZoom"===t.type&&t.batch)for(var n=0,i=t.batch.length;n<i;n++)if(t.batch[n].dataZoomId===e)return!1;return!0},generateCoordId:function(t){return t.type+"\0_"+t.id}};t.exports=m},function(t,e,n){n(130),n(48),n(49),n(206),n(207),n(128),n(127)},function(t,e,n){function i(t,e,n,i){var r=n.type,o=f[r.charAt(0).toUpperCase()+r.slice(1)],a=new o(n);e.add(a),i.set(t,a),a.__ecGraphicId=t}function r(t,e){var n=t&&t.parent;n&&("group"===t.type&&t.traverse(function(t){r(t,e)}),e.removeKey(t.__ecGraphicId),n.remove(t))}function o(t){return t=c.extend({},t),c.each(["id","parentId","$action","hv","bounding"].concat(p.LOCATION_PARAMS),function(e){delete t[e]}),t}function a(t,e){var n;return c.each(e,function(e){null!=t[e]&&"auto"!==t[e]&&(n=!0)}),n}function s(t,e){var n=t.exist;if(e.id=t.keyInfo.id,!e.type&&n&&(e.type=n.type),null==e.parentId){var i=e.parentOption;i?e.parentId=i.id:n&&(e.parentId=n.parentId)}e.parentOption=null}function l(t,e,n){var i=c.extend({},n),r=t[e],o=n.$action||"merge";if("merge"===o)if(r){c.merge(r,i,!0),p.mergeLayoutParam(r,i,{ignoreSize:!0}),p.copyLayoutParams(n,r)}else t[e]=i;else"replace"===o?t[e]=i:"remove"===o&&r&&(t[e]=null)}function u(t,e){t&&(t.hv=e.hv=[a(e,["left","right"]),a(e,["top","bottom"])],"group"===t.type&&(null==t.width&&(t.width=e.width=0),null==t.height&&(t.height=e.height=0)))}var h=n(2),c=n(1),d=n(5),f=n(3),p=n(9);h.registerPreprocessor(function(t){var e=t.graphic;c.isArray(e)?e[0]&&e[0].elements?t.graphic=[t.graphic[0]]:t.graphic=[{elements:e}]:e&&!e.elements&&(t.graphic=[{elements:[e]}])});var g=h.extendComponentModel({type:"graphic",defaultOption:{elements:[],parentId:null},_elOptionsToUpdate:null,mergeOption:function(t){var e=this.option.elements;this.option.elements=null,g.superApply(this,"mergeOption",arguments),this.option.elements=e},optionUpdated:function(t,e){var n=this.option,i=(e?n:t).elements,r=n.elements=e?[]:n.elements,o=[];this._flatten(i,o);var a=d.mappingToExists(r,o);d.makeIdAndName(a);var h=this._elOptionsToUpdate=[];c.each(a,function(t,e){var n=t.option;n&&(h.push(n),s(t,n),l(r,e,n),u(r[e],n))},this);for(var f=r.length-1;f>=0;f--)null==r[f]?r.splice(f,1):delete r[f].$action},_flatten:function(t,e,n){c.each(t,function(t){if(t){n&&(t.parentOption=n),e.push(t);var i=t.children;"group"===t.type&&i&&this._flatten(i,e,t),delete t.children}},this)},useElOptionsToUpdate:function(){var t=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,t}});h.extendComponentView({type:"graphic",init:function(t,e){this._elMap=c.createHashMap(),this._lastGraphicModel},render:function(t,e,n){t!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=t,this._updateElements(t,n),this._relocate(t,n)},_updateElements:function(t,e){var n=t.useElOptionsToUpdate();if(n){var a=this._elMap,s=this.group;c.each(n,function(t){var e=t.$action,n=t.id,l=a.get(n),u=t.parentId,h=null!=u?a.get(u):s;if("text"===t.type){var c=t.style;t.hv&&t.hv[1]&&(c.textVerticalAlign=c.textBaseline=null),!c.hasOwnProperty("textFill")&&c.fill&&(c.textFill=c.fill),!c.hasOwnProperty("textStroke")&&c.stroke&&(c.textStroke=c.stroke)}var d=o(t);e&&"merge"!==e?"replace"===e?(r(l,a),i(n,h,d,a)):"remove"===e&&r(l,a):l?l.attr(d):i(n,h,d,a);var f=a.get(n);f&&(f.__ecGraphicWidth=t.width,f.__ecGraphicHeight=t.height)})}},_relocate:function(t,e){for(var n=t.option.elements,i=this.group,r=this._elMap,o=n.length-1;o>=0;o--){var a=n[o],s=r.get(a.id);if(s){var l=s.parent,u=l===i?{width:e.getWidth(),height:e.getHeight()}:{width:l.__ecGraphicWidth||0,height:l.__ecGraphicHeight||0};p.positionElement(s,a,u,null,{hv:a.hv,boundingMode:a.bounding})}}},_clear:function(){var t=this._elMap;t.each(function(e){r(e,t)}),this._elMap=c.createHashMap()},dispose:function(){this._clear()}})},function(t,e,n){n(32),n(124),n(57)},function(t,e,n){n(135),n(217),n(136);var i=n(2);i.registerProcessor(n(218)),n(13).registerSubTypeDefaulter("legend",function(){return"plain"})},function(t,e,n){function i(t,e,n){var i=t.getOrient(),r=[1,1];r[i.index]=0,o.mergeLayoutParam(e,n,{type:"box",ignoreSize:r})}var r=n(135),o=n(9),a=r.extend({type:"legend.scroll",setScrollDataIndex:function(t){this.option.scrollDataIndex=t},defaultOption:{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},init:function(t,e,n,r){var s=o.getLayoutParams(t);a.superCall(this,"init",t,e,n,r),i(this,t,s)},mergeOption:function(t,e){a.superCall(this,"mergeOption",t,e),i(this,this.option,t)},getOrient:function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}}});t.exports=a},function(t,e,n){var i=n(1),r=n(3),o=n(9),a=n(136),s=r.Group,l=["width","height"],u=["x","y"],h=a.extend({type:"legend.scroll",newlineDisabled:!0,init:function(){h.superCall(this,"init"),this._currentIndex=0,this.group.add(this._containerGroup=new s),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new s)},resetInner:function(){h.superCall(this,"resetInner"),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},renderInner:function(t,e,n,o){function a(t,n){var a=t+"DataIndex",h=r.createIcon(e.get("pageIcons",!0)[e.getOrient().name][n],{onclick:i.bind(s._pageGo,s,a,e,o)},{x:-u[0]/2,y:-u[1]/2,width:u[0],height:u[1]});h.name=t,l.add(h)}var s=this;h.superCall(this,"renderInner",t,e,n,o);var l=this._controllerGroup,u=e.get("pageIconSize",!0);i.isArray(u)||(u=[u,u]),a("pagePrev",0);var c=e.getModel("pageTextStyle");l.add(new r.Text({name:"pageText",style:{textFill:c.getTextColor(),font:c.getFont(),textVerticalAlign:"middle",textAlign:"center"},silent:!0})),a("pageNext",1)},layoutInner:function(t,e,n){var a=this.getContentGroup(),s=this._containerGroup,h=this._controllerGroup,c=t.getOrient().index,d=l[c],f=l[1-c],p=u[1-c];o.box(t.get("orient"),a,t.get("itemGap"),c?n.width:null,c?null:n.height),o.box("horizontal",h,t.get("pageButtonItemGap",!0));var g=a.getBoundingRect(),m=h.getBoundingRect(),v=g[d]>n[d],x=[-g.x,-g.y];x[c]=a.position[c];var y=[0,0],_=[-m.x,-m.y],b=i.retrieve2(t.get("pageButtonGap",!0),t.get("itemGap",!0));if(v){var w=t.get("pageButtonPosition",!0);"end"===w?_[c]+=n[d]-m[d]:y[c]+=m[d]+b}_[1-c]+=g[f]/2-m[f]/2,a.attr("position",x),s.attr("position",y),h.attr("position",_);var S=this.group.getBoundingRect(),S={x:0,y:0};if(S[d]=v?n[d]:g[d],S[f]=Math.max(g[f],m[f]),S[p]=Math.min(0,m[p]+_[1-c]),s.__rectSize=n[d],v){var M={x:0,y:0};M[d]=Math.max(n[d]-m[d]-b,0),M[f]=S[f],s.setClipPath(new r.Rect({shape:M})),s.__rectSize=M[d]}else h.eachChild(function(t){t.attr({invisible:!0,silent:!0})});var T=this._getPageInfo(t);return null!=T.pageIndex&&r.updateProps(a,{position:T.contentPosition},t),this._updatePageInfoView(t,T),S},_pageGo:function(t,e,n){var i=this._getPageInfo(e)[t];null!=i&&n.dispatchAction({type:"legendScroll",scrollDataIndex:i,legendId:e.id})},_updatePageInfoView:function(t,e){var n=this._controllerGroup;i.each(["pagePrev","pageNext"],function(i){var r=null!=e[i+"DataIndex"],o=n.childOfName(i);o&&(o.setStyle("fill",r?t.get("pageIconColor",!0):t.get("pageIconInactiveColor",!0)),o.cursor=r?"pointer":"default")});var r=n.childOfName("pageText"),o=t.get("pageFormatter"),a=e.pageIndex,s=null!=a?a+1:0,l=e.pageCount;r&&o&&r.setStyle("text",i.isString(o)?o.replace("{current}",s).replace("{total}",l):o({current:s,total:l}))},_getPageInfo:function(t){function e(t){var e=t.getBoundingRect().clone();return e[g]+=t.position[d],e}var n,i,r,o,a=t.get("scrollDataIndex",!0),s=this.getContentGroup(),h=s.getBoundingRect(),c=this._containerGroup.__rectSize,d=t.getOrient().index,f=l[d],p=l[1-d],g=u[d],m=s.position.slice();s.eachChild(function(t){t.__legendDataIndex===a&&(o=t)});var v=c?Math.ceil(h[f]/c):0;if(o){var x=o.getBoundingRect(),y=o.position[d]+x[g];m[d]=-y-h[g],n=Math.floor(v*(y+x[g]+c/2)/h[f]),n=h[f]&&v?Math.max(0,Math.min(v-1,n)):-1;var _={x:0,y:0};_[f]=c,_[p]=h[p],_[g]=-m[d]-h[g];var b,w=s.children();if(s.eachChild(function(t,n){var i=e(t);i.intersect(_)&&(null==b&&(b=n),r=t.__legendDataIndex),n===w.length-1&&i[g]+i[f]<=_[g]+_[f]&&(r=null)}),null!=b){var S=w[b],M=e(S);if(_[g]=M[g]+M[f]-_[f],b<=0&&M[g]>=_[g])i=null;else{for(;b>0&&e(w[b-1]).intersect(_);)b--;i=w[b].__legendDataIndex}}}return{contentPosition:m,pageIndex:n,pageCount:v,pagePrevDataIndex:i,pageNextDataIndex:r}}});t.exports=h},function(t,e,n){function i(t,e,n){var i,r={},a="toggleSelected"===t;return n.eachComponent("legend",function(n){a&&null!=i?n[i?"select":"unSelect"](e.name):(n[t](e.name),i=n.isSelected(e.name));var s=n.getData();o.each(s,function(t){var e=t.get("name");if("\n"!==e&&""!==e){var i=n.isSelected(e);r.hasOwnProperty(e)?r[e]=r[e]&&i:r[e]=i}})}),{name:e.name,selected:r}}var r=n(2),o=n(1);r.registerAction("legendToggleSelect","legendselectchanged",o.curry(i,"toggleSelected")),r.registerAction("legendSelect","legendselected",o.curry(i,"select")),r.registerAction("legendUnSelect","legendunselected",o.curry(i,"unSelect"))},function(t,e){t.exports=function(t){var e=t.findComponents({mainType:"legend"});e&&e.length&&t.filterSeries(function(t){for(var n=0;n<e.length;n++)if(!e[n].isSelected(t.name))return!1;return!0})}},function(t,e,n){n(2).registerAction("legendScroll","legendscroll",function(t,e){var n=t.scrollDataIndex;null!=n&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(n)})})},function(t,e,n){n(214),n(215),n(216),n(219)},function(t,e,n){n(224),n(225),n(2).registerPreprocessor(function(t){t.markArea=t.markArea||{}})},function(t,e,n){n(226),n(227),n(2).registerPreprocessor(function(t){t.markLine=t.markLine||{}})},function(t,e,n){n(228),n(229),n(2).registerPreprocessor(function(t){t.markPoint=t.markPoint||{}})},function(t,e,n){t.exports=n(83).extend({type:"markArea",defaultOption:{zlevel:0,z:1,tooltip:{trigger:"item"},animation:!1,label:{normal:{show:!0,position:"top"},emphasis:{show:!0,position:"top"}},itemStyle:{normal:{borderWidth:0}}}})},function(t,e,n){function i(t){return!isNaN(t)&&!isFinite(t)}function r(t,e,n,r){var o=1-t;return i(e[o])&&i(n[o])}function o(t,e){var n=e.coord[0],i=e.coord[1];return!("cartesian2d"!==t.type||!n||!i||!r(1,n,i,t)&&!r(0,n,i,t))||(f.dataFilter(t,{coord:n,x:e.x0,y:e.y0})||f.dataFilter(t,{coord:i,x:e.x1,y:e.y1}))}function a(t,e,n,r,o){var a,s=r.coordinateSystem,l=t.getItemModel(e),u=h.parsePercent(l.get(n[0]),o.getWidth()),c=h.parsePercent(l.get(n[1]),o.getHeight());if(isNaN(u)||isNaN(c)){if(r.getMarkerPosition)a=r.getMarkerPosition(t.getValues(n,e));else{var d=t.get(n[0],e),f=t.get(n[1],e);a=s.dataToPoint([d,f],!0)}if("cartesian2d"===s.type){var p=s.getAxis("x"),g=s.getAxis("y"),d=t.get(n[0],e),f=t.get(n[1],e);i(d)?a[0]=p.toGlobalCoord(p.getExtent()["x0"===n[0]?0:1]):i(f)&&(a[1]=g.toGlobalCoord(g.getExtent()["y0"===n[1]?0:1]))}isNaN(u)||(a[0]=u),isNaN(c)||(a[1]=c)}else a=[u,c];return a}function s(t,e,n){var i,r,a=["x0","y0","x1","y1"];t?(i=l.map(t&&t.dimensions,function(t){var n=e.getData().getDimensionInfo(e.coordDimToDataDim(t)[0])||{};return n.name=t,n}),r=new u(l.map(a,function(t,e){return{name:t,type:i[e%2].type}}),n)):(i=[{name:"value",type:"float"}],r=new u(i,n));var s=l.map(n.get("data"),l.curry(p,e,t,n));t&&(s=l.filter(s,l.curry(o,t)));var h=t?function(t,e,n,i){return t.coord[Math.floor(i/2)][i%2]}:function(t){return t.value};return r.initData(s,null,h),r.hasItemOption=!0,r}var l=n(1),u=n(14),h=n(4),c=n(3),d=n(22),f=n(85),p=function(t,e,n,i){var r=f.dataTransform(t,i[0]),o=f.dataTransform(t,i[1]),a=l.retrieve,s=r.coord,u=o.coord;s[0]=a(s[0],-(1/0)),s[1]=a(s[1],-(1/0)),u[0]=a(u[0],1/0),u[1]=a(u[1],1/0);var h=l.mergeAll([{},r,o]);return h.coord=[r.coord,o.coord],h.x0=r.x,h.y0=r.y,h.x1=o.x,h.y1=o.y,h},g=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]];n(84).extend({type:"markArea",updateLayout:function(t,e,n){e.eachSeries(function(t){var e=t.markAreaModel;if(e){var i=e.getData();i.each(function(e){var r=l.map(g,function(r){return a(i,e,r,t,n)});i.setItemLayout(e,r);var o=i.getItemGraphicEl(e);o.setShape("points",r)})}},this)},renderSeries:function(t,e,n,i){var r=t.coordinateSystem,o=t.name,u=t.getData(),h=this.markerGroupMap,f=h.get(o)||h.set(o,{group:new c.Group});this.group.add(f.group),f.__keep=!0;var p=s(r,t,e);e.setData(p),p.each(function(e){p.setItemLayout(e,l.map(g,function(n){return a(p,e,n,t,i)})),p.setItemVisual(e,{color:u.getVisual("color")})}),p.diff(f.__data).add(function(t){var e=new c.Polygon({shape:{points:p.getItemLayout(t)}});p.setItemGraphicEl(t,e),f.group.add(e)}).update(function(t,n){var i=f.__data.getItemGraphicEl(n);c.updateProps(i,{shape:{points:p.getItemLayout(t)}},e,t),f.group.add(i),p.setItemGraphicEl(t,i)}).remove(function(t){var e=f.__data.getItemGraphicEl(t);f.group.remove(e)}).execute(),p.eachItemGraphicEl(function(t,n){var i=p.getItemModel(n),r=i.getModel("label.normal"),o=i.getModel("label.emphasis"),a=p.getItemVisual(n,"color");t.useStyle(l.defaults(i.getModel("itemStyle.normal").getItemStyle(),{fill:d.modifyAlpha(a,.4),stroke:a})),t.hoverStyle=i.getModel("itemStyle.normal").getItemStyle(),c.setLabelStyle(t.style,t.hoverStyle,r,o,{labelFetcher:e,labelDataIndex:n,defaultText:p.getName(n)||"",isRectText:!0,autoColor:a}),c.setHoverStyle(t,{}),t.dataModel=e}),f.__data=p,f.group.silent=e.get("silent")||t.get("silent")}})},function(t,e,n){t.exports=n(83).extend({type:"markLine",defaultOption:{zlevel:0,z:5,symbol:["circle","arrow"],symbolSize:[8,16],precision:2,tooltip:{trigger:"item"},label:{normal:{show:!0,position:"end"},emphasis:{show:!0}},lineStyle:{normal:{type:"dashed"},emphasis:{width:3}},animationEasing:"linear"}})},function(t,e,n){function i(t){return!isNaN(t)&&!isFinite(t)}function r(t,e,n,r){var o=1-t,a=r.dimensions[t];return i(e[o])&&i(n[o])&&e[t]===n[t]&&r.getAxis(a).containData(e[t])}function o(t,e){if("cartesian2d"===t.type){var n=e[0].coord,i=e[1].coord;if(n&&i&&(r(1,n,i,t)||r(0,n,i,t)))return!0}return c.dataFilter(t,e[0])&&c.dataFilter(t,e[1])}function a(t,e,n,r,o){var a,s=r.coordinateSystem,l=t.getItemModel(e),u=h.parsePercent(l.get("x"),o.getWidth()),c=h.parsePercent(l.get("y"),o.getHeight());if(isNaN(u)||isNaN(c)){if(r.getMarkerPosition)a=r.getMarkerPosition(t.getValues(t.dimensions,e));else{var d=s.dimensions,f=t.get(d[0],e),p=t.get(d[1],e);a=s.dataToPoint([f,p])}if("cartesian2d"===s.type){var g=s.getAxis("x"),m=s.getAxis("y"),d=s.dimensions;i(t.get(d[0],e))?a[0]=g.toGlobalCoord(g.getExtent()[n?0:1]):i(t.get(d[1],e))&&(a[1]=m.toGlobalCoord(m.getExtent()[n?0:1]))}isNaN(u)||(a[0]=u),isNaN(c)||(a[1]=c)}else a=[u,c];t.setItemLayout(e,a)}function s(t,e,n){var i;i=t?l.map(t&&t.dimensions,function(t){var n=e.getData().getDimensionInfo(e.coordDimToDataDim(t)[0])||{};return n.name=t,n}):[{name:"value",type:"float"}];var r=new u(i,n),a=new u(i,n),s=new u([],n),h=l.map(n.get("data"),l.curry(f,e,t,n));t&&(h=l.filter(h,l.curry(o,t)));var d=t?c.dimValueGetter:function(t){return t.value};return r.initData(l.map(h,function(t){return t[0]}),null,d),a.initData(l.map(h,function(t){return t[1]}),null,d),s.initData(l.map(h,function(t){return t[2]})),s.hasItemOption=!0,{from:r,to:a,line:s}}var l=n(1),u=n(14),h=n(4),c=n(85),d=n(111),f=function(t,e,n,i){var r=t.getData(),o=i.type;if(!l.isArray(i)&&("min"===o||"max"===o||"average"===o||null!=i.xAxis||null!=i.yAxis)){var a,s,u;if(null!=i.yAxis||null!=i.xAxis)s=null!=i.yAxis?"y":"x",a=e.getAxis(s),u=l.retrieve(i.yAxis,i.xAxis);else{var h=c.getAxisInfo(i,r,e,t);s=h.valueDataDim,a=h.valueAxis,u=c.numCalculate(r,s,o)}var d="x"===s?0:1,f=1-d,p=l.clone(i),g={};p.type=null,p.coord=[],g.coord=[],p.coord[f]=-(1/0),g.coord[f]=1/0;var m=n.get("precision");m>=0&&"number"==typeof u&&(u=+u.toFixed(Math.min(m,20))),p.coord[d]=g.coord[d]=u,i=[p,g,{type:o,valueIndex:i.valueIndex,value:u}]}return i=[c.dataTransform(t,i[0]),c.dataTransform(t,i[1]),l.extend({},i[2])],i[2].type=i[2].type||"",l.merge(i[2],i[0]),l.merge(i[2],i[1]),i};n(84).extend({type:"markLine",updateLayout:function(t,e,n){e.eachSeries(function(t){var e=t.markLineModel;if(e){var i=e.getData(),r=e.__from,o=e.__to;r.each(function(e){a(r,e,!0,t,n),a(o,e,!1,t,n)}),i.each(function(t){i.setItemLayout(t,[r.getItemLayout(t),o.getItemLayout(t)])}),this.markerGroupMap.get(t.id).updateLayout()}},this)},renderSeries:function(t,e,n,i){function r(e,n,r){var o=e.getItemModel(n);a(e,n,r,t,i),e.setItemVisual(n,{symbolSize:o.get("symbolSize")||y[r?0:1],symbol:o.get("symbol",!0)||x[r?0:1],color:o.get("itemStyle.normal.color")||h.getVisual("color")})}var o=t.coordinateSystem,u=t.id,h=t.getData(),c=this.markerGroupMap,f=c.get(u)||c.set(u,new d);this.group.add(f.group);var p=s(o,t,e),g=p.from,m=p.to,v=p.line;e.__from=g,e.__to=m,e.setData(v);var x=e.get("symbol"),y=e.get("symbolSize");l.isArray(x)||(x=[x,x]),"number"==typeof y&&(y=[y,y]),p.from.each(function(t){r(g,t,!0),r(m,t,!1)}),v.each(function(t){var e=v.getItemModel(t).get("lineStyle.normal.color");v.setItemVisual(t,{color:e||g.getItemVisual(t,"color")}),v.setItemLayout(t,[g.getItemLayout(t),m.getItemLayout(t)]),v.setItemVisual(t,{fromSymbolSize:g.getItemVisual(t,"symbolSize"),fromSymbol:g.getItemVisual(t,"symbol"),toSymbolSize:m.getItemVisual(t,"symbolSize"),toSymbol:m.getItemVisual(t,"symbol")})}),f.updateData(v),p.line.eachItemGraphicEl(function(t,n){t.traverse(function(t){t.dataModel=e})}),f.__keep=!0,f.group.silent=e.get("silent")||t.get("silent")}})},function(t,e,n){t.exports=n(83).extend({type:"markPoint",defaultOption:{zlevel:0,z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{normal:{show:!0,position:"inside"},emphasis:{show:!0}},itemStyle:{normal:{borderWidth:2}}}})},function(t,e,n){function i(t,e,n){var i=e.coordinateSystem;t.each(function(r){var o,a=t.getItemModel(r),l=s.parsePercent(a.get("x"),n.getWidth()),u=s.parsePercent(a.get("y"),n.getHeight());if(isNaN(l)||isNaN(u)){if(e.getMarkerPosition)o=e.getMarkerPosition(t.getValues(t.dimensions,r));else if(i){var h=t.get(i.dimensions[0],r),c=t.get(i.dimensions[1],r);o=i.dataToPoint([h,c])}}else o=[l,u];isNaN(l)||(o[0]=l),isNaN(u)||(o[1]=u),t.setItemLayout(r,o)})}function r(t,e,n){var i;i=t?a.map(t&&t.dimensions,function(t){var n=e.getData().getDimensionInfo(e.coordDimToDataDim(t)[0])||{};return n.name=t,n}):[{name:"value",type:"float"}];var r=new l(i,n),o=a.map(n.get("data"),a.curry(u.dataTransform,e));return t&&(o=a.filter(o,a.curry(u.dataFilter,t))),r.initData(o,null,t?u.dimValueGetter:function(t){return t.value}),r}var o=n(46),a=n(1),s=n(4),l=n(14),u=n(85);n(84).extend({type:"markPoint",updateLayout:function(t,e,n){e.eachSeries(function(t){var e=t.markPointModel;e&&(i(e.getData(),t,n),this.markerGroupMap.get(t.id).updateLayout(e))},this)},renderSeries:function(t,e,n,a){var s=t.coordinateSystem,l=t.id,u=t.getData(),h=this.markerGroupMap,c=h.get(l)||h.set(l,new o),d=r(s,t,e);e.setData(d),i(e.getData(),t,a),d.each(function(t){var n=d.getItemModel(t),i=n.getShallow("symbolSize");"function"==typeof i&&(i=i(e.getRawValue(t),e.getDataParams(t))),d.setItemVisual(t,{symbolSize:i,color:n.get("itemStyle.normal.color")||u.getVisual("color"),symbol:n.getShallow("symbol")})}),c.updateData(d),this.group.add(c.group),d.eachItemGraphicEl(function(t){t.traverse(function(t){t.dataModel=e})}),c.__keep=!0,c.group.silent=e.get("silent")||t.get("silent")}})},function(t,e,n){"use strict";var i=n(2),r=n(3),o=n(9);i.extendComponentModel({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),i.extendComponentView({type:"title",render:function(t,e,n){if(this.group.removeAll(),t.get("show")){var i=this.group,a=t.getModel("textStyle"),s=t.getModel("subtextStyle"),l=t.get("textAlign"),u=t.get("textBaseline"),h=new r.Text({style:r.setTextStyle({},a,{text:t.get("text"),textFill:a.getTextColor()},{disableBox:!0}),z2:10}),c=h.getBoundingRect(),d=t.get("subtext"),f=new r.Text({style:r.setTextStyle({},s,{text:d,textFill:s.getTextColor(),y:c.height+t.get("itemGap"),textVerticalAlign:"top"},{disableBox:!0}),z2:10}),p=t.get("link"),g=t.get("sublink");h.silent=!p,f.silent=!g,p&&h.on("click",function(){window.open(p,"_"+t.get("target"))}),g&&f.on("click",function(){window.open(g,"_"+t.get("subtarget"))}),i.add(h),d&&i.add(f);var m=i.getBoundingRect(),v=t.getBoxLayoutParams();v.width=m.width,v.height=m.height;var x=o.getLayoutRect(v,{width:n.getWidth(),height:n.getHeight()},t.get("padding"));l||(l=t.get("left")||t.get("right"),"middle"===l&&(l="center"),"right"===l?x.x+=x.width:"center"===l&&(x.x+=x.width/2)),u||(u=t.get("top")||t.get("bottom"),"center"===u&&(u="middle"),"bottom"===u?x.y+=x.height:"middle"===u&&(x.y+=x.height/2),u=u||"top"),i.attr("position",[x.x,x.y]);var y={textAlign:l,textVerticalAlign:u};h.setStyle(y),f.setStyle(y),m=i.getBoundingRect();var _=x.margin,b=t.getItemStyle(["color","opacity"]);b.fill=t.get("backgroundColor");var w=new r.Rect({shape:{x:m.x-_[3],y:m.y-_[0],width:m.width+_[1]+_[3],height:m.height+_[0]+_[2],r:t.get("borderRadius")},style:b,silent:!0});r.subPixelOptimizeRect(w),i.add(w)}}})},function(t,e,n){n(232),n(233),n(238),n(236),n(234),n(235),n(237)},function(t,e,n){var i=n(29),r=n(1),o=n(2).extendComponentModel({type:"toolbox",layoutMode:{type:"box",ignoreSize:!0},mergeDefaultAndTheme:function(t){o.superApply(this,"mergeDefaultAndTheme",arguments),r.each(this.option.feature,function(t,e){var n=i.get(e);n&&r.merge(t,n.defaultOption)})},defaultOption:{show:!0,z:6,zlevel:0,orient:"horizontal",left:"right",top:"top",backgroundColor:"transparent",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemSize:15,itemGap:8,showTitle:!0,iconStyle:{normal:{borderColor:"#666",color:"none"},emphasis:{borderColor:"#3E98C5"}}}});t.exports=o},function(t,e,n){(function(e){function i(t){return 0===t.indexOf("my")}var r=n(29),o=n(1),a=n(3),s=n(11),l=n(44),u=n(134),h=n(16);t.exports=n(2).extendComponentView({type:"toolbox",render:function(t,e,n,c){function d(o,a){var l,u=x[o],h=x[a],d=m[u],p=new s(d,t,t.ecModel);if(u&&!h){if(i(u))l={model:p,onclick:p.option.onclick,featureName:u};else{var g=r.get(u);if(!g)return;l=new g(p,e,n)}v[u]=l}else{if(l=v[h],!l)return;l.model=p,l.ecModel=e,l.api=n}return!u&&h?void(l.dispose&&l.dispose(e,n)):!p.get("show")||l.unusable?void(l.remove&&l.remove(e,n)):(f(p,l,u),p.setIconStatus=function(t,e){var n=this.option,i=this.iconPaths;n.iconStatus=n.iconStatus||{},n.iconStatus[t]=e,i[t]&&i[t].trigger(e)},void(l.render&&l.render(p,e,n,c)))}function f(i,r,s){var l=i.getModel("iconStyle"),u=r.getIcons?r.getIcons():i.get("icon"),h=i.get("title")||{};if("string"==typeof u){var c=u,d=h;u={},h={},u[s]=c,h[s]=d}var f=i.iconPaths={};o.each(u,function(s,u){var c=a.createIcon(s,{},{x:-g/2,y:-g/2,width:g,height:g});c.setStyle(l.getModel("normal").getItemStyle()),c.hoverStyle=l.getModel("emphasis").getItemStyle(),a.setHoverStyle(c),t.get("showTitle")&&(c.__title=h[u],c.on("mouseover",function(){var t=l.getModel("emphasis").getItemStyle();c.setStyle({text:h[u],textPosition:t.textPosition||"bottom",textFill:t.fill||t.stroke||"#000",textAlign:t.textAlign||"center"})}).on("mouseout",function(){c.setStyle({textFill:null})})),c.trigger(i.get("iconStatus."+u)||"normal"),p.add(c),c.on("click",o.bind(r.onclick,r,e,n,u)),f[u]=c})}var p=this.group;if(p.removeAll(),t.get("show")){var g=+t.get("itemSize"),m=t.get("feature")||{},v=this._features||(this._features={}),x=[];o.each(m,function(t,e){x.push(e)}),new l(this._featureNames||[],x).add(d).update(d).remove(o.curry(d,null)).execute(),this._featureNames=x,u.layout(p,t,n),p.add(u.makeBackground(p.getBoundingRect(),t)),p.eachChild(function(t){var e=t.__title,i=t.hoverStyle;if(i&&e){var r=h.getBoundingRect(e,h.makeFont(i)),o=t.position[0]+p.position[0],a=t.position[1]+p.position[1]+g,s=!1;a+r.height>n.getHeight()&&(i.textPosition="top",s=!0);var l=s?-5-r.height:g+8;o+r.width/2>n.getWidth()?(i.textPosition=["100%",l],i.textAlign="right"):o-r.width/2<0&&(i.textPosition=[0,l],i.textAlign="left")}})}},updateView:function(t,e,n,i){o.each(this._features,function(t){t.updateView&&t.updateView(t.model,e,n,i)})},updateLayout:function(t,e,n,i){o.each(this._features,function(t){t.updateLayout&&t.updateLayout(t.model,e,n,i)})},remove:function(t,e){o.each(this._features,function(n){n.remove&&n.remove(t,e)}),this.group.removeAll()},dispose:function(t,e){o.each(this._features,function(n){n.dispose&&n.dispose(t,e)})}})}).call(e,n(193))},function(t,e,n){function i(t){var e={},n=[],i=[];return t.eachRawSeries(function(t){var r=t.coordinateSystem;if(!r||"cartesian2d"!==r.type&&"polar"!==r.type)n.push(t);else{var o=r.getBaseAxis();if("category"===o.type){var a=o.dim+"_"+o.index;e[a]||(e[a]={categoryAxis:o,valueAxis:r.getOtherAxis(o),series:[]},i.push({axisDim:o.dim,axisIndex:o.index})),e[a].series.push(t)}else n.push(t)}}),{seriesGroupByCategoryAxis:e,other:n,meta:i}}function r(t){var e=[];return p.each(t,function(t,n){var i=t.categoryAxis,r=t.valueAxis,o=r.dim,a=[" "].concat(p.map(t.series,function(t){return t.name})),s=[i.model.getCategories()];p.each(t.series,function(t){s.push(t.getRawData().mapArray(o,function(t){return t}))});for(var l=[a.join(v)],u=0;u<s[0].length;u++){for(var h=[],c=0;c<s.length;c++)h.push(s[c][u]);l.push(h.join(v))}e.push(l.join("\n"))}),e.join("\n\n"+m+"\n\n")}function o(t){return p.map(t,function(t){var e=t.getRawData(),n=[t.name],i=[];return e.each(e.dimensions,function(){for(var t=arguments.length,r=arguments[t-1],o=e.getName(r),a=0;a<t-1;a++)i[a]=arguments[a];n.push((o?o+v:"")+i.join(v))}),n.join("\n")}).join("\n\n"+m+"\n\n")}function a(t){var e=i(t);return{value:p.filter([r(e.seriesGroupByCategoryAxis),o(e.other)],function(t){return t.replace(/[\n\t\s]/g,"")}).join("\n\n"+m+"\n\n"),meta:e.meta}}function s(t){return t.replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function l(t){var e=t.slice(0,t.indexOf("\n"));if(e.indexOf(v)>=0)return!0}function u(t){for(var e=t.split(/\n+/g),n=s(e.shift()).split(x),i=[],r=p.map(n,function(t){return{name:t,data:[]}}),o=0;o<e.length;o++){var a=s(e[o]).split(x);i.push(a.shift());for(var l=0;l<a.length;l++)r[l]&&(r[l].data[o]=a[l])}return{series:r,categories:i}}function h(t){for(var e=t.split(/\n+/g),n=s(e.shift()),i=[],r=0;r<e.length;r++){var o,a=s(e[r]).split(x),l="",u=!1;isNaN(a[0])?(u=!0,l=a[0],a=a.slice(1),i[r]={name:l,value:[]},o=i[r].value):o=i[r]=[];for(var h=0;h<a.length;h++)o.push(+a[h]);1===o.length&&(u?i[r].value=o[0]:i[r]=o[0])}return{name:n,data:i}}function c(t,e){var n=t.split(new RegExp("\n*"+m+"\n*","g")),i={series:[]};return p.each(n,function(t,n){if(l(t)){var r=u(t),o=e[n],a=o.axisDim+"Axis";o&&(i[a]=i[a]||[],i[a][o.axisIndex]={data:r.categories},i.series=i.series.concat(r.series))}else{var r=h(t);i.series.push(r)}}),i}function d(t){this._dom=null,this.model=t}function f(t,e){return p.map(t,function(t,n){var i=e&&e[n];return p.isObject(i)&&!p.isArray(i)?(p.isObject(t)&&!p.isArray(t)&&(t=t.value),p.defaults({value:t},i)):t})}var p=n(1),g=n(21),m=new Array(60).join("-"),v="\t",x=new RegExp("["+v+"]+","g");d.defaultOption={show:!0,readOnly:!1,optionToContent:null,contentToOption:null,icon:"M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28",title:"数据视图",lang:["数据视图","关闭","刷新"],backgroundColor:"#fff",textColor:"#000",textareaColor:"#fff",textareaBorderColor:"#333",buttonColor:"#c23531",buttonTextColor:"#fff"},d.prototype.onclick=function(t,e){function n(){i.removeChild(o),M._dom=null}var i=e.getDom(),r=this.model;this._dom&&i.removeChild(this._dom);var o=document.createElement("div");o.style.cssText="position:absolute;left:5px;top:5px;bottom:5px;right:5px;",o.style.backgroundColor=r.get("backgroundColor")||"#fff";var s=document.createElement("h4"),l=r.get("lang")||[];s.innerHTML=l[0]||r.get("title"),s.style.cssText="margin: 10px 20px;",s.style.color=r.get("textColor");var u=document.createElement("div"),h=document.createElement("textarea");u.style.cssText="display:block;width:100%;overflow:auto;";var d=r.get("optionToContent"),f=r.get("contentToOption"),m=a(t);if("function"==typeof d){var x=d(e.getOption());"string"==typeof x?u.innerHTML=x:p.isDom(x)&&u.appendChild(x)}else u.appendChild(h),h.readOnly=r.get("readOnly"),h.style.cssText="width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;",h.style.color=r.get("textColor"),h.style.borderColor=r.get("textareaBorderColor"),h.style.backgroundColor=r.get("textareaColor"),h.value=m.value;var y=m.meta,_=document.createElement("div");_.style.cssText="position:absolute;bottom:0;left:0;right:0;";var b="float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px",w=document.createElement("div"),S=document.createElement("div");b+=";background-color:"+r.get("buttonColor"),b+=";color:"+r.get("buttonTextColor");var M=this;g.addEventListener(w,"click",n),g.addEventListener(S,"click",function(){var t;try{t="function"==typeof f?f(u,e.getOption()):c(h.value,y)}catch(t){throw n(),new Error("Data view format error "+t)}t&&e.dispatchAction({type:"changeDataView",newOption:t}),n()}),w.innerHTML=l[1],S.innerHTML=l[2],S.style.cssText=b,w.style.cssText=b,!r.get("readOnly")&&_.appendChild(S),_.appendChild(w),g.addEventListener(h,"keydown",function(t){if(9===(t.keyCode||t.which)){var e=this.value,n=this.selectionStart,i=this.selectionEnd;this.value=e.substring(0,n)+v+e.substring(i),this.selectionStart=this.selectionEnd=n+1,g.stop(t)}}),o.appendChild(s),o.appendChild(u),o.appendChild(_),u.style.height=i.clientHeight-80+"px",i.appendChild(o),this._dom=o},d.prototype.remove=function(t,e){this._dom&&e.getDom().removeChild(this._dom)},d.prototype.dispose=function(t,e){this.remove(t,e)},n(29).register("dataView",d),n(2).registerAction({type:"changeDataView",event:"dataViewChanged",update:"prepareAndUpdate"},function(t,e){var n=[];p.each(t.newOption.series,function(t){var i=e.getSeriesByName(t.name)[0];if(i){var r=i.get("data");n.push({name:t.name,data:f(t.data,r)})}else n.push(p.extend({type:"scatter"},t))}),e.mergeOption(p.defaults({series:n},t.newOption))}),t.exports=d},function(t,e,n){"use strict";function i(t,e,n){(this._brushController=new l(n.getZr())).on("brush",s.bind(this._onBrush,this)).mount(),this._isZoomActive}function r(t){var e={};return s.each(["xAxisIndex","yAxisIndex"],function(n){e[n]=t[n],null==e[n]&&(e[n]="all"),(e[n]===!1||"none"===e[n])&&(e[n]=[])}),e}function o(t,e){t.setIconStatus("back",h.count(e)>1?"emphasis":"normal")}function a(t,e,n,i,o){var a=n._isZoomActive;i&&"takeGlobalCursor"===i.type&&(a="dataZoomSelect"===i.key&&i.dataZoomSelectActive),n._isZoomActive=a,t.setIconStatus("zoom",a?"emphasis":"normal");var s=new u(r(t.option),e,{include:["grid"]});n._brushController.setPanels(s.makePanelOpts(o,function(t){return t.xAxisDeclared&&!t.yAxisDeclared?"lineX":!t.xAxisDeclared&&t.yAxisDeclared?"lineY":"rect"})).enableBrush(!!a&&{brushType:"auto",brushStyle:{lineWidth:0,fill:"rgba(0,0,0,0.2)"}})}var s=n(1),l=n(131),u=n(189),h=n(129),c=n(58),d=s.each;n(211);var f="\0_ec_\0toolbox-dataZoom_";i.defaultOption={show:!0,icon:{zoom:"M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",back:"M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"},title:{zoom:"区域缩放",back:"区域缩放还原"}};var p=i.prototype;p.render=function(t,e,n,i){this.model=t,this.ecModel=e,this.api=n,a(t,e,this,i,n),
o(t,e)},p.onclick=function(t,e,n){g[n].call(this)},p.remove=function(t,e){this._brushController.unmount()},p.dispose=function(t,e){this._brushController.dispose()};var g={zoom:function(){var t=!this._isZoomActive;this.api.dispatchAction({type:"takeGlobalCursor",key:"dataZoomSelect",dataZoomSelectActive:t})},back:function(){this._dispatchZoomAction(h.pop(this.ecModel))}};p._onBrush=function(t,e){function n(t,e,n){var r=e.getAxis(t),s=r.model,l=i(t,s,a),u=l.findRepresentativeAxisProxy(s).getMinMaxSpan();null==u.minValueSpan&&null==u.maxValueSpan||(n=c(0,n.slice(),r.scale.getExtent(),0,u.minValueSpan,u.maxValueSpan)),l&&(o[l.id]={dataZoomId:l.id,startValue:n[0],endValue:n[1]})}function i(t,e,n){var i;return n.eachComponent({mainType:"dataZoom",subType:"select"},function(n){var r=n.getAxisModel(t,e.componentIndex);r&&(i=n)}),i}if(e.isEnd&&t.length){var o={},a=this.ecModel;this._brushController.updateCovers([]);var s=new u(r(this.model.option),a,{include:["grid"]});s.matchOutputRanges(t,a,function(t,e,i){if("cartesian2d"===i.type){var r=t.brushType;"rect"===r?(n("x",i,e[0]),n("y",i,e[1])):n({lineX:"x",lineY:"y"}[r],i,e)}}),h.push(a,o),this._dispatchZoomAction(o)}},p._dispatchZoomAction=function(t){var e=[];d(t,function(t,n){e.push(s.clone(t))}),e.length&&this.api.dispatchAction({type:"dataZoom",from:this.uid,batch:e})},n(29).register("dataZoom",i),n(2).registerPreprocessor(function(t){function e(t,e){if(e){var r=t+"Index",o=e[r];null==o||"all"==o||s.isArray(o)||(o=o===!1||"none"===o?[]:[o]),n(t,function(e,n){if(null==o||"all"==o||s.indexOf(o,n)!==-1){var a={type:"select",$fromToolbox:!0,id:f+t+n};a[r]=n,i.push(a)}})}}function n(e,n){var i=t[e];s.isArray(i)||(i=i?[i]:[]),d(i,n)}if(t){var i=t.dataZoom||(t.dataZoom=[]);s.isArray(i)||(t.dataZoom=i=[i]);var r=t.toolbox;if(r&&(s.isArray(r)&&(r=r[0]),r&&r.feature)){var o=r.feature.dataZoom;e("xAxis",o),e("yAxis",o)}}}),t.exports=i},function(t,e,n){"use strict";function i(t){this.model=t}var r=n(1);i.defaultOption={show:!0,type:[],icon:{line:"M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4",bar:"M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7",stack:"M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z",tiled:"M2.3,2.2h22.8V25H2.3V2.2z M35,2.2h22.8V25H35V2.2zM2.3,35h22.8v22.8H2.3V35z M35,35h22.8v22.8H35V35z"},title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"},option:{},seriesIndex:{}};var o=i.prototype;o.getIcons=function(){var t=this.model,e=t.get("icon"),n={};return r.each(t.get("type"),function(t){e[t]&&(n[t]=e[t])}),n};var a={line:function(t,e,n,i){if("bar"===t)return r.merge({id:e,type:"line",data:n.get("data"),stack:n.get("stack"),markPoint:n.get("markPoint"),markLine:n.get("markLine")},i.get("option.line")||{},!0)},bar:function(t,e,n,i){if("line"===t)return r.merge({id:e,type:"bar",data:n.get("data"),stack:n.get("stack"),markPoint:n.get("markPoint"),markLine:n.get("markLine")},i.get("option.bar")||{},!0)},stack:function(t,e,n,i){if("line"===t||"bar"===t)return r.merge({id:e,stack:"__ec_magicType_stack__"},i.get("option.stack")||{},!0)},tiled:function(t,e,n,i){if("line"===t||"bar"===t)return r.merge({id:e,stack:""},i.get("option.tiled")||{},!0)}},s=[["line","bar"],["stack","tiled"]];o.onclick=function(t,e,n){var i=this.model,o=i.get("seriesIndex."+n);if(a[n]){var l={series:[]},u=function(e){var o=e.subType,s=e.id,u=a[n](o,s,e,i);u&&(r.defaults(u,e.option),l.series.push(u));var h=e.coordinateSystem;if(h&&"cartesian2d"===h.type&&("line"===n||"bar"===n)){var c=h.getAxesByScale("ordinal")[0];if(c){var d=c.dim,f=d+"Axis",p=t.queryComponents({mainType:f,index:e.get(name+"Index"),id:e.get(name+"Id")})[0],g=p.componentIndex;l[f]=l[f]||[];for(var m=0;m<=g;m++)l[f][g]=l[f][g]||{};l[f][g].boundaryGap="bar"===n}}};r.each(s,function(t){r.indexOf(t,n)>=0&&r.each(t,function(t){i.setIconStatus(t,"normal")})}),i.setIconStatus(n,"emphasis"),t.eachComponent({mainType:"series",query:null==o?null:{seriesIndex:o}},u),e.dispatchAction({type:"changeMagicType",currentType:n,newOption:l})}};var l=n(2);l.registerAction({type:"changeMagicType",event:"magicTypeChanged",update:"prepareAndUpdate"},function(t,e){e.mergeOption(t.newOption)}),n(29).register("magicType",i),t.exports=i},function(t,e,n){"use strict";function i(t){this.model=t}var r=n(129);i.defaultOption={show:!0,icon:"M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",title:"还原"};var o=i.prototype;o.onclick=function(t,e,n){r.clear(t),e.dispatchAction({type:"restore",from:this.uid})},n(29).register("restore",i),n(2).registerAction({type:"restore",event:"restore",update:"prepareAndUpdate"},function(t,e){e.resetOption("recreate")}),t.exports=i},function(t,e,n){function i(t){this.model=t}var r=n(10);i.defaultOption={show:!0,icon:"M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",title:"保存为图片",type:"png",name:"",excludeComponents:["toolbox"],pixelRatio:1,lang:["右键另存为图片"]},i.prototype.unusable=!r.canvasSupported;var o=i.prototype;o.onclick=function(t,e){var n=this.model,i=n.get("name")||t.get("title.0.text")||"echarts",o=document.createElement("a"),a=n.get("type",!0)||"png";o.download=i+"."+a,o.target="_blank";var s=e.getConnectedDataURL({type:a,backgroundColor:n.get("backgroundColor",!0)||t.get("backgroundColor")||"#fff",excludeComponents:n.get("excludeComponents"),pixelRatio:n.get("pixelRatio")});if(o.href=s,"function"!=typeof MouseEvent||r.browser.ie||r.browser.edge){var l=n.get("lang"),u='<body style="margin:0;"><img src="'+s+'" style="max-width:100%;" title="'+(l&&l[0]||"")+'" /></body>',h=window.open();h.document.write(u)}else{var c=new MouseEvent("click",{view:window,bubbles:!0,cancelable:!1});o.dispatchEvent(c)}},n(29).register("saveAsImage",i),t.exports=i},function(t,e,n){n(57),n(241),n(242),n(2).registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},function(){}),n(2).registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},function(){})},function(t,e,n){function i(t){var e="cubic-bezier(0.23, 1, 0.32, 1)",n="left "+t+"s "+e+",top "+t+"s "+e;return s.map(p,function(t){return t+"transition:"+n}).join(";")}function r(t){var e=[],n=t.get("fontSize"),i=t.getTextColor();return i&&e.push("color:"+i),e.push("font:"+t.getFont()),n&&e.push("line-height:"+Math.round(3*n/2)+"px"),c(["decoration","align"],function(n){var i=t.get(n);i&&e.push("text-"+n+":"+i)}),e.join(";")}function o(t){var e=[],n=t.get("transitionDuration"),o=t.get("backgroundColor"),a=t.getModel("textStyle"),s=t.get("padding");return n&&e.push(i(n)),o&&(f.canvasSupported?e.push("background-Color:"+o):(e.push("background-Color:#"+l.toHex(o)),e.push("filter:alpha(opacity=70)"))),c(["width","color","radius"],function(n){var i="border-"+n,r=d(i),o=t.get(r);null!=o&&e.push(i+":"+o+("color"===n?"":"px"))}),e.push(r(a)),null!=s&&e.push("padding:"+h.normalizeCssArray(s).join("px ")+"px"),e.join(";")+";"}function a(t,e){var n=document.createElement("div"),i=this._zr=e.getZr();this.el=n,this._x=e.getWidth()/2,this._y=e.getHeight()/2,t.appendChild(n),this._container=t,this._show=!1,this._hideTimeout;var r=this;n.onmouseenter=function(){r._enterable&&(clearTimeout(r._hideTimeout),r._show=!0),r._inContent=!0},n.onmousemove=function(e){if(e=e||window.event,!r._enterable){var n=i.handler;u.normalizeEvent(t,e,!0),n.dispatch("mousemove",e)}},n.onmouseleave=function(){r._enterable&&r._show&&r.hideLater(r._hideDelay),r._inContent=!1}}var s=n(1),l=n(22),u=n(21),h=n(7),c=s.each,d=h.toCamelCase,f=n(10),p=["","-webkit-","-moz-","-o-"],g="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;";a.prototype={constructor:a,_enterable:!0,update:function(){var t=this._container,e=t.currentStyle||document.defaultView.getComputedStyle(t),n=t.style;"absolute"!==n.position&&"absolute"!==e.position&&(n.position="relative")},show:function(t){clearTimeout(this._hideTimeout);var e=this.el;e.style.cssText=g+o(t)+";left:"+this._x+"px;top:"+this._y+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",this._show=!0},setContent:function(t){this.el.innerHTML=null==t?"":t},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el;return[t.clientWidth,t.clientHeight]},moveTo:function(t,e){var n,i=this._zr;i&&i.painter&&(n=i.painter.getViewportRootOffset())&&(t+=n.offsetLeft,e+=n.offsetTop);var r=this.el.style;r.left=t+"px",r.top=e+"px",this._x=t,this._y=e},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(s.bind(this.hide,this),t)):this.hide())},isShow:function(){return this._show}},t.exports=a},function(t,e,n){n(2).extendComponentModel({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:8,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}})},function(t,e,n){function i(t){for(var e=t.pop();t.length;){var n=t.pop();n&&(n instanceof x&&(n=n.get("tooltip",!0)),"string"==typeof n&&(n={formatter:n}),e=new x(n,e,e.ecModel))}return e}function r(t,e){return t.dispatchAction||c.bind(e.dispatchAction,e)}function o(t,e,n,i,r,o,a){var l=s(n),u=l.width,h=l.height;return null!=o&&(t+u+o>i?t-=u+o:t+=o),null!=a&&(e+h+a>r?e-=h+a:e+=a),[t,e]}function a(t,e,n,i,r){var o=s(n),a=o.width,l=o.height;return t=Math.min(t+a,i)-a,e=Math.min(e+l,r)-l,t=Math.max(t,0),e=Math.max(e,0),[t,e]}function s(t){var e=t.clientWidth,n=t.clientHeight;if(document.defaultView&&document.defaultView.getComputedStyle){var i=document.defaultView.getComputedStyle(t);i&&(e+=parseInt(i.paddingLeft,10)+parseInt(i.paddingRight,10)+parseInt(i.borderLeftWidth,10)+parseInt(i.borderRightWidth,10),n+=parseInt(i.paddingTop,10)+parseInt(i.paddingBottom,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10))}return{width:e,height:n}}function l(t,e,n){var i=n[0],r=n[1],o=5,a=0,s=0,l=e.width,u=e.height;switch(t){case"inside":a=e.x+l/2-i/2,s=e.y+u/2-r/2;break;case"top":a=e.x+l/2-i/2,s=e.y-r-o;break;case"bottom":a=e.x+l/2-i/2,s=e.y+u+o;break;case"left":a=e.x-i-o,s=e.y+u/2-r/2;break;case"right":a=e.x+l+o,s=e.y+u/2-r/2}return[a,s]}function u(t){return"center"===t||"middle"===t}var h=n(240),c=n(1),d=n(7),f=n(4),p=n(3),g=n(125),m=n(9),v=n(10),x=n(11),y=n(126),_=n(18),b=n(80),w=c.bind,S=c.each,M=f.parsePercent,T=new p.Rect({shape:{x:-1,y:-1,width:2,height:2}});n(2).extendComponentView({type:"tooltip",init:function(t,e){if(!v.node){var n=new h(e.getDom(),e);this._tooltipContent=n}},render:function(t,e,n){if(!v.node){this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=n,this._lastDataByCoordSys=null,this._alwaysShowContent=t.get("alwaysShowContent");var i=this._tooltipContent;i.update(),i.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow()}},_initGlobalListener:function(){var t=this._tooltipModel,e=t.get("triggerOn");y.register("itemTooltip",this._api,w(function(t,n,i){"none"!==e&&(e.indexOf(t)>=0?this._tryShow(n,i):"leave"===t&&this._hide(i))},this))},_keepShow:function(){var t=this._tooltipModel,e=this._ecModel,n=this._api;if(null!=this._lastX&&null!=this._lastY&&"none"!==t.get("triggerOn")){var i=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){i.manuallyShowTip(t,e,n,{x:i._lastX,y:i._lastY})})}},manuallyShowTip:function(t,e,n,i){if(i.from!==this.uid&&!v.node){var o=r(i,n);this._ticket="";var a=i.dataByCoordSys;if(i.tooltip&&null!=i.x&&null!=i.y){var s=T;s.position=[i.x,i.y],s.update(),s.tooltip=i.tooltip,this._tryShow({offsetX:i.x,offsetY:i.y,target:s},o)}else if(a)this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,event:{},dataByCoordSys:i.dataByCoordSys,tooltipOption:i.tooltipOption},o);else if(null!=i.seriesIndex){if(this._manuallyAxisShowTip(t,e,n,i))return;var l=g(i,e),u=l.point[0],h=l.point[1];null!=u&&null!=h&&this._tryShow({offsetX:u,offsetY:h,position:i.position,target:l.el,event:{}},o)}else null!=i.x&&null!=i.y&&(n.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:n.getZr().findHover(i.x,i.y).target,event:{}},o))}},manuallyHideTip:function(t,e,n,i){var o=this._tooltipContent;this._alwaysShowContent||o.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,i.from!==this.uid&&this._hide(r(i,n))},_manuallyAxisShowTip:function(t,e,n,r){var o=r.seriesIndex,a=r.dataIndex,s=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=o&&null!=a&&null!=s){var l=e.getSeriesByIndex(o);if(l){var u=l.getData(),t=i([u.getItemModel(a),l,(l.coordinateSystem||{}).model,t]);if("axis"===t.get("trigger"))return n.dispatchAction({type:"updateAxisPointer",seriesIndex:o,dataIndex:a,position:r.position}),!0}}},_tryShow:function(t,e){var n=t.target,i=this._tooltipModel;if(i){this._lastX=t.offsetX,this._lastY=t.offsetY;var r=t.dataByCoordSys;r&&r.length?this._showAxisTooltip(r,t):n&&null!=n.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(t,n,e)):n&&n.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(t,n,e)):(this._lastDataByCoordSys=null,this._hide(e))}},_showOrMove:function(t,e){var n=t.get("showDelay");e=c.bind(e,this),clearTimeout(this._showTimout),n>0?this._showTimout=setTimeout(e,n):e()},_showAxisTooltip:function(t,e){var n=this._ecModel,r=this._tooltipModel,o=[e.offsetX,e.offsetY],a=[],s=[],l=i([e.tooltipOption,r]);S(t,function(t){S(t.dataByAxis,function(t){var e=n.getComponent(t.axisDim+"Axis",t.axisIndex),i=t.value,r=[];if(e&&null!=i){var o=b.getValueLabel(i,e.axis,n,t.seriesDataIndices,t.valueLabelOpt);c.each(t.seriesDataIndices,function(a){var l=n.getSeriesByIndex(a.seriesIndex),u=a.dataIndexInside,h=l&&l.getDataParams(u);h.axisDim=t.axisDim,h.axisIndex=t.axisIndex,h.axisType=t.axisType,h.axisId=t.axisId,h.axisValue=_.getAxisRawValue(e.axis,i),h.axisValueLabel=o,h&&(s.push(h),r.push(l.formatTooltip(u,!0)))});var l=o;a.push((l?d.encodeHTML(l)+"<br />":"")+r.join("<br />"))}})},this),a.reverse(),a=a.join("<br /><br />");var u=e.position;this._showOrMove(l,function(){this._updateContentNotChangedOnAxis(t)?this._updatePosition(l,u,o[0],o[1],this._tooltipContent,s):this._showTooltipContent(l,a,s,Math.random(),o[0],o[1],u)})},_showSeriesItemTooltip:function(t,e,n){var r=this._ecModel,o=e.seriesIndex,a=r.getSeriesByIndex(o),s=e.dataModel||a,l=e.dataIndex,u=e.dataType,h=s.getData(),c=i([h.getItemModel(l),s,a&&(a.coordinateSystem||{}).model,this._tooltipModel]),d=c.get("trigger");if(null==d||"item"===d){var f=s.getDataParams(l,u),p=s.formatTooltip(l,!1,u),g="item_"+s.name+"_"+l;this._showOrMove(c,function(){this._showTooltipContent(c,p,f,g,t.offsetX,t.offsetY,t.position,t.target)}),n({type:"showTip",dataIndexInside:l,dataIndex:h.getRawIndex(l),seriesIndex:o,from:this.uid})}},_showComponentItemTooltip:function(t,e,n){var i=e.tooltip;if("string"==typeof i){var r=i;i={content:r,formatter:r}}var o=new x(i,this._tooltipModel,this._ecModel),a=o.get("content"),s=Math.random();this._showOrMove(o,function(){this._showTooltipContent(o,a,o.get("formatterParams")||{},s,t.offsetX,t.offsetY,t.position,e)}),n({type:"showTip",from:this.uid})},_showTooltipContent:function(t,e,n,i,r,o,a,s){if(this._ticket="",t.get("showContent")&&t.get("show")){var l=this._tooltipContent,u=t.get("formatter");a=a||t.get("position");var h=e;if(u&&"string"==typeof u)h=d.formatTpl(u,n,!0);else if("function"==typeof u){var c=w(function(e,i){e===this._ticket&&(l.setContent(i),this._updatePosition(t,a,r,o,l,n,s))},this);this._ticket=i,h=u(n,i,c)}l.setContent(h),l.show(t),this._updatePosition(t,a,r,o,l,n,s)}},_updatePosition:function(t,e,n,i,r,s,h){var d=this._api.getWidth(),f=this._api.getHeight();e=e||t.get("position");var p=r.getSize(),g=t.get("align"),v=t.get("verticalAlign"),x=h&&h.getBoundingRect().clone();if(h&&x.applyTransform(h.transform),"function"==typeof e&&(e=e([n,i],s,r.el,x,{viewSize:[d,f],contentSize:p.slice()})),c.isArray(e))n=M(e[0],d),i=M(e[1],f);else if(c.isObject(e)){e.width=p[0],e.height=p[1];var y=m.getLayoutRect(e,{width:d,height:f});n=y.x,i=y.y,g=null,v=null}else if("string"==typeof e&&h){var _=l(e,x,p);n=_[0],i=_[1]}else{var _=o(n,i,r.el,d,f,g?null:20,v?null:20);n=_[0],i=_[1]}if(g&&(n-=u(g)?p[0]/2:"right"===g?p[0]:0),v&&(i-=u(v)?p[1]/2:"bottom"===v?p[1]:0),t.get("confine")){var _=a(n,i,r.el,d,f);n=_[0],i=_[1]}r.moveTo(n,i)},_updateContentNotChangedOnAxis:function(t){var e=this._lastDataByCoordSys,n=!!e&&e.length===t.length;return n&&S(e,function(e,i){var r=e.dataByAxis||{},o=t[i]||{},a=o.dataByAxis||[];n&=r.length===a.length,n&&S(r,function(t,e){var i=a[e]||{},r=t.seriesDataIndices||[],o=i.seriesDataIndices||[];n&=t.value===i.value&&t.axisType===i.axisType&&t.axisId===i.axisId&&r.length===o.length,n&&S(r,function(t,e){var i=o[e];n&=t.seriesIndex===i.seriesIndex&&t.dataIndex===i.dataIndex})})}),this._lastDataByCoordSys=t,!!n},_hide:function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},dispose:function(t,e){v.node||(this._tooltipContent.hide(),y.unregister("itemTooltip",e))}})},,function(t,e,n){function i(t){return parseInt(t,10)}function r(t,e){s.initVML(),this.root=t,this.storage=e;var n=document.createElement("div"),i=document.createElement("div");n.style.cssText="display:inline-block;overflow:hidden;position:relative;width:300px;height:150px;",i.style.cssText="position:absolute;left:0;top:0;",t.appendChild(n),this._vmlRoot=i,this._vmlViewport=n,this.resize();var r=e.delFromStorage,o=e.addToStorage;e.delFromStorage=function(t){r.call(e,t),t&&t.onRemove&&t.onRemove(i)},e.addToStorage=function(t){t.onAdd&&t.onAdd(i),o.call(e,t)},this._firstPaint=!0}function o(t){return function(){a('In IE8.0 VML mode painter not support method "'+t+'"')}}var a=n(54),s=n(187);r.prototype={constructor:r,getViewportRoot:function(){return this._vmlViewport},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(){var t=this.storage.getDisplayList(!0,!0);this._paintList(t)},_paintList:function(t){for(var e=this._vmlRoot,n=0;n<t.length;n++){var i=t[n];i.invisible||i.ignore?(i.__alreadyNotVisible||i.onRemove(e),i.__alreadyNotVisible=!0):(i.__alreadyNotVisible&&i.onAdd(e),i.__alreadyNotVisible=!1,i.__dirty&&(i.beforeBrush&&i.beforeBrush(),(i.brushVML||i.brush).call(i,e),i.afterBrush&&i.afterBrush())),i.__dirty=!1}this._firstPaint&&(this._vmlViewport.appendChild(e),this._firstPaint=!1)},resize:function(t,e){var t=null==t?this._getWidth():t,e=null==e?this._getHeight():e;if(this._width!=t||this._height!=e){this._width=t,this._height=e;var n=this._vmlViewport.style;n.width=t+"px",n.height=e+"px"}},dispose:function(){this.root.innerHTML="",this._vmlRoot=this._vmlViewport=this.storage=null},getWidth:function(){return this._width},getHeight:function(){return this._height},clear:function(){this._vmlViewport&&this.root.removeChild(this._vmlViewport)},_getWidth:function(){var t=this.root,e=t.currentStyle;return(t.clientWidth||i(e.width))-i(e.paddingLeft)-i(e.paddingRight)|0},_getHeight:function(){var t=this.root,e=t.currentStyle;return(t.clientHeight||i(e.height))-i(e.paddingTop)-i(e.paddingBottom)|0}};for(var l=["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],u=0;u<l.length;u++){var h=l[u];r.prototype[h]=o(h)}t.exports=r},function(t,e,n){if(!n(10).canvasSupported){var i=n(6),r=n(12),o=n(27).CMD,a=n(22),s=n(16),l=n(40),u=n(91),h=n(38),c=n(55),d=n(90),f=n(8),p=n(27),g=n(39),m=n(187),v=Math.round,x=Math.sqrt,y=Math.abs,_=Math.cos,b=Math.sin,w=Math.max,S=i.applyTransform,M=",",T="progid:DXImageTransform.Microsoft",I=21600,A=I/2,C=1e5,P=1e3,D=function(t){t.style.cssText="position:absolute;left:0;top:0;width:1px;height:1px;",t.coordsize=I+","+I,t.coordorigin="0,0"},k=function(t){return String(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;")},L=function(t,e,n){return"rgb("+[t,e,n].join(",")+")"},O=function(t,e){e&&t&&e.parentNode!==t&&t.appendChild(e)},z=function(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)},E=function(t,e,n){return(parseFloat(t)||0)*C+(parseFloat(e)||0)*P+n},R=function(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t},N=function(t,e,n){var i=a.parse(e);n=+n,isNaN(n)&&(n=1),i&&(t.color=L(i[0],i[1],i[2]),t.opacity=n*i[3])},B=function(t){var e=a.parse(t);return[L(e[0],e[1],e[2]),e[3]]},V=function(t,e,n){var i=e.fill;if(null!=i)if(i instanceof g){var r,o=0,a=[0,0],s=0,l=1,u=n.getBoundingRect(),h=u.width,c=u.height;if("linear"===i.type){r="gradient";var d=n.transform,f=[i.x*h,i.y*c],p=[i.x2*h,i.y2*c];d&&(S(f,f,d),S(p,p,d));var m=p[0]-f[0],v=p[1]-f[1];o=180*Math.atan2(m,v)/Math.PI,o<0&&(o+=360),o<1e-6&&(o=0)}else{r="gradientradial";var f=[i.x*h,i.y*c],d=n.transform,x=n.scale,y=h,_=c;a=[(f[0]-u.x)/y,(f[1]-u.y)/_],d&&S(f,f,d),y/=x[0]*I,_/=x[1]*I;var b=w(y,_);s=0/b,l=2*i.r/b-s}var M=i.colorStops.slice();M.sort(function(t,e){return t.offset-e.offset});for(var T=M.length,A=[],C=[],P=0;P<T;P++){var D=M[P],k=B(D.color);C.push(D.offset*l+s+" "+k[0]),0!==P&&P!==T-1||A.push(k)}if(T>=2){var L=A[0][0],O=A[1][0],z=A[0][1]*e.opacity,E=A[1][1]*e.opacity;t.type=r,t.method="none",t.focus="100%",t.angle=o,t.color=L,t.color2=O,t.colors=C.join(","),t.opacity=E,t.opacity2=z}"radial"===r&&(t.focusposition=a.join(","))}else N(t,i,e.opacity)},F=function(t,e){null!=e.lineDash&&(t.dashstyle=e.lineDash.join(" ")),null==e.stroke||e.stroke instanceof g||N(t,e.stroke,e.opacity)},G=function(t,e,n,i){var r="fill"==e,o=t.getElementsByTagName(e)[0];null!=n[e]&&"none"!==n[e]&&(r||!r&&n.lineWidth)?(t[r?"filled":"stroked"]="true",n[e]instanceof g&&z(t,o),o||(o=m.createNode(e)),r?V(o,n,i):F(o,n),O(t,o)):(t[r?"filled":"stroked"]="false",z(t,o))},H=[[],[],[]],W=function(t,e){var n,i,r,a,s,l,u=o.M,h=o.C,c=o.L,d=o.A,f=o.Q,p=[];for(a=0;a<t.length;){switch(r=t[a++],i="",n=0,r){case u:i=" m ",n=1,s=t[a++],l=t[a++],H[0][0]=s,H[0][1]=l;break;case c:i=" l ",n=1,s=t[a++],l=t[a++],H[0][0]=s,H[0][1]=l;break;case f:case h:i=" c ",n=3;var g,m,y=t[a++],w=t[a++],T=t[a++],C=t[a++];r===f?(g=T,m=C,T=(T+2*y)/3,C=(C+2*w)/3,y=(s+2*y)/3,w=(l+2*w)/3):(g=t[a++],m=t[a++]),H[0][0]=y,H[0][1]=w,H[1][0]=T,H[1][1]=C,H[2][0]=g,H[2][1]=m,s=g,l=m;break;case d:var P=0,D=0,k=1,L=1,O=0;e&&(P=e[4],D=e[5],k=x(e[0]*e[0]+e[1]*e[1]),L=x(e[2]*e[2]+e[3]*e[3]),O=Math.atan2(-e[1]/L,e[0]/k));var z=t[a++],E=t[a++],R=t[a++],N=t[a++],B=t[a++]+O,V=t[a++]+B+O;a++;var F=t[a++],G=z+_(B)*R,W=E+b(B)*N,y=z+_(V)*R,w=E+b(V)*N,Z=F?" wa ":" at ";Math.abs(G-y)<1e-4&&(Math.abs(V-B)>.01?F&&(G+=270/I):Math.abs(W-E)<1e-4?F&&G<z||!F&&G>z?w-=270/I:w+=270/I:F&&W<E||!F&&W>E?y+=270/I:y-=270/I),p.push(Z,v(((z-R)*k+P)*I-A),M,v(((E-N)*L+D)*I-A),M,v(((z+R)*k+P)*I-A),M,v(((E+N)*L+D)*I-A),M,v((G*k+P)*I-A),M,v((W*L+D)*I-A),M,v((y*k+P)*I-A),M,v((w*L+D)*I-A)),s=y,l=w;break;case o.R:var q=H[0],j=H[1];q[0]=t[a++],q[1]=t[a++],j[0]=q[0]+t[a++],j[1]=q[1]+t[a++],e&&(S(q,q,e),S(j,j,e)),q[0]=v(q[0]*I-A),j[0]=v(j[0]*I-A),q[1]=v(q[1]*I-A),j[1]=v(j[1]*I-A),p.push(" m ",q[0],M,q[1]," l ",j[0],M,q[1]," l ",j[0],M,j[1]," l ",q[0],M,j[1]);break;case o.Z:p.push(" x ")}if(n>0){p.push(i);for(var X=0;X<n;X++){var Y=H[X];e&&S(Y,Y,e),p.push(v(Y[0]*I-A),M,v(Y[1]*I-A),X<n-1?M:"")}}}return p.join("")};f.prototype.brushVML=function(t){var e=this.style,n=this._vmlEl;n||(n=m.createNode("shape"),D(n),this._vmlEl=n),G(n,"fill",e,this),G(n,"stroke",e,this);var i=this.transform,r=null!=i,o=n.getElementsByTagName("stroke")[0];if(o){var a=e.lineWidth;if(r&&!e.strokeNoScale){var s=i[0]*i[3]-i[1]*i[2];a*=x(y(s))}o.weight=a+"px"}var l=this.path||(this.path=new p);this.__dirtyPath&&(l.beginPath(),this.buildPath(l,this.shape),l.toStatic(),this.__dirtyPath=!1),n.path=W(l.data,this.transform),n.style.zIndex=E(this.zlevel,this.z,this.z2),O(t,n),null!=e.text?this.drawRectText(t,this.getBoundingRect()):this.removeRectText(t)},f.prototype.onRemove=function(t){z(t,this._vmlEl),this.removeRectText(t)},f.prototype.onAdd=function(t){O(t,this._vmlEl),this.appendRectText(t)};var Z=function(t){return"object"==typeof t&&t.tagName&&"IMG"===t.tagName.toUpperCase()};c.prototype.brushVML=function(t){var e,n,i=this.style,r=i.image;if(Z(r)){var o=r.src;if(o===this._imageSrc)e=this._imageWidth,n=this._imageHeight;else{var a=r.runtimeStyle,s=a.width,l=a.height;a.width="auto",a.height="auto",e=r.width,n=r.height,a.width=s,a.height=l,this._imageSrc=o,this._imageWidth=e,this._imageHeight=n}r=o}else r===this._imageSrc&&(e=this._imageWidth,n=this._imageHeight);if(r){var u=i.x||0,h=i.y||0,c=i.width,d=i.height,f=i.sWidth,p=i.sHeight,g=i.sx||0,y=i.sy||0,_=f&&p,b=this._vmlEl;b||(b=m.doc.createElement("div"),D(b),this._vmlEl=b);var I,A=b.style,C=!1,P=1,k=1;if(this.transform&&(I=this.transform,P=x(I[0]*I[0]+I[1]*I[1]),k=x(I[2]*I[2]+I[3]*I[3]),C=I[1]||I[2]),C){var L=[u,h],z=[u+c,h],R=[u,h+d],N=[u+c,h+d];S(L,L,I),S(z,z,I),S(R,R,I),S(N,N,I);var B=w(L[0],z[0],R[0],N[0]),V=w(L[1],z[1],R[1],N[1]),F=[];F.push("M11=",I[0]/P,M,"M12=",I[2]/k,M,"M21=",I[1]/P,M,"M22=",I[3]/k,M,"Dx=",v(u*P+I[4]),M,"Dy=",v(h*k+I[5])),A.padding="0 "+v(B)+"px "+v(V)+"px 0",A.filter=T+".Matrix("+F.join("")+", SizingMethod=clip)"}else I&&(u=u*P+I[4],h=h*k+I[5]),A.filter="",A.left=v(u)+"px",A.top=v(h)+"px";var G=this._imageEl,H=this._cropEl;G||(G=m.doc.createElement("div"),this._imageEl=G);var W=G.style;if(_){if(e&&n)W.width=v(P*e*c/f)+"px",W.height=v(k*n*d/p)+"px";else{var q=new Image,j=this;q.onload=function(){q.onload=null,e=q.width,n=q.height,W.width=v(P*e*c/f)+"px",W.height=v(k*n*d/p)+"px",j._imageWidth=e,j._imageHeight=n,j._imageSrc=r},q.src=r}H||(H=m.doc.createElement("div"),H.style.overflow="hidden",this._cropEl=H);var X=H.style;X.width=v((c+g*c/f)*P),X.height=v((d+y*d/p)*k),X.filter=T+".Matrix(Dx="+-g*c/f*P+",Dy="+-y*d/p*k+")",H.parentNode||b.appendChild(H),G.parentNode!=H&&H.appendChild(G)}else W.width=v(P*c)+"px",W.height=v(k*d)+"px",b.appendChild(G),H&&H.parentNode&&(b.removeChild(H),this._cropEl=null);var Y="",U=i.opacity;U<1&&(Y+=".Alpha(opacity="+v(100*U)+") "),Y+=T+".AlphaImageLoader(src="+r+", SizingMethod=scale)",W.filter=Y,b.style.zIndex=E(this.zlevel,this.z,this.z2),O(t,b),null!=i.text&&this.drawRectText(t,this.getBoundingRect())}},c.prototype.onRemove=function(t){z(t,this._vmlEl),this._vmlEl=null,this._cropEl=null,this._imageEl=null,this.removeRectText(t)},c.prototype.onAdd=function(t){O(t,this._vmlEl),this.appendRectText(t)};var q,j="normal",X={},Y=0,U=100,$=document.createElement("div"),K=function(t){var e=X[t];if(!e){Y>U&&(Y=0,X={});var n,i=$.style;try{i.font=t,n=i.fontFamily.split(",")[0]}catch(t){}e={style:i.fontStyle||j,variant:i.fontVariant||j,weight:i.fontWeight||j,size:0|parseFloat(i.fontSize||12),family:n||"Microsoft YaHei"},X[t]=e,Y++}return e};s.measureText=function(t,e){var n=m.doc;q||(q=n.createElement("div"),q.style.cssText="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;",m.doc.body.appendChild(q));try{q.style.font=e}catch(t){}return q.innerHTML="",q.appendChild(n.createTextNode(t)),{width:q.offsetWidth}};for(var Q=new r,J=function(t,e,n,i){var r=this.style;this.__dirty&&l.normalizeTextStyle(r,!0);var o=r.text;if(null!=o&&(o+=""),o){if(r.rich){var a=s.parseRichText(o,r);o=[];for(var u=0;u<a.lines.length;u++){for(var h=a.lines[u].tokens,c=[],d=0;d<h.length;d++)c.push(h[d].text);o.push(c.join(""))}o=o.join("\n")}var f,p,g=r.textAlign,x=r.textVerticalAlign,y=K(r.font),_=y.style+" "+y.variant+" "+y.weight+" "+y.size+'px "'+y.family+'"';n=n||s.getBoundingRect(o,_,g,x);var b=this.transform;if(b&&!i&&(Q.copy(e),Q.applyTransform(b),e=Q),i)f=e.x,p=e.y;else{var w=r.textPosition,T=r.textDistance;if(w instanceof Array)f=e.x+R(w[0],e.width),p=e.y+R(w[1],e.height),g=g||"left";else{var I=s.adjustTextPositionOnRect(w,e,T);f=I.x,p=I.y,g=g||I.textAlign,x=x||I.textVerticalAlign}}f=s.adjustTextX(f,n.width,g),p=s.adjustTextY(p,n.height,x),p+=n.height/2;var A,C,P,L=m.createNode,z=this._textVmlEl;z?(P=z.firstChild,A=P.nextSibling,C=A.nextSibling):(z=L("line"),A=L("path"),C=L("textpath"),P=L("skew"),C.style["v-text-align"]="left",D(z),A.textpathok=!0,C.on=!0,z.from="0 0",z.to="1000 0.05",O(z,P),O(z,A),O(z,C),this._textVmlEl=z);var N=[f,p],B=z.style;b&&i?(S(N,N,b),P.on=!0,P.matrix=b[0].toFixed(3)+M+b[2].toFixed(3)+M+b[1].toFixed(3)+M+b[3].toFixed(3)+",0,0",P.offset=(v(N[0])||0)+","+(v(N[1])||0),P.origin="0 0",B.left="0px",B.top="0px"):(P.on=!1,B.left=v(f)+"px",B.top=v(p)+"px"),C.string=k(o);try{C.style.font=_}catch(t){}G(z,"fill",{fill:r.textFill,opacity:r.opacity},this),G(z,"stroke",{stroke:r.textStroke,opacity:r.opacity,lineDash:r.lineDash},this),z.style.zIndex=E(this.zlevel,this.z,this.z2),O(t,z)}},tt=function(t){z(t,this._textVmlEl),this._textVmlEl=null},et=function(t){O(t,this._textVmlEl)},nt=[u,h,c,f,d],it=0;it<nt.length;it++){var rt=nt[it].prototype;rt.drawRectText=J,rt.removeRectText=tt,rt.appendRectText=et}d.prototype.brushVML=function(t){var e=this.style;null!=e.text?this.drawRectText(t,{x:e.x||0,y:e.y||0,width:0,height:0},this.getBoundingRect(),!0):this.removeRectText(t)},d.prototype.onRemove=function(t){this.removeRectText(t)},d.prototype.onAdd=function(t){this.appendRectText(t)}}},function(t,e,n){n(245),n(92).registerPainter("vml",n(244))}])});