package net.bioknow.cdtms.studyClose;

import net.bioknow.webplug.configdb.DAOConfigDB;

import java.util.List;
import java.util.Map;

public class DAOstudyCloseIntegrate {

    protected final static String xmlpath = "/cdtms/studyclose/configform.xml";

    protected final static String debug = "debug";

    protected final static String sn = "sn";
    protected final static String tableid = "tableid";
    protected final static String AuthorizedRole = "AuthorizedRole";

    public static List<Map<String, String>> listRule(String projectId) throws Exception {
        DAOConfigDB dao = new DAOConfigDB(projectId, DAOstudyCloseIntegrate.xmlpath);
        return dao.list();
    }

    protected static List<Map<String, String>> listRule(String projectId, String sn_no) throws Exception {
        DAOConfigDB dao = new DAOConfigDB(projectId, DAOstudyCloseIntegrate.xmlpath);

        return dao.listByTable(sn_no, sn);
    }

}
