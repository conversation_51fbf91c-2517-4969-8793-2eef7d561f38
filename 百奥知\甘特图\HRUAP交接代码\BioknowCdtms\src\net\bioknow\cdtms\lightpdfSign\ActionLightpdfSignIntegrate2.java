package net.bioknow.cdtms.lightpdfSign;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.itextpdf.text.pdf.PdfReader;
import net.bioknow.cdtms.formMail.DAOTransemail;
import net.bioknow.dbplug.wordreport.DAOWordreport;
import net.bioknow.dbplug.wordreport.UtilAsposeword;
import net.bioknow.mvc.RootAction;
import net.bioknow.mvc.tools.WebPath;
import net.bioknow.passport.datamng.DAOPPData;
import net.bioknow.passport.datamng.PassportCacheUtil;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.schemaplug.attach.AttachDAO;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.uapplug.usersyn.CNT_Usersyn;
import net.bioknow.uapplug.usersyn.DAOUsersyn;
import net.bioknow.webutil.fileup.DAOFileup;
import net.bioknow.webutil.session.SessInfo;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static net.bioknow.cdtms.lightpdfSign.PdfHelper.getKeyWords;
import static net.bioknow.cdtms.lightpdfSign.ResponseUtils.*;
import static net.bioknow.cdtms.lightpdfSign.WaterMarkUtils.PDFAddWatermark;


public class ActionLightpdfSignIntegrate2 extends RootAction {


    public void checkIsLogin(HttpServletRequest request, HttpServletResponse response) {
        try {


            String taskId = request.getParameter("task_id");
            String email = request.getParameter("email");
            String system = request.getParameter("system");

            List<SessInfo> sessInfoList = SessUtil.listLoginedSessInfo(system);

            String emaildecode = new String(Base64.getDecoder().decode(email));

            DAODataMng daoDataMng = new DAODataMng(system);
            Map<String, String> currUserInfoMap = new HashMap<>();
            request.setAttribute("currUserInfoMap",currUserInfoMap);
            DAOTransemail daoTransemail = new DAOTransemail(system);
            Thread emailThread = new Thread(daoTransemail);
            emailThread.start();

            List<Map> esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.sign_flow_id='" + taskId + "'", null, 1);


            if (CollectionUtils.isEmpty(esignInstanceList)) {

                currUserInfoMap.put("status","-1");
                currUserInfoMap.put("msg","Task Not Found");
                this.forward(request, response, "Verify");
                return;
            }



            currUserInfoMap.put("title", (String) esignInstanceList.get(0).get("subject"));
            currUserInfoMap.put("id", String.valueOf(esignInstanceList.get(0).get("id")) );
            currUserInfoMap.put("taskId", taskId);
            currUserInfoMap.put("email",emaildecode);
            currUserInfoMap.put("system",system);
            Map curresignInstanceMap = esignInstanceList.get(0);
            SimpleDateFormat sdf = new SimpleDateFormat("MMMM d, yyyy");

            String esignInstanceStatus = (String) curresignInstanceMap.get("status");
            currUserInfoMap.put("status",esignInstanceStatus);
            Date esignInitiatDate = (Date) curresignInstanceMap.get("esign_begin_date");
            String esignInitiatDateStr = sdf.format(esignInitiatDate);
            currUserInfoMap.put("notes", "Created by "+curresignInstanceMap.get("initiator")+" on "+esignInitiatDateStr);
            List<Map> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id='" + curresignInstanceMap.get("id") + "' and  obj.user_code='" + emaildecode + "'", null, 1);
            if (CollectionUtils.isEmpty(esignSignerList)) {
                currUserInfoMap.put("status","-1");
                currUserInfoMap.put("msg","You are not a signer of the current task.");
                this.forward(request, response, "Verify");
                return;
            }
            Map currSignerMap = esignSignerList.get(0);


            String esignEndDate = ObjectUtils.isNotEmpty(curresignInstanceMap.get("esign_end_date"))?sdf.format((Date)curresignInstanceMap.get("esign_end_date")):"";
            String esignExpireData = ObjectUtils.isNotEmpty(curresignInstanceMap.get("sign_expire_data"))?sdf.format((Date)curresignInstanceMap.get("sign_expire_data")):"";
            String esignExecuteDate = ObjectUtils.isNotEmpty(currSignerMap.get("execute_date"))?sdf.format((Date) currSignerMap.get("execute_date")):"";

            String signMsg = null;
            if(StringUtils.isNotEmpty(esignExecuteDate)){
                signMsg=",Your signing time is "+esignExecuteDate;
            }else {
                signMsg=",You have not signed.";
            }

            String msg = null;

            if(StringUtils.equals(esignInstanceStatus,"2")){
                msg="The task has been signed and completed on "+esignEndDate+signMsg;
                currUserInfoMap.put("msg",msg);

                this.forward(request, response, "Verify");
                return;

            }

            if(StringUtils.equals(esignInstanceStatus,"3")){
                msg="The task expired on "+esignExpireData+", and the signing has been terminated.";
                currUserInfoMap.put("msg",msg);

                this.forward(request, response, "Verify");
                return;
            }


            String cancelor = (String) curresignInstanceMap.get("cancelor");
            String cancelCause = (String) curresignInstanceMap.get("cancel_cause");


            if(StringUtils.equals(esignInstanceStatus,"5")){
                msg="The task was initiated to terminate signing by "+cancelor+" due to "+cancelCause+" on "+esignEndDate+",and the signing has been terminated."+signMsg;
                currUserInfoMap.put("msg",msg);

                this.forward(request, response, "Verify");
                return;
            }


            String esignSigerStatus = (String) currSignerMap.get("status");
            if (StringUtils.equals(esignSigerStatus,"2")) {
                msg="The task you have signed has been completed on "+esignExecuteDate;
                currUserInfoMap.put("msg",msg);

                this.forward(request, response, "Verify");
                return;
            }

            String remoteAddr = request.getRemoteAddr();
            boolean sessExists = sessInfoList.stream()
                    .anyMatch(sessInfo ->
                            StringUtils.equals(sessInfo.getUserloginid(), emaildecode) &&
                                    StringUtils.equals(sessInfo.getIp(), remoteAddr)
                    );



            if (sessExists) {
                currUserInfoMap.put("verification_code", (String) esignSignerList.get(0).get("verification_code"));
                this.forward(request, response, "Verify");
                return;
            }




            this.forward(request, response, "Verify");
            return;



        } catch (IOException ex) {
            throw new RuntimeException(ex);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }


    }



    public void signCreate(HttpServletRequest request, HttpServletResponse response) {
            this.forward(request, response, "signCreate");
            return;
    }



    public void ajaxCaneclPage(HttpServletRequest request, HttpServletResponse response) {

        request.setAttribute("id",request.getParameter("id"));
        this.forward(request, response, "ajaxCaneclPage");
        return;
    }

    public void createCheck(HttpServletRequest request, HttpServletResponse response) {
        try {


            String requestBody = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();
            Map<String, String> requestDataMap = gson.fromJson(requestBody, new TypeToken<Map<String, String>>() {
            }.getType());
            String recipients = requestDataMap.get("recipients");
            String subject = requestDataMap.get("subject");
            String signFlowExpireTimeStr = requestDataMap.get("signFlowExpireTime");
            String signpage = requestDataMap.get("signpage");
            String signPageFile = requestDataMap.get("signPageFile");
//            String studyid = requestDataMap.get("studyid");
//            Long studyid =null;
            Long studyid =null;


            if (ObjectUtils.isNotEmpty(requestDataMap.get("studyid"))) {
                studyid=Long.valueOf(requestDataMap.get("studyid"));
            }

            String body = requestDataMap.get("body");


            List<Map<String, String>> recipientList = new ArrayList<>();
            String[] recipientsArr = recipients.split(";");
            for (String recipient : recipientsArr) {
                String[] recipientArr = recipient.trim().split("[<>]");



                String name = recipientArr[0].trim();
                String email =recipientArr.length>1? recipientArr[1].trim():recipientArr[0].trim();
                Map<String, String> recipienMap = new HashMap<>();
                recipienMap.put("name",  name);
                recipienMap.put("value", email);
                recipientList.add(recipienMap);
            }

            String projectId = SessUtil.getSessInfo().getProjectid();
            DAOFileup fdao = new DAOFileup();
            AttachDAO attachDAO = new AttachDAO(projectId);
            File signpageFileLocal;
            String signpageFileName;
            if (StringUtils.isEmpty(signPageFile)) {
            List signpageList = fdao.parseToFile(signpage);
            List<String> signpageFileNameList = fdao.parseToFileName(signpage);
            File signpageFile = (File) signpageList.get(0);
             signpageFileName = (String) signpageFileNameList.get(0);

                int fileNameLastDotIndex = signpageFileName.lastIndexOf(".");
                signpageFileName = signpageFileName.substring(0, fileNameLastDotIndex) + ".pdf";

                boolean signpageFileIspdf = signpageFile.getName().toLowerCase().endsWith(".pdf");
            String signFilePathFullOutURI = null;
            if (!signpageFileIspdf) {
                String signFilePathFullURI = signpageFile.getAbsolutePath();
                int lastDotIndex = signFilePathFullURI.lastIndexOf(".");
                signFilePathFullOutURI = signFilePathFullURI.substring(0, lastDotIndex) + ".pdf";
                UtilAsposeword.word2pdf(signFilePathFullURI, signFilePathFullOutURI);

            }

            signpageFileLocal = new File(signpageFileIspdf ? signpageFile.getAbsolutePath() : signFilePathFullOutURI);

            }else {
                signpageFileName = signPageFile.split("\\|")[0].split("\\*")[0];
                String signedFileId = signPageFile.split("\\|")[0].split("\\*")[1];
                signpageFileLocal = attachDAO.getFile(signedFileId, "esign_file");
            }

            List<Map<String, String>> listR = DAOLightpdfSignIntegrate.listRule(projectId, "1");
            String esignUrl = listR.get(0).get(DAOLightpdfSignIntegrate.esignUrl);
            String uploadFileMsgJson = LightpdfSignIntegrateUtil.uploadFile(esignUrl + "/api/files/upload", signpageFileLocal.getPath(), signpageFileName.replace(" ", ""));




            Map uploadFileMsgMap = gson.fromJson(uploadFileMsgJson, new TypeToken<Map<String, Object>>() {
            }.getType());
            if (!StringUtils.equals(String.valueOf(uploadFileMsgMap.get("status")), "200")) {
                response.getOutputStream().write(String.valueOf(uploadFileMsgMap.get("message")).getBytes("UTF-8"));
                response.getOutputStream().close();
                return;
            }
            Map<String,Object> esignInstanceMap = new HashMap();
            esignInstanceMap.put("subject",subject);
            esignInstanceMap.put("signFlowExpireTime",signFlowExpireTimeStr);

            Map uploadFileDataMap = (Map) uploadFileMsgMap.get("data");
            List uploadFileDataList = (List) uploadFileDataMap.get("items");
            Map uploadFileData0Map = (Map) uploadFileDataList.get(0);
            String eSignfileKey = (String) uploadFileData0Map.get("file_id");
            List<Map<String, String>> DAOLightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectId, "1");
            Map<String, String> DAOLightpdfSignIntegrateMap = DAOLightpdfSignIntegrateList.get(0);

            ArrayList<Map> signerInfosList = getSignerInfosList(signpageFileLocal, recipientList);
            String CNSMsg = LightpdfSignIntegrateUtil.eSignRegister(projectId,eSignfileKey, esignInstanceMap, signerInfosList, DAOLightpdfSignIntegrateMap);


            Map CNSMsgMap = gson.fromJson(CNSMsg, new TypeToken<Map<String, Object>>() {
            }.getType());

            if (!StringUtils.equals(String.valueOf(CNSMsgMap.get("status")), "200")) {
                String message = (String) CNSMsgMap.get("message");
                response.getOutputStream().write(message.getBytes("UTF-8"));
                response.getOutputStream().close();
                return;

            }


            Map GUUDataMap = (Map) CNSMsgMap.get("data");
            String signFlowId = (String) GUUDataMap.get("task_id");


            Map esignInstanceToSaveMap = new HashMap();
            esignInstanceToSaveMap.put("active", "1");
            esignInstanceToSaveMap.put("sign_flow_id", signFlowId);
            esignInstanceToSaveMap.put("status", "1");
            esignInstanceToSaveMap.put("esign_msg", CNSMsg);
            DAODataMng daoDataMng = new DAODataMng(projectId);

            String signFlowExpireTime = (String) esignInstanceMap.get("signFlowExpireTime");
            SimpleDateFormat DateFormatYMDHMS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date signFlowExpireDate = DateFormatYMDHMS.parse(signFlowExpireTime);

            Map currentUserMap = SessUtil.getSessInfo().getUser();

            esignInstanceToSaveMap.put("remark", esignInstanceMap.get("remark"));
            esignInstanceToSaveMap.put("subject", esignInstanceMap.get("subject"));
            esignInstanceToSaveMap.put("sign_expire_data", signFlowExpireDate);
            esignInstanceToSaveMap.put("initiator", currentUserMap.get("username"));
            esignInstanceToSaveMap.put("esign_begin_date", new Date());
            esignInstanceToSaveMap.put("study_id", studyid);
            esignInstanceToSaveMap.put("file_name", signpageFileName);
            daoDataMng.save("esign_instance", esignInstanceToSaveMap);

            ArrayList<Map> signUrlInfosToSaveList = new ArrayList<>();
            for (Map signUrlInfosMap : signerInfosList) {
                Map signUrlInfosToSaveMap = new HashMap<>();
                signUrlInfosToSaveMap.put("name", signUrlInfosMap.get("name"));
                signUrlInfosToSaveMap.put("signer_id", signUrlInfosMap.get("signer_id"));

                String email = (String) signUrlInfosMap.get("email");
                signUrlInfosToSaveMap.put("user_code", new String(Base64.getDecoder().decode(email.getBytes())));
                signUrlInfosToSaveMap.put("verification_code", signUrlInfosMap.get("verification_code"));
                signUrlInfosToSaveMap.put("esign_url", signUrlInfosMap.get("signUrlShort"));
                signUrlInfosToSaveMap.put("esign_instance_id", esignInstanceToSaveMap.get("id"));
                signUrlInfosToSaveMap.put("status", "0");
                signUrlInfosToSaveList.add(signUrlInfosToSaveMap);
//				daoDataMng.save("esign_signer",signUrlInfosToSaveMap );
//				daoDataMng.saveBatch("esign_signer", (List) signUrlInfosToSaveList, 2L,null);


            }

            Long userid = Long.valueOf(SessUtil.getSessInfo().getUserid());

            daoDataMng.saveBatch("esign_signer", (List) signUrlInfosToSaveList, userid, null);


            Map<Object, Object> esignFileMap = new HashMap<>();


            esignFileMap.put("esign_file_key", eSignfileKey);
            String fileLocalUuid = attachDAO.saveFile(signpageFileLocal, "esign_file");
            String strAttachToSave = "" + signpageFileName + "*" + fileLocalUuid + "|";
            esignFileMap.put("esign_file_key", eSignfileKey);
            esignFileMap.put("file", strAttachToSave);


            esignFileMap.put("esign_instance_id", esignInstanceToSaveMap.get("id"));

            daoDataMng.save("esign_file", esignFileMap);


            Map esignLogToSaveMap = new HashMap<>();
            esignLogToSaveMap.put("esign_instance_id", esignInstanceToSaveMap.get("id"));
            esignLogToSaveMap.put("esign_status", "0");
//            esignLogToSaveMap.put("msg", ReceiveMsg);
//            esignLogToSaveMap.put("esign_desc", callBackDesc);
            esignLogToSaveMap.put("execute_date", new Date());
            esignLogToSaveMap.put("name", currentUserMap.get("username"));


            daoDataMng.save("esign_log", esignLogToSaveMap);
            Map<Object, Object> responseData = new HashMap<>();

            responseData.put("redirectUrl","/tableapp.edit.do?tableid=esign_instance&id="+esignInstanceToSaveMap.get("id"));
            ResponseUtils.response(response,HttpStatus.OK,responseData);


            String CDTMSUrl = DAOLightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.CDTMSUrl);



            for (Map signerInfoMap : signerInfosList) {
                DAOTransemail daoTransemail = new DAOTransemail(projectId);

                String email = (String) signerInfoMap.get("email");

                String content = "\"" + signpageFileName.replace(".pdf","") + "\" need to be signed." +

                        "   <a href=\"" + CDTMSUrl + "/lightpdfSign.checkIsLogin.do?system=" + projectId + "&task_id=" + signFlowId + "&email=" +
                        email
                        + "\" title=\"去签署\">Go to sign.</a><br>" +
                        "Signing ends on：" + signFlowExpireTime + "<br>" +
                        "Your signing verification code is：" + signerInfoMap.get("verification_code")+"，<a href=\"https://cdtms.hengrui.com/webutil/js/pdfjs/web/viewer.show.do?file=/%E7%BA%BF%E4%B8%8A%E7%AD%BE%E5%90%8D%E5%8A%9F%E8%83%BD%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97_20231101.pdf&amp;unloadstr=\" target=\"_blank\" style=\"font-weight: bold;color: red;\">Signature operation guide</a>";
                content=body+content;
                daoTransemail.sendmail("《" + signpageFileName.replace(".pdf","") + "》 need to be signed!", content, new String(Base64.getDecoder().decode(email.getBytes())));

            }

            return;




        } catch (Exception e) {
            Log.error("err",e);
        }


    }

    public void signerSearch(HttpServletRequest request, HttpServletResponse response) {
        try {


            String term = request.getParameter("q");
            String page = request.getParameter("page");

            String projectid  = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectid);
            DAOUsersyn daoUsersyn = new DAOUsersyn(projectid);

            List<Map<String, String>> daoUsersynRoleList = daoUsersyn.listRule();
            Map<String, String> daoUsersynRoleMap = daoUsersynRoleList.get(0);
            String accountTableId = daoUsersynRoleMap.get(CNT_Usersyn.tableid);
            String nameFileId = daoUsersynRoleMap.get(CNT_Usersyn.field_name);
            String emailFileId = daoUsersynRoleMap.get(CNT_Usersyn.field_email);

            String currUserid = SessUtil.getSessInfo().getUserid();
            List<Map<String, Object>> options = new ArrayList<>();
            List<Map> signerList = daoDataMng.listRecord(accountTableId, "obj."+emailFileId+" like '%" + term + "%' or obj."+nameFileId+" like '%" + term + "%'", null, 10, StringUtils.isNotEmpty(page)? Integer.parseInt(page) :1);

            if (CollectionUtils.isEmpty(signerList)) {

                emailFileId="user_code";
                nameFileId="name";
                signerList = daoDataMng.listRecord("esign_signer", "obj.userid="+currUserid+" and obj.user_code like '%" + term + "%' or obj.name like '%" + term + "%'", null, 10, StringUtils.isNotEmpty(page)? Integer.parseInt(page) :1);

            }

            // Create the main JSON map

            for (Map signerMap : signerList) {
                Map<String, Object> option = new HashMap<>();
                option.put("id", signerMap.get(nameFileId)+"<"+signerMap.get(emailFileId)+">");
                option.put("text", signerMap.get(nameFileId)+"<"+signerMap.get(emailFileId)+">");
                options.add(option);
            }



            // Create the main JSON map
            Map<String, Object> jsonMap = new HashMap<>();
            jsonMap.put("results", options);

            Map<String, Object> pagination = new HashMap<>();
            pagination.put("more", true);
            jsonMap.put("pagination", pagination);


            ResponseUtils.response(response,HttpStatus.OK,jsonMap);
//            daoDataMng.listRecord("ryjbzlb")
//            AttachDAO attdao = new AttachDAO(projectid);
//            DAOFileup fdao = new DAOFileup();
//            List listFile = fdao.parseToFile(inputfile);
////            if(listFile.size()==0) return lang.get("请选择授权文件！");
//            fdao.parseToFileName(inputfile);
//            File file = (File) listFile.get(0);


        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    }


    public void studySearch(HttpServletRequest request, HttpServletResponse response) {
        try {


            String term = request.getParameter("q");
            String page = request.getParameter("page");

            String projectid  = SessUtil.getSessInfo().getProjectid();

            List<Map<String, String>> LightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectid);
            Map<String, String> LightpdfSignIntegrateMap = LightpdfSignIntegrateList.get(0);
            String studyTableid = LightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.studyTableid);
            String studyUserTableid = LightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.studyUserTableid);
            String studyTableStudyFiledid = LightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.studyTableStudyFiledid);
            List<Map<String, Object>> options = new ArrayList<>();
            DAODataMng daoDataMng = new DAODataMng(projectid);
            List<Map> signerList = daoDataMng.listRecord(studyTableid, "obj."+studyTableStudyFiledid+" like '%" + term + "%'", null, 10, StringUtils.isNotEmpty(page)? Integer.parseInt(page) :1);


            // Create the main JSON map

            for (Map signerMap : signerList) {
                Map<String, Object> option = new HashMap<>();
                option.put("id", signerMap.get("id"));
                option.put("text", signerMap.get(studyTableStudyFiledid));
                options.add(option);
            }



            // Create the main JSON map
            Map<String, Object> jsonMap = new HashMap<>();
            jsonMap.put("results", options);

            Map<String, Object> pagination = new HashMap<>();
            pagination.put("more", true);
            jsonMap.put("pagination", pagination);


            ResponseUtils.response(response,HttpStatus.OK,jsonMap);
//            daoDataMng.listRecord("ryjbzlb")
//            AttachDAO attdao = new AttachDAO(projectid);
//            DAOFileup fdao = new DAOFileup();
//            List listFile = fdao.parseToFile(inputfile);
////            if(listFile.size()==0) return lang.get("请选择授权文件！");
//            fdao.parseToFileName(inputfile);
//            File file = (File) listFile.get(0);


        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    }

    public void idVerify(HttpServletRequest request, HttpServletResponse response) {
        try {


            String requestBody = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));


            Gson gson = new Gson();
            Map<String, String> requestDataMap = gson.fromJson(requestBody, new TypeToken<Map<String, String>>(){}.getType());

            String id = requestDataMap.get("id");
            String taskId = requestDataMap.get("taskId");
            String email = requestDataMap.get("email");
            String verificationCode = requestDataMap.get("verificationCode");
            String signatureReason = requestDataMap.get("signatureReason");
            String system = requestDataMap.get("system");
            DAODataMng daoDataMng = new DAODataMng(system);
            Map reData = new HashMap<>();

            List<Map> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id='" + id + "' and obj.user_code='"+email+"' and  obj.verification_code='" + verificationCode + "'", null, 1);


            if (CollectionUtils.isNotEmpty(esignSignerList)) {

                Map<String, Object> esignSignerToSaveMap = new HashMap<>();
                esignSignerToSaveMap.put("sign_reason",signatureReason);
                esignSignerToSaveMap.put("id",esignSignerList.get(0).get("id"));
                daoDataMng.save("esign_signer",esignSignerToSaveMap);
                email= new String(Base64.getEncoder().encode(email.getBytes("UTF-8")));
                List<Map<String, String>> listR = DAOLightpdfSignIntegrate.listRule(system, "1");
                String esignUrl = listR.get(0).get(DAOLightpdfSignIntegrate.esignUrl);
                String redirectUrl = esignUrl + "?task_id=" + taskId + "&email=" + email + "&verification_code=" + esignSignerList.get(0).get("verification_code");

                reData.put("redirectUrl",redirectUrl);
                ResponseUtils.response(response,HttpStatus.OK,reData);

            }else {
                ResponseUtils.response(response,HttpStatus.NOT_FOUND);
            }


            return;


        } catch (IOException ex) {
            throw new RuntimeException(ex);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }


    }


    public synchronized void callback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {


            InputStream is = request.getInputStream();
            String ReceiveMsg = IOUtils.toString(is, StandardCharsets.UTF_8);
            is.close();

            HashMap<String, Object> MsgnMap = new HashMap<>();

            if (StringUtils.isBlank(ReceiveMsg)) {
                MsgnMap.put("msg", "无效的请求");
                response(response, HttpStatus.BAD_REQUEST);
                return;
            }

            String projectId = request.getParameter("projectid");
            DAODataMng daoDataMng = new DAODataMng(projectId);

            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();
            Map<String, Object> ReceiveMap = gson.fromJson(ReceiveMsg, new TypeToken<Map<String, Object>>() {
            }.getType());

            Integer callBackEnum = (Integer) ReceiveMap.get("status");
            Integer callBackType = (Integer) ReceiveMap.get("type");
            String callBackDesc = (String) ReceiveMap.get("callBackDesc");
            String backProcessId = (String) ReceiveMap.get("task_id");

            List esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.sign_flow_id='" + backProcessId + "'", null, 1);

            if (CollectionUtils.isEmpty(esignInstanceList)) {

                MsgnMap.put("msg", "无效的请求");
                response(response, HttpStatus.BAD_REQUEST);
                return;
            }

            response(response, HttpStatus.OK);


            Map esignInstanceMap = (Map) esignInstanceList.get(0);

            Long esignInstanceId = (Long) esignInstanceMap.get("id");
            String tableid = (String) esignInstanceMap.get("tableid");
            Long recordid = (Long) esignInstanceMap.get("recordid");
            Long studyid = (Long) esignInstanceMap.get("study_id");


            Map esignInstanceToSaveMap = new HashMap<>();
            esignInstanceToSaveMap.put("id", esignInstanceId);


            Map esignLogToSaveMap = new HashMap<>();
            esignLogToSaveMap.put("esign_instance_id", esignInstanceId);
            esignLogToSaveMap.put("tableid", tableid);
            esignLogToSaveMap.put("recordid", recordid);
            esignLogToSaveMap.put("study_id", studyid);
            esignLogToSaveMap.put("msg", ReceiveMsg);
            esignLogToSaveMap.put("esign_desc", callBackDesc);


//            List<Map<String, Object>> signerList = (List) callBackProcessVOMap.get("signerList");
//            Map<String, Object> signerMap = signerList.stream().max(Comparator.comparing((Map map) -> map.get("signDate") == null ? "" : (String) map.get("signDate"))).get();
//            esignInstanceToSaveMap.put("version", esignInstanceVersion);
            Map esignSignerToSaveMap = new HashMap<>();


            Integer backprocessEndTime = (Integer) ReceiveMap.get("updated_at");


            Date executeDate = new Date(backprocessEndTime * 1000l);
            DAOTransemail daoTransemail = new DAOTransemail(projectId);
            String subject = (String) esignInstanceMap.get("subject");

            String outputUrl = (String) ReceiveMap.get("output_url");

            List<Map<String, Object>> esignFileList = daoDataMng.listRecord("esign_file", "obj.esign_instance_id='" + esignInstanceId + "'", null, 100);
            Map<String, Object> esignFileMap = esignFileList.get(0);
            String initFileIndex = (String) esignFileMap.get("file");
            String initFileName = initFileIndex.split("\\*")[0];
            initFileName=initFileName.replace(".pdf","");


            Map<String, String> saveFileInfo = null;
            if (StringUtils.isNotEmpty(outputUrl)) {
                saveFileInfo = saveBackFile(projectId, outputUrl, tableid, recordid, (Long) esignInstanceMap.get("esign_engine_id"), esignFileMap);

            }


            if (callBackType == 1) {
                esignLogToSaveMap.put("execute_date", executeDate);

                String emailEncode = (String) ReceiveMap.get("email");
                String email = new String(Base64.getDecoder().decode(emailEncode.getBytes()));
                List<Map<String, Object>> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id='" + esignInstanceId + "' and obj.user_code='" + email + "'", null, 1);

                if (CollectionUtils.isNotEmpty(esignSignerList)) {
                    Map<String, Object> esignSignerMap = esignSignerList.get(0);
                    esignSignerToSaveMap.put("id", esignSignerMap.get("id"));
                    esignSignerToSaveMap.put("status", "2");
                    esignSignerToSaveMap.put("execute_date", executeDate);

                    esignLogToSaveMap.put("esign_status", "1");

                    esignLogToSaveMap.put("execute_date", executeDate);
                    esignLogToSaveMap.put("name", esignSignerMap.get("name"));

                }


                daoDataMng.save("esign_signer", esignSignerToSaveMap);


                String initiatorMail = PassportCacheUtil.getUserLoginidById(projectId, String.valueOf(esignInstanceMap.get("userid")));
                String content = "您好，您提交的《" + initFileName + "》," + esignLogToSaveMap.get("name") + "已签署完成。";
                daoTransemail.sendmail(subject + "," + esignLogToSaveMap.get("name") + "已签署完成", content, initiatorMail);


            }


            if (callBackEnum != null) {

                if (callBackEnum == 4) {

                    //流程完成业务

                    esignInstanceToSaveMap.put("esign_end_date", executeDate);
                    esignInstanceToSaveMap.put("status", "2");
                    esignLogToSaveMap.put("esign_status", "2");
                    esignLogToSaveMap.put("execute_date", executeDate);
                    esignLogToSaveMap.put("name", "SA");

                    if (StringUtils.isEmpty(outputUrl)) {
                        String initiatorMail = PassportCacheUtil.getUserLoginidById(projectId, String.valueOf(esignInstanceMap.get("userid")));
                        String content = "您好，您提交的《" + initFileName + "》已签署完成，文件返回失败，请联系管理员。";
                        daoTransemail.sendmail(subject + "，文件返回失败！", content, initiatorMail);

                    } else {

                        List<Map> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + esignInstanceMap.get("id"), null, 100);

                        String fileName = saveFileInfo.get("fileName");
                        String filePath = saveFileInfo.get("filePath");
                        SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmm");
                        String currDate = sdf.format(new Date());

                        int lastSeparatorIndex = filePath.lastIndexOf("/");
                             filePath.substring(lastSeparatorIndex + 1);
                        String outFilePath=filePath.replaceAll(".pdf","_Water.pdf");
                        PDFAddWatermark(filePath, outFilePath, "江苏恒瑞医药有限公司内部文件-"+currDate+"-副本", 80, 170, 12, 190, 200, 200);

                        File[] mailFile = {new File(outFilePath)};
                        String[] mailFileName ={fileName};
                        List<String> SignerMails = new ArrayList<>();
                        for (Map<String, String> esignSignerMap : esignSignerList) {

                            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                            String signDate = sdf2.format(esignSignerMap.get("execute_date"));
                            String content = "您好，您于" + signDate + "签署的《" + initFileName + "》已签署完成。";

                            SignerMails.add(esignSignerMap.get("user_code"));
                            daoTransemail.sendmail(esignInstanceMap.get("subject") + "签署完成！", content, esignSignerMap.get("user_code"),null,mailFile,mailFileName);

                        }
                        String initiatorMail = PassportCacheUtil.getUserLoginidById(projectId, String.valueOf(esignInstanceMap.get("userid")));

                        if (!SignerMails.contains(initiatorMail)) {

                            String content = "您好，您提交的《" + esignInstanceMap.get("subject") + "》已签署完成。";
//                        daoTransemail.sendmail(subject + "签署完成！", content, initiatorMail);
                            daoTransemail.sendmail(esignInstanceMap.get("subject") + "签署完成！", content, initiatorMail,null,mailFile,mailFileName);

                        }
                        }



                }

                if (callBackEnum == 2) {
                    esignInstanceToSaveMap.put("status", "3");
                    esignLogToSaveMap.put("esign_status", "3");


                    esignLogToSaveMap.put("execute_date", executeDate);
                    esignLogToSaveMap.put("name", "SA");


                    esignInstanceToSaveMap.put("esign_end_date", new Date(backprocessEndTime));


                    String initiatorMail = PassportCacheUtil.getUserLoginidById(projectId, String.valueOf(esignInstanceMap.get("userid")));
                    String content = "您好，您提交的《" + initFileName + "》签署未完成并已过期，请补签后生效。";
                    daoTransemail.sendmail(subject + "签署过期！", content, initiatorMail);
                }


                if (callBackEnum == 3) {
                    esignLogToSaveMap.put("esign_status", "6");
                    esignLogToSaveMap.put("execute_date", executeDate);
                    esignLogToSaveMap.put("name", "SA");

                }
            }

            daoDataMng.save("esign_instance", esignInstanceToSaveMap);
            daoDataMng.save("esign_log", esignLogToSaveMap);

            return;


        } catch (IOException e) {

            Log.error("", e);

        } catch (Exception e) {
            Log.error("", e);
        }

    }



//    1681868007

    public Map<String, String> saveBackFile(String projectId, String outputUrl, String tableid, Long recordid, Long esignEngineId, Map esignFileMap) {

        try {
            DAODataMng daoDataMng = new DAODataMng(projectId);



            String fileIndex = (String) esignFileMap.get("file");
            String fileName = fileIndex.split("\\*")[0];

            Map esignFileTOSaveMap = new HashMap<>();


            String signedFileKey = outputUrl.substring(outputUrl.lastIndexOf('/') + 1);
            String filePath = WebPath.getRootPath() + DAOFileup.tempfolder + "/eSign";

            LightpdfSignIntegrateUtil.downloadByNIO(outputUrl, filePath, signedFileKey);

            esignFileTOSaveMap.put("esign_signed_file_key", signedFileKey);
            esignFileTOSaveMap.put("id", (Long) esignFileMap.get("id"));
            AttachDAO attachDAO = new AttachDAO(projectId);


            String backStrAttachToSave = "";


            File fileLocal = new File(filePath + "/" + signedFileKey);

            String fileUuid = attachDAO.saveFile(fileLocal, "esign_file");
            String strAttachToSave = "" + fileName.toString().split("\\*")[0] + "*" + fileUuid + "|";
            esignFileTOSaveMap.put("signed_file", strAttachToSave);
            daoDataMng.save("esign_file", esignFileTOSaveMap);

            Map signEngineMap = daoDataMng.getRecord("esign_engine", esignEngineId);
            if (MapUtils.isNotEmpty(signEngineMap)) {


            String backFileFiled = (String) signEngineMap.get("back_file_filed");
            String toSignField = (String) signEngineMap.get("to_sign_field");
            String signPageFiled = (String) signEngineMap.get("sign_page_filed");


            if (StringUtils.isNotEmpty(backFileFiled) || StringUtils.isNotEmpty(toSignField) || StringUtils.isNotEmpty(signPageFiled)) {

                Map signDataMap = daoDataMng.getRecord(tableid, recordid);
                String backFileUuid = attachDAO.saveFile(fileLocal, tableid);
                backStrAttachToSave += "" + fileName.toString().split("\\*")[0] + "*" + backFileUuid + "|";

                String recordFileValue = (String) esignFileMap.get("record_file_value");

                if (StringUtils.isNotEmpty(backFileFiled)) {

                    String backFileValue = (String) signDataMap.get(backFileFiled);
                    backFileValue = backFileValue.replace(recordFileValue, backStrAttachToSave);

                    signDataMap.put(backFileFiled, backFileValue);

                } else if (StringUtils.isNotEmpty(signPageFiled)) {
                    String signPageValue = (String) signDataMap.get(signPageFiled);
                    signPageValue = signPageValue.replace(recordFileValue, backStrAttachToSave);

                    signDataMap.put(signPageFiled, signPageValue);
                } else if (StringUtils.isNotEmpty(toSignField)) {

                    String toSignValue = (String) signDataMap.get(toSignField);


                    toSignValue = toSignValue.replace(recordFileValue, backStrAttachToSave);

                    signDataMap.put(toSignField, toSignValue);
                }

                daoDataMng.saveRecord(tableid, signDataMap, "2", null);
            }
            }

            Map<String, String> FileInfoMap = new HashMap<>();

            FileInfoMap.put("filePath",filePath + "/" + signedFileKey);
            FileInfoMap.put("fileName",fileIndex.split("\\*")[0]);

            return FileInfoMap;
        } catch (IOException e) {

            Log.error("", e);

        } catch (Exception e) {
            Log.error("", e);
        }


        return null;
    }

    public void Revoke(HttpServletRequest request, HttpServletResponse response) {

        try {


            String revokeReson = request.getParameter("revokeReson");

            String esignInstanceIdArrStr = request.getParameter("esignInstanceId");

            String[] esignInstanceIdArr = esignInstanceIdArrStr.split(",");


            String projectId = SessUtil.getSessInfo().getProjectid();
            Date currentDate = new Date();

            String projectid = SessUtil.getSessInfo().getProjectid();

            DAODataMng daoDataMng = new DAODataMng(projectid);
            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();
            Map currentUserMap = SessUtil.getSessInfo().getUser();


            Map revokeParamMap = new HashMap<>();
            revokeParamMap.put("type", "1");
            revokeParamMap.put("remark", revokeReson);
            String revokeParamJson = gson.toJson(revokeParamMap);

            List<Map<String, String>> listR = DAOLightpdfSignIntegrate.listRule(projectId, "1");

            String esignUrl = listR.get(0).get(DAOLightpdfSignIntegrate.esignUrl);
            for (String esignInstanceIdStr : esignInstanceIdArr) {


               Long esignInstanceId=Long.valueOf(esignInstanceIdStr);
//            Long esignInstanceId = Long.valueOf(request.getParameter("esignInstanceId"));

            Map esignInstanceMap = daoDataMng.getRecord("esign_instance", esignInstanceId);

            String signFlowId = (String) esignInstanceMap.get("sign_flow_id");

            String revokeMsg = LightpdfSignIntegrateUtil.httpPut(esignUrl + "/api/tasks/" + signFlowId, revokeParamJson);

//            Map revokeMsgMap = gson.fromJson(revokeMsg, new TypeToken<Map<String, Object>>() {
//            }.getType());
//
//
//            if (!StringUtils.equals(String.valueOf(revokeMsgMap.get("status")), "200")) {
//
//                String message = (String) revokeMsgMap.get("message");
//                response.getOutputStream().write(message.getBytes("UTF-8"));
//                response.getOutputStream().close();
//                return;
//
//            }





            DAOTransemail daoTransemail = new DAOTransemail(projectId);

            String initiatorMail = PassportCacheUtil.getUserLoginidById(projectId, String.valueOf(esignInstanceMap.get("userid")));


            Map esignLogToSaveMap = new HashMap<>();
            esignLogToSaveMap.put("esign_instance_id", esignInstanceId);
            esignLogToSaveMap.put("tableid", esignInstanceMap.get("tableid"));
            esignLogToSaveMap.put("recordid", esignInstanceMap.get("recordid"));
            esignLogToSaveMap.put("study_id", esignInstanceMap.get("study_id"));
            esignLogToSaveMap.put("esign_status", "7");
//            esignLogToSaveMap.put("msg", ReceiveMsg);
//            esignLogToSaveMap.put("esign_desc", callBackDesc);
            esignLogToSaveMap.put("execute_date", currentDate);


            esignLogToSaveMap.put("name", currentUserMap.get("username"));

            Map esignInstanceToSaveMap = new HashMap<>();

            esignInstanceToSaveMap.put("id", esignInstanceId);
            esignInstanceToSaveMap.put("revoke_msg", revokeMsg);
            esignInstanceToSaveMap.put("status", "5");
            esignInstanceToSaveMap.put("cancel_cause", revokeReson);
            esignInstanceToSaveMap.put("cancelor", currentUserMap.get("username"));
            esignInstanceToSaveMap.put("esign_end_date", currentDate);

            daoDataMng.save("esign_instance", esignInstanceToSaveMap);
            daoDataMng.save("esign_log", esignLogToSaveMap);
//			daoDataMng.listRecord("")

            List<Map<String, Object>> esignFileList = daoDataMng.listRecord("esign_file", "obj.esign_instance_id='" + esignInstanceId + "'", null, 100);
            Map<String, Object> esignFileMap = esignFileList.get(0);
            String initFileIndex = (String) esignFileMap.get("file");
            String initFileName = initFileIndex.split("\\*")[0];
            initFileName=initFileName.replace(".pdf","");

            String content = "您好，《" + initFileName + "》该签署任务已被签署发起人废除。<br> 废除原因为" + revokeReson;

            List<Map> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + esignInstanceId, null, 100);
            StringBuilder receiver = new StringBuilder();

            for (Map<String, String> esignSignerMap : esignSignerList) {
                String email = esignSignerMap.get("user_code");
                if (receiver.length() > 0) {
                    receiver.append(", ");
                }
                receiver.append(email);
            }
            daoTransemail.sendmail(esignInstanceMap.get("subject") + "签署废除！", content, receiver.toString());
            }

            response.getOutputStream().write("200".getBytes("UTF-8"));
            response.getOutputStream().close();


        } catch (Exception e) {
            Log.error("", e);
        }

    }


    private static String generateRandomCode() {
        String str = "0123456789";
        char[] chars = str.toCharArray();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < 6; i++) {
            int index = (int) (Math.random() * chars.length);
            sb.append(chars[index]);
        }
        return sb.toString();


    }


    public String[] wordToPdf(String signFile, String tableid, AttachDAO attachDAO, String projectId) throws Exception {

        String[] signFileArr = signFile.split("\\*");
        String signFilePathURI = attachDAO.getFilePathURI(signFileArr[1], tableid);
        String fileExtension = signFileArr[1].substring(signFileArr[1].lastIndexOf(".") + 1);
        String signFilePathFullURI = WebPath.getRootPath() + "/" + signFilePathURI;


        if (fileExtension.equalsIgnoreCase("doc") || fileExtension.equalsIgnoreCase("docx")) {
            DAOWordreport daoWordreport = new DAOWordreport(projectId);
            daoWordreport.convertDocToPdf(signFilePathFullURI, "2", "4");
        } else if (!fileExtension.equalsIgnoreCase("pdf")) {
            return null;
        }
        String fileName = signFileArr[0].substring(0, signFileArr[0].lastIndexOf(".")) + ".pdf";
        signFilePathFullURI = signFilePathFullURI.substring(0, signFilePathFullURI.lastIndexOf(".")) + ".pdf";

        return new String[]{fileName, signFilePathFullURI};
    }

//    public static void main(String[] args) throws UnsupportedEncodingException {
////        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
////
////        Date date = new Date(1686208530 * 1000l);
////        System.out.println(sdf.format(date));
//
//        Date date = new Date();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmm");
//        String formattedDate = sdf.format(date);
//        System.out.println(formattedDate);
//    }



    public static void main(String[] args) {
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();

        objectObjectHashMap.put("status","200");
        objectObjectHashMap.put("msg","success");


        ArrayList<String> objects = new ArrayList<>();
        objects.add("aaaaaa1");
        objects.add("aaaaaa2");
        objects.add("aaaaaa3");
        Gson gson = new Gson();
        objectObjectHashMap.put("data",objects);
        System.out.println(gson.toJson(objectObjectHashMap));

    }




    public static ArrayList<Map> getSignerInfosList(File fileLocal, List<Map<String, String>> signersList) throws IOException {
        File esign_file = fileLocal;

        String absolutePath = esign_file.getAbsolutePath();
        PdfReader pdfReader = new PdfReader(absolutePath);
//        float[] xArr = getKeyWords(pdfReader, "签名");
//        Float X = ArrayUtils.isEmpty(xArr) ? null : xArr[0];
        ArrayList<Map> signerInfosList = new ArrayList<>();
        ArrayList<String> existRandomCode = new ArrayList<>();


        for (Map signerMap : signersList) {
            String name = String.valueOf(signerMap.get("name")).split("\\(")[0];

            float[] coordinate = getKeyWords(pdfReader, name);
            Long page = ArrayUtils.isEmpty(coordinate) ? null : (long) coordinate[2];
            Float Y = ArrayUtils.isEmpty(coordinate) ? null : coordinate[1];
            Float X = ArrayUtils.isEmpty(coordinate) ? null : coordinate[0]+100;

//                HashMap<Object, Object> coordinateMap = new HashMap<>();
            Map signerInfosMap = new HashMap<>();
            String email = (String) signerMap.get("value");

            signerInfosMap.put("page", page);
            signerInfosMap.put("position_x", X);
            signerInfosMap.put("position_y", Y);

//                Log.info("name:" + name + ",page:" + page + ",X:" + X + ",Y:" + Y);

//                signerInfosMap.put("coordinate",coordinateMap);
            signerInfosMap.put("email",new String(Base64.getEncoder().encode(email.getBytes("UTF-8")))
            );
            String randomCode;
            while (true) {
                randomCode = generateRandomCode();
                if (!existRandomCode.contains(randomCode)) {
                    existRandomCode.add(randomCode);
                    break;
                }
            }
            signerInfosMap.put("verification_code", randomCode);
            signerInfosMap.put("name", signerMap.get("name"));
            signerInfosList.add(signerInfosMap);
        }
        pdfReader.close();

        return signerInfosList;
    }

    public void eSignRegister(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {


            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();


            String projectId = SessUtil.getSessInfo().getProjectid();
//            Map userMap = SessUtil.getSessInfo().getUser();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            String esignInstanceJson = request.getParameter("eSigndata");


//            Long esignInstanceId = Long.valueOf(request.getParameter("esignInstanceId"));
            String signersJson = request.getParameter("signersJson");
            String tableid = request.getParameter("tableid");
            Long recordid = Long.valueOf(request.getParameter("recordid"));

            Map currentUserMap = (Map) SessUtil.getSessInfo().getUser();
            String initiatorUserCode = (String) currentUserMap.get("loginid");

            Map esignInstanceMap = gson.fromJson(esignInstanceJson, new TypeToken<Map<String, Object>>() {
            }.getType());

            String signFile = (String) esignInstanceMap.get("signfile");
            String signPageFile = (String) esignInstanceMap.get("signpagefile");

//            String signPageFile = request.getParameter("signPage");


            AttachDAO attachDAO = new AttachDAO(projectId);

            String[] signFilePdfArr = wordToPdf(signFile, tableid, attachDAO, projectId);

            if (ArrayUtils.isEmpty(signFilePdfArr)) {

                response.setCharacterEncoding("UTF-8");
                response.getOutputStream().write(String.valueOf("文件格式错误，请上传word或pdf文件！").getBytes("UTF-8"));
                response.getOutputStream().close();
            }

            String fileName = signFilePdfArr[0];
            String fileLocalUri = signFilePdfArr[1];


            if (StringUtils.isNotEmpty(signPageFile)) {
                String[] signPageFilePdfArr = wordToPdf(signPageFile, tableid, attachDAO, projectId);

                if (ArrayUtils.isEmpty(signPageFilePdfArr)) {
                    response.getOutputStream().write(String.valueOf("文件格式错误，请上传word或pdf文件！").getBytes("UTF-8"));
                    response.getOutputStream().close();
                }
                fileLocalUri = PDFMerger.PDFMerger(signFilePdfArr[1], signPageFilePdfArr[1], signPageFilePdfArr[0]);
                fileName = signPageFilePdfArr[0];

            }

            File fileLocal = new File(fileLocalUri);


            List<Map<String, String>> DAOLightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectId, "1");
            Map<String, String> DAOLightpdfSignIntegrateMap = DAOLightpdfSignIntegrateList.get(0);
            String esignUrl = DAOLightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.esignUrl);
            String uploadFileMsgJson = LightpdfSignIntegrateUtil.uploadFile(esignUrl + "/api/files/upload", fileLocal.getPath(), fileName.replace(" ", ""));

            if (StringUtils.equals(uploadFileMsgJson, "408")) {
                response.getOutputStream().write("连接超时，请联系管理员。".getBytes("UTF-8"));
                response.getOutputStream().close();
                return;
            }


            Map uploadFileMsgMap = gson.fromJson(uploadFileMsgJson, new TypeToken<Map<String, Object>>() {
            }.getType());


            if (!StringUtils.equals(String.valueOf(uploadFileMsgMap.get("status")), "200")) {
                response.getOutputStream().write(String.valueOf(uploadFileMsgMap.get("message")).getBytes("UTF-8"));
                response.getOutputStream().close();
                return;
            }


            Map uploadFileDataMap = (Map) uploadFileMsgMap.get("data");
            List uploadFileDataList = (List) uploadFileDataMap.get("items");
            Map uploadFileData0Map = (Map) uploadFileDataList.get(0);
            String eSignfileKey = (String) uploadFileData0Map.get("file_id");


            DtrefDAO dtrefDAO = new DtrefDAO(projectId);

            String eSignDataRefField = dtrefDAO.getRefField("xsht", tableid);

            Map eSignDataMap = daoDataMng.getRecord(tableid, Long.valueOf(recordid));

            Long studyId = (Long) eSignDataMap.get(eSignDataRefField);

            List<Map<String, String>> eSignEngineList = daoDataMng.listRecord("esign_engine", "obj.tableid='" + tableid + "'", null, 100);

            Map eSignEngineMap = eSignEngineList.get(0);


            List<Map<String, String>> signersList = gson.fromJson(signersJson, new TypeToken<List<Map<String, String>>>() {
            }.getType());


            ArrayList<Map> signerInfosList = getSignerInfosList(fileLocal, signersList);

            String CNSMsg = LightpdfSignIntegrateUtil.eSignRegister(projectId,eSignfileKey, esignInstanceMap, signerInfosList, DAOLightpdfSignIntegrateMap);


            Map CNSMsgMap = gson.fromJson(CNSMsg, new TypeToken<Map<String, Object>>() {
            }.getType());

            if (!StringUtils.equals(String.valueOf(CNSMsgMap.get("status")), "200")) {
                String message = (String) CNSMsgMap.get("message");
                response.getOutputStream().write(message.getBytes("UTF-8"));
                response.getOutputStream().close();
                return;

            }


            Map GUUDataMap = (Map) CNSMsgMap.get("data");
            String signFlowId = (String) GUUDataMap.get("task_id");


            Map esignInstanceToSaveMap = new HashMap();
            esignInstanceToSaveMap.put("tableid", tableid);
            esignInstanceToSaveMap.put("recordid", recordid);
            esignInstanceToSaveMap.put("active", "1");
            esignInstanceToSaveMap.put("study_id", studyId);
//            esignInstanceToSaveMap.put("edition_name", eSignDataMap.get(eSignEngineMap.get("edition_filed")));
//            esignInstanceToSaveMap.put("edition_date", eSignDataMap.get(eSignEngineMap.get("edition_date_filed")));
//            esignInstanceToSaveMap.put("status", "0");
            esignInstanceToSaveMap.put("esign_engine_id", eSignEngineMap.get("id"));
            esignInstanceToSaveMap.put("sign_flow_id", signFlowId);
            esignInstanceToSaveMap.put("status", "1");
            esignInstanceToSaveMap.put("esign_msg", CNSMsg);

            String signFlowExpireTime = (String) esignInstanceMap.get("signFlowExpireTime");
            SimpleDateFormat DateFormatYMDHMS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date signFlowExpireDate = DateFormatYMDHMS.parse(signFlowExpireTime);


            esignInstanceToSaveMap.put("remark", esignInstanceMap.get("remark"));
            esignInstanceToSaveMap.put("subject", esignInstanceMap.get("subject"));
            esignInstanceToSaveMap.put("sign_expire_data", signFlowExpireDate);
            esignInstanceToSaveMap.put("initiator", currentUserMap.get("username"));
            esignInstanceToSaveMap.put("esign_begin_date", new Date());
            esignInstanceToSaveMap.put("file_name", fileName);
            daoDataMng.save("esign_instance", esignInstanceToSaveMap);


            ArrayList<Map> signUrlInfosToSaveList = new ArrayList<>();
            for (Map signUrlInfosMap : signerInfosList) {
                Map signUrlInfosToSaveMap = new HashMap<>();
                signUrlInfosToSaveMap.put("name", signUrlInfosMap.get("name"));

                String email = (String) signUrlInfosMap.get("email");
                signUrlInfosToSaveMap.put("user_code", new String(Base64.getDecoder().decode(email.getBytes())));
                signUrlInfosToSaveMap.put("verification_code", signUrlInfosMap.get("verification_code"));
                signUrlInfosToSaveMap.put("esign_url", signUrlInfosMap.get("signUrlShort"));
                signUrlInfosToSaveMap.put("esign_instance_id", esignInstanceToSaveMap.get("id"));
                signUrlInfosToSaveMap.put("status", "0");
                signUrlInfosToSaveMap.put("signer_id", signUrlInfosMap.get("signer_id"));

                signUrlInfosToSaveList.add(signUrlInfosToSaveMap);
//				daoDataMng.save("esign_signer",signUrlInfosToSaveMap );
//				daoDataMng.saveBatch("esign_signer", (List) signUrlInfosToSaveList, 2L,null);


            }

            Long userid = Long.valueOf(SessUtil.getSessInfo().getUserid());
            daoDataMng.saveBatch("esign_signer", (List) signUrlInfosToSaveList, userid, null);


            Map<Object, Object> esignFileMap = new HashMap<>();


            esignFileMap.put("tableid", tableid);
            esignFileMap.put("recordid", recordid);
            esignFileMap.put("study_id", studyId);
            esignFileMap.put("record_file_value", StringUtils.isNotEmpty(signPageFile) ? signPageFile + "|" : signFile + "|");
            esignFileMap.put("esign_file_key", eSignfileKey);
            String fileLocalUuid = attachDAO.saveFile(fileLocal, "esign_file");
            String strAttachToSave = "" + fileName + "*" + fileLocalUuid + "|";
            esignFileMap.put("esign_file_key", eSignfileKey);
            esignFileMap.put("file", strAttachToSave);


            esignFileMap.put("esign_instance_id", esignInstanceToSaveMap.get("id"));

            daoDataMng.save("esign_file", esignFileMap);


            Map esignLogToSaveMap = new HashMap<>();
            esignLogToSaveMap.put("esign_instance_id", esignInstanceToSaveMap.get("id"));
            esignLogToSaveMap.put("tableid", tableid);
            esignLogToSaveMap.put("recordid", recordid);
            esignLogToSaveMap.put("study_id", studyId);
            esignLogToSaveMap.put("esign_status", "0");
//            esignLogToSaveMap.put("msg", ReceiveMsg);
//            esignLogToSaveMap.put("esign_desc", callBackDesc);
            esignLogToSaveMap.put("execute_date", new Date());
            esignLogToSaveMap.put("name", currentUserMap.get("username"));


            daoDataMng.save("esign_log", esignLogToSaveMap);


            response.getOutputStream().write("200".getBytes());
            response.getOutputStream().close();









            return;

        } catch (Exception e) {
            Log.error("", e);

        } finally {

        }
    }

    public void eSign(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {


            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);

            String tableid = request.getParameter("tableid");
            Long recordid = Long.valueOf(request.getParameter("recordid"));
            String anewSign = request.getParameter("anewSign");
            String anewInstanceId = request.getParameter("currid");
            request.setAttribute("isSign", "1");

            if (StringUtils.isNotEmpty(anewSign) && StringUtils.isNotEmpty(anewInstanceId)) {

                Map anewInstanceToSaveMap = new HashMap<>();
                anewInstanceToSaveMap.put("id", Long.valueOf(anewInstanceId));
                anewInstanceToSaveMap.put("active", "0");
                daoDataMng.save("Esign_instance", anewInstanceToSaveMap);
            }

            List<Map<String, String>> eSignEngineList = daoDataMng.listRecord("esign_engine", "obj.tableid='" + tableid + "'", null, 100);

            Map eSignEngineMap = eSignEngineList.get(0);
            String sginRole = (String) eSignEngineMap.get("sgin_role");
            String toSignFieldid = (String) eSignEngineMap.get("to_sign_field");
            String signPageFiledid = (String) eSignEngineMap.get("sign_page_filed");


            DtrefDAO dtrefDAO = new DtrefDAO(projectId);

            String eSignDataRefField = dtrefDAO.getRefField("xsht", tableid);

            Map eSignDataMap = daoDataMng.getRecord(tableid, recordid);

            Long studyId = (Long) eSignDataMap.get(eSignDataRefField);

            String toSignFiles = (String) eSignDataMap.get(toSignFieldid);


            if (StringUtils.isEmpty(toSignFiles)) {

                response.getOutputStream().write(String.valueOf("请上传待签署文件!").getBytes("GBK"));
                response.getOutputStream().close();
                return;
            }


            ArrayList<Object> eSignFileList = new ArrayList<>();

            for (String toSignFile : toSignFiles.split("\\|")) {
                Map eSignFileMap = new HashMap<>();
                String[] toSignFileArr = toSignFile.split("\\*");
                eSignFileMap.put("file_uuid", toSignFileArr[1]);
                eSignFileMap.put("filename", toSignFileArr[0]);
                eSignFileList.add(eSignFileMap);
            }


//            Map signPageFileMap = new HashMap<>();
            ArrayList<Object> signPageFileList = new ArrayList<>();

            if (StringUtils.isNotEmpty(signPageFiledid)) {
                String signPageFiles = (String) eSignDataMap.get(signPageFiledid);
                if (StringUtils.isEmpty(signPageFiles)) {
                    response.getOutputStream().write(String.valueOf("请上传签字页!").getBytes("GBK"));
                    response.getOutputStream().close();
                    return;

                }


                for (String signPageFile : signPageFiles.split("\\|")) {
                    Map signPageFileMap = new HashMap<>();
                    String[] toSignFileArr = signPageFile.split("\\*");
                    signPageFileMap.put("file_uuid", toSignFileArr[1]);
                    signPageFileMap.put("filename", toSignFileArr[0]);
                    signPageFileList.add(signPageFileMap);
                }


//                String[] signPageFileArr = signPageFile.split("\\|")[0].split("\\*");
//                signPageFileMap.put("filename", signPageFileArr[0]);
//
//                signPageFileMap.put("file_uuid", signPageFileArr[1]);


            }

            request.setAttribute("eSignFileList", eSignFileList);
            request.setAttribute("signPageFileList", signPageFileList);
            Map<Object, Object> esignInstanceMap = new HashMap<>();
            esignInstanceMap.put("edition_name", eSignDataMap.get(eSignEngineMap.get("edition_filed")));
            Date editionDate = (Date) eSignDataMap.get(eSignEngineMap.get("edition_date_filed"));

            esignInstanceMap.put("edition_date", editionDate);

            request.setAttribute("esignInstanceMap", esignInstanceMap);
            String stuydUser = LightpdfSignIntegrateUtil.getStudyUser(studyId, sginRole, projectId);
            request.setAttribute("stuydUser", stuydUser);


            String mailTemplateType = (String) eSignEngineMap.get("mail_template_type");
            if (!StringUtils.isEmpty(mailTemplateType)) {
                this.forwardByUri(request, response, "formMail.getemail.do?tableid=" + tableid + "&recordid=" + recordid + "&where=obj.type='" + mailTemplateType + "'");

                return;

            }
            this.forward(request, response, "eSign");


        } catch (Exception e) {
            Log.error("", e);

        }
    }


    public void View(HttpServletRequest request, HttpServletResponse response) throws IOException {

        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            String tableid = request.getParameter("tableid");
            Map currentUserMap = SessUtil.getSessInfo().getUser();

            Long recordid = Long.valueOf(request.getParameter("recordid"));
            List esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.active =1 and obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "'", null, 1);
            List<Map<String, Object>> esignLogList = daoDataMng.listRecord("esign_log", "obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "'", "obj.execute_date asc", 100);
            SimpleDateFormat sdf_d = new SimpleDateFormat("yyyy-MM-dd");

//            for (Map<String, Object> esignLogMap : esignLogList) {
//                 LightpdfSignIntegrateUtil.getRecordUI("esign_log", esignLogMap);
//            }

            for (int i = 0; i < esignLogList.size(); i++) {

                Map<String, Object> esignLogMap = esignLogList.get(i);
                esignLogMap.put("sn", i + 1);

                DateFormat formatTo = new SimpleDateFormat("HH:mm");

                esignLogMap.put("timeStamp", formatTo.format(esignLogMap.get("execute_date")));

                Integer esignStatus = Integer.valueOf((String) esignLogMap.get("esign_status"));

                esignLogMap.put("name", StringUtils.equals((CharSequence) esignLogMap.get("name"), "SA") ? "" : esignLogMap.get("name"));

                switch (esignStatus) {
                    case 0:
                        esignLogMap.put("desc", "创建签署流程");
                        break;
                    case 1:
                        esignLogMap.put("desc", "签署完成");
                        break;
                    case 2:
                        esignLogMap.put("desc", "所有用户均已签署，签署成功");
                        break;
                    case 3:
                        esignLogMap.put("desc", "到达过期日期，已过期");
                        break;
                    case 4:
                        esignLogMap.put("desc", "签署截止前通知");
                        break;
                    case 5:
                        esignLogMap.put("desc", "拒签");
                        break;
                    case 6:
                        esignLogMap.put("desc", "中止文件下载成功");
                        break;
                    case 7:
                        esignLogMap.put("desc", "中止完成");
                        break;
                    default:
                        esignLogMap.put("desc", "");
                }
            }

            esignLogList.sort(Comparator.comparing((Map<String, Object> esignLogMap) -> (Integer) esignLogMap.get("sn")).reversed());

            Map<String, List<Map<String, Object>>> esignLogGroupDayMapAsc = esignLogList.stream().collect(Collectors.groupingBy((Map esignLogMap) -> sdf_d.format(esignLogMap.get("execute_date"))));

            Map esignLogGroupDayMap = new TreeMap<>(Collections.reverseOrder());

            esignLogGroupDayMap.putAll(esignLogGroupDayMapAsc);


            Map esignInstanceMap = (Map) esignInstanceList.get(0);
            Map esignInstanceUIMap = LightpdfSignIntegrateUtil.getRecordUI("esign_instance", esignInstanceMap, projectId);
            Long userid = (Long) esignInstanceMap.get("userid");
            DAOPPData dao = new DAOPPData(projectId);
            String initiatorName = (String) dao.getUserById(userid).getRealName();

            SimpleDateFormat sdf_s = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            esignInstanceUIMap.put("initiatDate", sdf_s.format(esignInstanceMap.get("createtime")));


            Long esignInstanceId = (Long) esignInstanceMap.get("id");
            String Msg = (String) esignInstanceMap.get("esign_msg");

            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();

            Map CNSMsgMap = gson.fromJson(Msg, new TypeToken<Map<String, Object>>() {
            }.getType());
//			Map msgData = (Map) CNSMsgMap.get("data");
//			List<Map> signUrlInfosList = (List) msgData.get("signUrlInfos");
//			Map<String, Map> signUrlInfosListToMap = signUrlInfosList.stream().collect(Collectors.toMap((Map map) -> (String) map.get("user_code"),t -> t));
//


            List<Map<String, Object>> esignFileList = daoDataMng.listRecord("esign_file", "obj.esign_instance_id=" + esignInstanceId, null, 100);

//            esignInstanceUIMap.put("esign_url", sdf_s.format(esignInstanceMap.get("createtime")));

//			List<Map<String, Object>> esignFileSignerList = daoDataMng.listRecord("esign_file_signer", "obj.esign_instance_id=" + esignInstanceId, null, 100);
            List<Map<String, Object>> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + esignInstanceId, null, 100);

//            Map<String, Map> esignSignerListToMap = esignSignerList.stream().collect(Collectors.toMap((Map map) -> (String) map.get("user_code"), t -> t));

//            esignInstanceUIMap.put("Signer", esignSignerListToMap.get(currentUserMap.get("domain_accounts")));


//			for (Map<String, Object> esignFileSignerMap : esignFileSignerList) {
//				esignFileSignerMap.put("SignerInfoMap",esignSignerListToMap.get(esignFileSignerMap.get("user_code")));
//
//			}

//			Map<Long, List<Map<String, Object>>> esignFileSignerListToMap = esignFileSignerList.stream().collect(Collectors.groupingBy((Map map1) -> (Long) map1.get("esign_file_id")));


            for (Map esignFileMap : esignFileList) {


                String file = (String) esignFileMap.get("file");
                String signedFile = (String) esignFileMap.get("signed_file");

                String fileIndex = StringUtils.isNotEmpty(signedFile) ? signedFile : file;

                esignFileMap.put("filename", fileIndex.split("\\*")[0]);
//				eSignFileMap.put("filename",URLEncoder.encode(fileIndex.substring(0, fileIndex.lastIndexOf("*")),"UTF-8"));


                esignFileMap.put("file_uuid", fileIndex.substring(fileIndex.lastIndexOf("*") + 1, fileIndex.lastIndexOf("|")));


            }


            List esignSignerUIList = new ArrayList<>();
            for (Map esignSignertMap : esignSignerList) {
                esignSignerUIList.add(LightpdfSignIntegrateUtil.getRecordUI("esign_signer", esignSignertMap, projectId));
            }

//			String str = studyid_Schema.formatToOutput(tableid, mapF, eSignDataMap);
            request.setAttribute("esignInstanceMap", esignInstanceUIMap);
            request.setAttribute("esignFileList", esignFileList);
            request.setAttribute("esignLogGroupDayMap", esignLogGroupDayMap);
            request.setAttribute("fileSignerList", esignSignerUIList);


            this.forward(request, response, "View");


        } catch (Exception e) {
            Log.error("", e);
        } finally {


        }
    }


}
