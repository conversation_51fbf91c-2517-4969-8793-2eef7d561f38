package net.bioknow.cdtms.formMail;


import net.bioknow.webutil.tools.Log;

import java.util.List;
import java.util.Map;

public class RunnableOfSendMail implements Runnable{

    private String projectId;
    private Map<String,Object> mailContent;

    @Override
    public void run() {
        DAOTransemail daoTransemail = new DAOTransemail(projectId);
        Map<String, Object> mailContentMap = getMailContent();
        String subject = mailContentMap.get("subject").toString();
        String content = mailContentMap.get("content").toString();
        List<String> emailList = (List<String>) mailContentMap.get("email");
        emailList.forEach(email -> {
            try {
                daoTransemail.sendmail(subject, content, email);
            } catch (Exception e) {
                Log.error("send mail error , 邮件接收人："+email+")；具体原因:", e);
            }
        });
    }

    //getter and setter


    public Map<String, Object> getMailContent() {
        return mailContent;
    }

    public void setMailContent(Map<String, Object> mailContent) {
        this.mailContent = mailContent;
    }

    private String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
}
