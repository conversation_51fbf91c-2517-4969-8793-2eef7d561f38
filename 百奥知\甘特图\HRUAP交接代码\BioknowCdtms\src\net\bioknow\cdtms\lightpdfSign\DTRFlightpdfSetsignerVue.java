package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.services.core.ApiResult;
import net.bioknow.services.uap.dbdatamng.function.DTRecordFuncActionNew;
import net.bioknow.services.uap.dbdatamng.function.FuncInfoBeanNew;
import net.bioknow.services.uap.dbdatamng.function.FuncParamBeanNew;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;


public class DTRFlightpdfSetsignerVue extends DTRecordFuncActionNew {


	public boolean canUse(int auth, String tableid, Long recordid) {

		try {

			if (!StringUtils.equals(tableid,"esign_instance")) {
				return false;
			}
			String projectId = SessUtil.getSessInfo().getProjectid();

			DAODataMng daoDataMng=new DAODataMng(projectId);
			Map esignInstanceMap = daoDataMng.getRecord(tableid, recordid);
			if (!StringUtils.equals(SessUtil.getSessInfo().getUserid(),String.valueOf(esignInstanceMap.get("userid")))) {

				return false;
			}
			String esignInstanceStatus = (String) esignInstanceMap.get("status");
			if (StringUtils.equals(esignInstanceStatus,"1")) {
				return true;
			}

		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}

	@Override
	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBeanNew fpb) {
		try {
			HashMap map = new HashMap();
			map.put("url", "/LightpdfSignIntergrate.ajaxsetSigner3.do?id=" + fpb.getRecordid());
			response.getOutputStream().write(ApiResult.ok("ok", map).getBytes(StandardCharsets.UTF_8));
			response.getOutputStream().close();
		}catch (IOException e) {
            throw new RuntimeException(e);
        }

    }



	public FuncInfoBeanNew getFIB(String tableid) {
		FuncInfoBeanNew fib = new FuncInfoBeanNew("cdtms_DTRFlightpdfSetsignerVue");

		fib.setName("添加签字人");
//		fib.setType(FuncInfoBean.FUNCTYPE_INNERWINDOW);
		fib.setType(FuncInfoBeanNew.FUNCTYPE_INNERWINDOW);
//		fib.setWinHeight(500);
//		fib.setWinWidth(500);
//		fib.setSimpleViewShow(true);




		return fib;
	}

}
