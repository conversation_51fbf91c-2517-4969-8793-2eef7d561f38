package net.bioknow.cdtms.lightpdfSign;//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//


import com.google.gson.Gson;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import net.bioknow.services.core.ApiResult;
import net.bioknow.services.uap.dbdatamng.function.DTRecordFuncActionNew;
import net.bioknow.services.uap.dbdatamng.function.DTTableFuncActionNew;
import net.bioknow.services.uap.dbdatamng.function.FuncInfoBeanNew;
import net.bioknow.services.uap.dbdatamng.function.FuncParamBeanNew;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTTableFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang.StringUtils;

public class DTRFlightpdfCancelVue extends DTRecordFuncActionNew {


	public boolean canUse(int auth, String tableid, Long recordid) {
		try {

			if (!StringUtils.equals(tableid,"esign_instance")) {
				return false;
			}
			String projectId = SessUtil.getSessInfo().getProjectid();

			DAODataMng daoDataMng=new DAODataMng(projectId);
			Map esignInstanceMap = daoDataMng.getRecord(tableid, recordid);
			if (!StringUtils.equals(SessUtil.getSessInfo().getUserid(),String.valueOf(esignInstanceMap.get("userid")))) {

				return false;
			}
			String esignInstanceStatus = (String) esignInstanceMap.get("status");
			if (StringUtils.equals(esignInstanceStatus,"1")) {
				return true;
			}
		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}




	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBeanNew fpb) {
		try {
			Map<String, Object> map = new HashMap();
			map.put("url", "/uapvue/index.html#/basic_form");
			Gson json = new Gson();
			Map<String, Object> params = (Map)json.fromJson(json.toJson(fpb), Map.class);
			params.put("url", "/LightpdfSignIntergrate.ajaxCancel.do?id=" + fpb.getRecordid());
			map.put("param", params);
			response.getOutputStream().write(ApiResult.ok("ok", map).getBytes(StandardCharsets.UTF_8));
			response.getOutputStream().close();
		} catch (Exception var7) {
			Log.error(var7.getMessage(), var7);
		}

	}

	public FuncInfoBeanNew getFIB(String tableid) {

		FuncInfoBeanNew fib = new FuncInfoBeanNew("cdtms_DTRFlightpdfCancelVue");

		try {
			fib.setName("废除");
			fib.setWinWidth(550);
			fib.setWinHeight(300);
			//fib.setConfirmStr(this.getLanguage().get("确定删除当前列表的所有数据么？此为不可恢复操作，且不记录删除历史。 请做好系统备份再慎重操作！"));
			fib.setType("vuemodal");
		} catch (Exception var4) {
			Log.error("", var4);
		}

		return fib;
	}
}
