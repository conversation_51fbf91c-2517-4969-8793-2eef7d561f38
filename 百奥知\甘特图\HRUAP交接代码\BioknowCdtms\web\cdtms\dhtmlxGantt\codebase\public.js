var param = studyId ? '?tableid=xsht&studyId=' + studyId : '?tableid=xsht';
var ganttScrollState = null;
const GANTT_DEFAULT_LEVEL = 'quarters';

gantt.attachEvent("onBeforeGanttReady", function () {
    // do something
    // alert("2");
    // initializeModal.style.display = "block";
    loader.style.display = 'block';
    // if (studyId){return true;}
    let actionsLastElem = document.querySelector('.actions_last-elem');

    if (studyId && actionsLastElem && currRole=='MDM') {
        // 创建一个 span 元素
        let span = document.createElement('span');
        // 添加 class 属性和 data-action 属性
        span.setAttribute('class', 'btn outline-btn js-action-btn');
        span.setAttribute('data-action', 'templateStore');
        // 创建一个文本节点并设置其文本内容
        let textNode = document.createTextNode('存储为模板');
        span.appendChild(textNode);
        // 在 div 元素内部的第一个位置插入 span 元素
        actionsLastElem.insertBefore(span, actionsLastElem.firstChild);
    }
});

const templateStoreSaveDiv = document.getElementById("templateStoreSave");
const templateStoreCancelDiv = document.getElementById("templateStoreCancel");
templateStoreSaveDiv.addEventListener("click", function () {
    loader.style.display = 'block';

    gantt.ajax.post({
        url: "/scheduleGantt.saveAsTemplate.do",
        data: {
            studyId: studyId,
            templateName: document.getElementById('templateName').value,
            templateDesc: document.getElementById('templateDesc').value
        }
    }).then(function (response) {
        var res = JSON.parse(response.responseText);

        if (res && res.status == "200") {
            gantt.message("模板创建成功");
        }
        loader.style.display = 'none';
        templateStoreModal.style.display = 'none';
    });
});

templateStoreCancelDiv.addEventListener("click", function () {
    templateStoreModal.style.display = 'none';
});

function initialize() {
    gantt.ajax.post({
        url: "/scheduleGantt.getTemplate.do",
        data: {},
        callback: function (result) {
        }
    }).then(function (response) {

        var resTemplate = JSON.parse(response.responseText);
        const templateSelect = document.getElementById("template");
        for (let i = 0; i < resTemplate.length; i++) {
            const option = document.createElement("option");
            option.value = resTemplate[i]["key"];
            option.text = resTemplate[i]["value"];
            templateSelect.add(option);
        }

        loader.style.display = 'none';
    });

}

const initializeSaveDiv = document.getElementById("initializeSave");
const initializeCancelDiv = document.getElementById("initializeCancel");

initializeSaveDiv.addEventListener("click", function () {
    loader.style.display = 'block';

    gantt.ajax.post({
        url: "/scheduleGantt.generateTask.do",
        data: {
            studyId: studyId,
            templateId: document.getElementById('template').value,
            studyStratDate: document.getElementById('study_strat_date').value
        },
        callback: function (result) {
        }
    }).then(function (response) {

        initializeModal.style.display = "none";

        gantt.load("/scheduleGantt.getTaskList.do" + param, 'json')
            .then(function (xhr) {
                loader.style.display = 'none';
            });
    });

});

initializeCancelDiv.addEventListener("click", function () {
    initializeModal.style.display = 'none';
});


window.ganttModules = {};

function addClass(node, className) {
    if(node){
        node.classList.add(className);
    }
}

function removeClass(node, className) {
    if(node) {
        node.classList.remove(className);
    }
}

function getButton(name) {
    return document.querySelector(".actions [data-action='" + name + "']");
}

function highlightButton(name) {
    addClass(getButton(name), "menu-item-active");
}

function unhighlightButton(name) {
    removeClass(getButton(name), "menu-item-active");
}

function disableButton(name) {
    getButton(name).disabled = true;
}

function enableButton(name) {
    getButton(name).disabled = false;
}

function refreshUndoBtns() {
    if (!gantt.getUndoStack || !gantt.getUndoStack().length) {
        disableButton("undo");
    } else {
        enableButton("undo");
    }

    if (!gantt.getRedoStack || !gantt.getRedoStack().length) {
        disableButton("redo");
    } else {
        enableButton("redo");
    }
}

if (param.studyId) {
    setInterval(refreshUndoBtns, 1000);
}

let drawPlannedNode;
function toolbarMenu (action) {
    if (!action) return;
    const fn = toolbarMenu[action];
    fn && (fn instanceof Function) && fn();
}
toolbarMenu.undo = function () {
    gantt.undo();
    refreshUndoBtns();
}
toolbarMenu.redo = function () {
    gantt.redo();
    refreshUndoBtns();
}
toolbarMenu.zoomToFit = function () {
    ganttModules.zoomToFit.toggle();
    toggleCheckbox(zoomToFitCheckbox, config.zoom_to_fit);
    disableButtonWithScaleUnit();
    scaleComboElement.value = zoomConfig.levels[gantt.ext.zoom.getCurrentLevel()].name;
    toolbarMenu('today');
}
toolbarMenu.templateStore = function () {
    templateStoreModal.style.display = 'block';
}
toolbarMenu.fullscreen = function () {
    gantt.ext.fullscreen.toggle();
    config.fullscreen = !config.fullscreen;
    const elem = document.querySelector('.actions [data-action="fullscreen"]');
    if (config.fullscreen) {
        elem.innerHTML = '<img src="/cdtms/dhtmlxGantt/codebase/images/close.png"/>退出全屏';
    } else {
        elem.innerHTML = '<img src="/cdtms/dhtmlxGantt/codebase/images/open.png"/>全屏';
    }
}
toolbarMenu.collapseAll = function () {
    gantt.eachTask(function (task) {
        if (task.$level >= 1) {
            task.$open = false;
        }
    });
    gantt.render();
}
toolbarMenu.baseline = function () {
    if (!drawPlannedNode) {
        drawPlannedNode = gantt.addTaskLayer(draw_planned);
    } else {
        gantt.removeTaskLayer(drawPlannedNode);
        drawPlannedNode = null;
    }
    gantt.render();
}
toolbarMenu.expandAll = function () {
    gantt.eachTask(function (task) {
        // if(task.$level>=1) {}
        task.$open = true;
    });
    gantt.render();
}
toolbarMenu.toggleAutoScheduling = function () {
    gantt.config.auto_scheduling = !gantt.config.auto_scheduling;

    if (gantt.config.auto_scheduling) {

        gantt.config.auto_scheduling = function (task, predecessor) {
            return task.planned; // 仅计划中的任务自动计划
        };

        gantt.autoSchedule();
    }
}
toolbarMenu.toggleCriticalPath = function () {
    gantt.config.highlight_critical_path = !gantt.config.highlight_critical_path;

    gantt.render();
}
toolbarMenu.toggleShowColumns = function () {
    gantt.config.columns.forEach(col => {
        if (col.hide !== undefined) {
            col.hide = !col.hide;
        }
    })
    gantt.render();
}
toolbarMenu.today = function () {
    // 让时间线在展示区域的2/3处
    const __task_scroll_offset = gantt.config.task_scroll_offset;
    gantt.config.task_scroll_offset = gantt.$task.offsetWidth / 3 * 2;
    gantt.showDate(new Date());
    // 复原偏移量
    gantt.config.task_scroll_offset = __task_scroll_offset;
}
toolbarMenu.toStartTime = function () {
    const {start_date} = gantt.getSubtaskDates();
    gantt.showDate(start_date);
}

const zoomConfig = {
    levels: [
        {
            name: 'years',
            scales: [
                {
                    unit: 'year',
                    step: 1,
                    format: transitions.year
                }
            ],
            scale_height: 36,
            min_column_width: 50,
        },
        {
            name: 'quarters',
            scales: [
                {
                    unit: 'year',
                    format: transitions.year
                },
                {
                    unit: 'quarter',
                    step: 1,
                    format: date => `Q${Math.floor(date.getMonth() / 3) + 1}`,
                }
            ],
            scale_height: 50,
            min_column_width: 50,
        },
        {
            name: 'months',
            scales: [
                {
                    unit: 'year',
                    format: transitions.year,
                },
                {
                    unit: 'month',
                    format: '%M'
                }
            ],
            scale_height: 50,
            min_column_width: 50,
        },
        {
            name: 'weeks',
            scales: [
                {
                    unit: 'week',
                    step: 1,
                    format: date => {
                        const dateToStr = local === 'en' ? gantt.date.date_to_str('%d%M') : gantt.date.date_to_str('%M%d')
                        return [
                            dateToStr(date),
                            dateToStr(gantt.date.add(gantt.date.add(date, 1, 'week'), -1, 'day'))
                        ].join(' - ')
                    }
                },
            ],
            scale_height: 36,
            min_column_width: 120,
        },
        {
            name: 'days',
            scales: [
                {
                    unit: 'month',
                    format: local === 'en' ? '%M %Y' : '%Y年 %M',
                },
                {
                    unit: 'day',
                    format: '%d'
                }
            ],
            scale_height: 50,
            min_column_width: 50,
        }
    ]
}

gantt.ext.zoom.init(zoomConfig);

let zoomToFitMode = false;
ganttModules.zoomToFit = (function (gantt) {
    let cachedSettings = {};

    function saveConfig() {
        const config = gantt.config;
        cachedSettings = {};
        cachedSettings.scales = config.scales;
        cachedSettings.template = gantt.templates.date_scale;
        cachedSettings.start_date = config.start_date;
        cachedSettings.end_date = config.end_date;
        cachedSettings.zoom_level = gantt.ext.zoom.getCurrentLevel();
    }

    function restoreConfig() {
        applyConfig(cachedSettings, gantt.getSubtaskDates());
    }

    function applyConfig(config, dates) {
        if (config.scales[0].date) {
            gantt.templates.date_scale = null;
        } else {
            gantt.templates.date_scale = config.scales[0].template;
        }

        gantt.config.scales = config.scales;

        const unit = config.scales[config.scales.length - 1].unit;
        if (['year', 'quarter'].includes(unit)) {
            gantt.config.start_date = dates.start_date;
            gantt.config.end_date = dates.end_date;
        } else {
            gantt.config.start_date = gantt.config.end_date = null;
        }

        if (config.zoom_level !== undefined) {
            gantt.ext.zoom.setLevel(config.zoom_level);
        }
    }


    function zoomToFit() {
        const project = gantt.getSubtaskDates(),
            areaWidth = gantt.$task.offsetWidth;
        const scaleConfigs = zoomConfig.levels

        let zoomLevel = 0;
        for (let i = 0; i < scaleConfigs.length; i++) {
            zoomLevel = i;
            const level = scaleConfigs[i].scales;
            const lowestScale = level[level.length - 1]
            const columnCount = getUnitsBetween(project.start_date, project.end_date, lowestScale.unit, lowestScale.step || 1);
            if ((columnCount + 2) * gantt.config.min_column_width <= areaWidth) {
                break;
            }
        }

        if (zoomLevel == scaleConfigs.length) {
            zoomLevel--;
        }

        gantt.ext.zoom.setLevel(scaleConfigs[zoomLevel].name);
        applyConfig(scaleConfigs[zoomLevel], project);

        gantt.render();
    }

    // get number of columns in timeline
    function getUnitsBetween(from, to, unit, step) {
        let start = new Date(from),
            end = new Date(to);
        let units = 0;
        while (start.valueOf() < end.valueOf()) {
            units++;
            start = gantt.date.add(start, step, unit);
        }
        return units;
    }

    return {
        enable: function () {
            zoomToFitMode = true;
            saveConfig();
            zoomToFit();
            gantt.render();
        },
        toggle: function () {
            if (zoomToFitMode) {
                this.disable();
            } else {
                this.enable();
            }
        },
        disable: function () {
            zoomToFitMode = false;
            restoreConfig();
            gantt.render();
        }
    };
})(gantt);


gantt.templates.grid_row_class = function (start, end, task) {
    return gantt.hasChild(task.id) ? "gantt_parent_row" : "";
};
gantt.templates.scale_row_class = function (scale) {
    if (scale.index === 0 && ['year', 'month'].includes(scale.unit)) {
        return 'gantt_scale_year';
    } else {
        return '';
    }
}

const font_width_ratio = 20;

// gantt.templates.progress_text = function (start, end, task) {
//     return "<span style='text-align:left;'>" + Math.round(task.progress * 100) + "% </span>";
// };

// gantt.templates.leftside_text = function leftSideTextTemplate(start, end, task) {
//     if (getTaskFitValue(task) === "left") {
//         return task.text;
//     }
//     return "";
// };
//
// gantt.templates.rightside_text = function rightSideTextTemplate(start, end, task) {
//     if (getTaskFitValue(task) === "right") {
//         return task.text;
//     }
//     return "";
// };
//
// gantt.templates.task_text = function taskTextTemplate(start, end, task) {
//     if (getTaskFitValue(task) === "center") {
//         return task.text;
//     }
//     return "";
// };

gantt.templates.task_text = function taskTextTemplate(start, end, task) {
    if (task.type !== 'task') return '';
    // 单项目
    if (studyId) return '';
    // 多项目
    switch (Number(task.status)) {
        case 1: {
            // 进行中
            const width = gantt.posFromDate(new Date()) - gantt.posFromDate(start);
            return `<div class="multiple-task" style="width: ${width}px"></div>`;
        }
        case 3: {
            // 已延期
            const width = gantt.posFromDate(new Date(task.actual_end_date)) - gantt.posFromDate(start);
            return `<div class="multiple-task" style="width: ${width}px;"></div>`;
        }
        case 0:
        case 2:
        default:
            return '';
    }
};

function getTaskFitValue(task) {
    let taskStartPos = gantt.posFromDate(task.start_date),
        taskEndPos = gantt.posFromDate(task.end_date);

    const width = taskEndPos - taskStartPos;
    const textWidth = (task.text || "").length * font_width_ratio + 40;

    if (width < textWidth) {
        let ganttLastDate = gantt.getState().max_date;
        let ganttEndPos = gantt.posFromDate(ganttLastDate);
        if (ganttEndPos - taskEndPos < textWidth) {
            return "left"
        } else {
            return "right"
        }
    } else {
        return "center";
    }
}

gantt.attachEvent("onAfterTaskUpdate", function (id, task) {
    parentProgress(id)
    if(task.baseline_duration && task.planned_duration>task.baseline_duration){
        gantt.message({text: "您刚刚修改的"+task.name+"超出了标准时间，这只是一个提示，请您关注！", expire: -1});
    }
});

gantt.attachEvent("onBeforeTaskDrag", function (id, mode, e) {
    var task = gantt.getTask(id);
    // 如果任务的进度大于 0，则禁止拖动任务
    if (task.progress > 0 && mode != "progress") {
        return false;
    }
    // 允许拖动任务
    return true;
});

gantt.attachEvent("onTaskDrag", function (id, mode, task, original) {
    if (mode == "progress") {
        parentProgress(id)
    }
});

gantt.attachEvent("onAfterTaskAdd", function (id) {
    parentProgress(id)
});

gantt.attachEvent("onAfterTaskDelete", function (id, task) {
    if (task.parent) {
        const siblings = gantt.getChildren(task.parent);
        if (siblings.length) {
            parentProgress(siblings[0])
        }
    }
});

function parentProgress(id) {
    gantt.eachParent(function (task) {
        const children = gantt.getChildren(task.id);
        let childProgress = 0;
        for (let i = 0; i < children.length; i++) {
            const child = gantt.getTask(children[i])
            childProgress += (child.progress * 100);
        }
        task.progress = childProgress / children.length / 100;
        gantt.updateTask(task.id);

    }, id)
    gantt.render();
}


gantt.config.sort = true;
gantt.i18n.setLocale("cn");
gantt.config.static_background = true;

gantt.plugins({
    marker: true,
    fullscreen: true,
    critical_path: true,
    auto_scheduling: true,
    tooltip: true,
    undo: true,
    drag_timeline: true,
    grouping: true,
    multiselect: true

});

const date_to_str = gantt.date.date_to_str(gantt.config.task_date);
const today = new Date();
gantt.addMarker({
    start_date: today,
    css: "today",
    text: "Today",
    title: "Today: " + date_to_str(today)
});

// scrollTo
// const start = new Date(2019, 9, 28);
// gantt.addMarker({
//     start_date: start,
//     css: "status_line",
//     text: "Start project",
//     title: "Start project: " + date_to_str(start)
// });

gantt.attachEvent("onTaskCreated", function (item) {
    if (item.duration == 1) {
        item.duration = 8 * 3 * 60;
    }
    return true;
});

gantt.ext.fullscreen.getFullscreenElement = function () {
    return document.querySelector(".main-container");
};

const durationFormatter = gantt.ext.formatters.durationFormatter({
    enter: "day",
    store: "minute",
    format: "day",
    short: true,
    hoursPerDay: 24,
});

function byId(list, id) {
    for (var i = 0; i < list.length; i++) {
        if (list[i].key == id)
            return list[i].label || "";
    }
    return "";
}

const linksFormatter = gantt.ext.formatters.linkFormatter({durationFormatter: durationFormatter});

const columns = [
    /* {
        name: "",
        width: 15,
        resize: false,
        template (task) {
            return "<span class='gantt_grid_wbs'>" + gantt.getWBSCode(task) + "</span>"
        }
    }, */
    {
        name: "text",
        tree: true,
        label: transitions.taskName,
        width: 200,
        min_width: 200,
        max_width: 200,
        template: function (task) {
            return `<span class="task" onclick="handleTaskGridClick(event)" data-studycode="${task.studyCode}" data-menuposition="${task.menuPosition}">${task.text || ""}</span>`;
        }
    },
    {
        name: "owners",
        label: transitions.executor,
        width: 140,
        min_width: 140,
        max_width: 140,
        align: "left",
        hide: false,
        template (task) {
            if (task.type === gantt.config.types.project) return;
            if (!task.owner) return;

            const owners = task.owner.split(',').map(ownerId => byId(gantt.serverList(task.studyCode + "_UserOptions"), ownerId)).filter(Boolean);

            return owners.join(';');
        },
    },
    {
        name: "planned_start_date",
        label: transitions.planstart,
        align: "left",
        width: 100,
        min_width: 100,
        max_width: 100,
        hide: false,
    },
    {
        name: "duration_formatted",
        label: transitions.duration,
        align: "right",
        width: 80,
        min_width: 80,
        max_width: 80,
        hide: false,
        template: function (task) {
            if (!task.planned_duration && task.planned_duration !== 0) return '';
            return durationFormatter.format(task.planned_duration);
        }
    },
    {
        name: "actual_start_date",
        label: transitions.startdate,
        align: "left",
        width: 100,
        min_width: 100,
        max_width: 100,
        hide: false,
    },
    {
        name: "actual_end_date",
        label: transitions.enddate,
        align: "left",
        width: 100,
        min_width: 100,
        max_width: 100,
        hide: false,
    },
];

//if(studyId){
    columns.push(
        {
            name: "actions",
            label: "",
            width: 40,
            min_width: 40,
            max_width: 40,
            sort: false,
            hide: false,
            template: function (task) {
                // console.log(JSON.stringify(task));
                // return "<input type=button value='V' onclick=clone_task(" + task.id + ")>";
                // return "<div role='button' width=30 aria-label='复制' class='gantt_clone' onclick='clone_task("+ task.id + ")';></div>";
                // return "<input type=button value='V' onclick=clone_task(" + task.id + ")>"
                if (task.$level >= 2) return "<div role='button' width=30 aria-label='" + transitions.copy + task.$level + "' class='gantt_clone' onclick='cloneTaskTask(" + task.id + ")';></div>";
            }
        },
        {
            name:"add",
            sort: false,
            width: 40,
            min_width: 40,
            max_width: 40,
            label: "",
            hide: false,
        }

    );
//}

gantt.config.columns = columns;

function draw_planned(task) {
    function _renderPlanned (tasks) {
        const pDom = document.createElement('div');
        let hasDom = false;
        [].concat(tasks).forEach(function (task) {
            if (task.planned_start_date && task.planned_end_date) {
                hasDom = true;
                const {top, left, width, height, rowHeight} = gantt.getTaskPosition(task, task.planned_start_date, task.planned_end_date);
                const baseTop = top + (rowHeight - height) / 2;
                const cDom = document.createElement('div');
                cDom.dataset.task_id = task.id;
                cDom.style.left = left + 'px';
                cDom.style.top = baseTop + 'px';
                if (task.type === 'milestone') {
                    cDom.className = 'baseline tooltip-reference baseline-milestone';
                    cDom.style.width = gantt.config.bar_height + 'px';
                    cDom.style.height = gantt.config.bar_height + 'px';
                } else {
                    cDom.className = 'baseline tooltip-reference';
                    cDom.style.width = width + 'px';
                    cDom.style.height = height + 'px';
                }
                pDom.appendChild(cDom);
            }
        })
        
        if (hasDom) return pDom;
        return false;
    }

    // 任务展示计划开始计划结束
    // 分段任务展示父任务计划开始计划结束
    if (task.type === 'task' || task.render === 'split' || task.type === 'milestone') {
        return _renderPlanned(task);
    }
}

gantt.templates.task_class = function(start, end, task) {
    if (task.type === 'task') {
        return [
            'future-task hidden-task',
            'doing-task',
            'completed-task',
            'overdue-task hidden-task',
        ][task.status];
    } else if (task.type === 'milestone') {
        if (!task.actual_start_date) return 'hidden-task'
    }
};

var child_links;
var clone_original_ids_table;

function obtain_link_ids(id) {
    var task = gantt.getTask(id);
    var source_links = task.$source;
    for (var i = 0; i < source_links.length; i++) {
        child_links.push(source_links[i]);
    }
}

function create_clone_original_ids_table(original_id, clone_id) {
    clone_original_ids_table[original_id] = clone_id;
}

function clone_child_links() {

    for (var i = 0; i < child_links.length; i++) {
        var link = gantt.getLink(child_links[i]);

        if (clone_original_ids_table[link.source] && clone_original_ids_table[link.target]) {

            var clone_link = {};
            clone_link.id = gantt.uid();
            clone_link.target = clone_original_ids_table[link.target];
            clone_link.source = clone_original_ids_table[link.source];
            clone_link.type = link.type;
            clone_link.lag = link.lag;
            gantt.addLink(clone_link)
        }
    }
}

//
// gantt.attachEvent("onTaskIdChange", async function (oldId, newId) {
//     console.log("触发了" + oldId + "——" + newId);
// });


function getTaskCount(id) {

    if (!gantt.hasChild(id)) {
        return 1;
    } else {
        let children = gantt.getChildren(id);
        var count = 0;
        children.map(child => count = count + getTaskCount(child))
        return count;
    }
}

let subTaskCount;

async function cloneTaskTask(id) {
    subTaskCount = getTaskCount(id) + 1
    child_links = [];
    clone_original_ids_table = {};
    const task = gantt.getTask(id);
    // console.log("task" + getTaskCount(id));
    //
    // console.log(getTaskCount(id)+1);
    clone_task(id, task.parent);
}

async function clone_task(id, newParentid) {
    // 执行异步操作，并在异步操作完成后触发 onTaskIdChange 事件
    const task = gantt.getTask(id);
    const clone = gantt.copy(task);
    clone.id = gantt.uid();
    clone.progress = 0;
    clone.parent = newParentid;
    clone.type = clone.type != "milestone" ? "task" : "milestone";
    let newTaskId = await gantt.addTask(clone, clone.parent, clone.$index);
    obtain_link_ids(id);
    create_clone_original_ids_table(id, newTaskId);

    if (subTaskCount == Object.keys(clone_original_ids_table).length) {
        clone_child_links();
    }
    if (!gantt.hasChild(id)) {
        return;
    }

    let children = gantt.getChildren(id);
    // 如果当前节点有子节点，递归调用子节点的处理函数
    children.map(child => clone_task(child, newTaskId))
}

gantt.templates.lightbox_header = function (start, end, task) {
    var taskNumber = gantt.getTaskIndex(task.id);
    return "任务 #" + taskNumber + ": " + task.text || "";
};

gantt.locale.labels.section_studyCode = "项目";
gantt.locale.labels.section_name = "名称";
gantt.locale.labels.section_owner = "负责人";
gantt.locale.labels.section_description = "说明";
gantt.locale.labels.section_baseline = "计划时间";
gantt.locale.labels.section_actualDate = "实际时间";
gantt.locale.labels.section_progress = "进度";
gantt.locale.labels.section_is_cycle = "周期任务";
gantt.locale.labels.section_cycle = "周期说明";

gantt.attachEvent("onBeforeLightbox", function (task_id) {
    var task = gantt.getTask(task_id);
    gantt.updateCollection("UserOptions", gantt.serverList(task.studyCode + "_UserOptions"));
    return true;
});


gantt.attachEvent("onLightbox", function (id) {
    gantt.getLightboxSection("studyCode").control.disabled = true;
    var task = gantt.getTask(id);

    if (task.type == 'task') {
        var actualDateField = gantt.getLightboxSection("actualDate").node;

        if (task.progress === 0) {
            // 获取对应的字段节点
            actualDateField.previousSibling.style.display = "none";
            // 隐藏字段节点
            actualDateField.style.display = "none";
        }

        if (task.progress === 0) {
            gantt.getLightboxSection("progress").setValue("0");
        }

        var progressSelect = gantt.getLightboxSection("progress").control;

        // 监听进度下拉选项的变化事件
        progressSelect.addEventListener("change", function (event) {
            var newValue = event.target.value;

            if(newValue!=0){
                actualDateField.previousSibling.style.display = "";

                actualDateField.style.display = "";
            } else {
                // 获取对应的字段节点
                actualDateField.previousSibling.style.display = "none";
                // 隐藏字段节点
                actualDateField.style.display = "none";
            }
            // 在这里添加处理代码，当进度变化时触发
            // newValue 是新选择的进度值
            // 在这里可以取消 display = "none" 或执行其他操作
        });

        // 在灯箱关闭时，取消对进度下拉选项的事件监听
        gantt.attachEvent("onAfterLightbox", function () {
            progressSelect.removeEventListener("change");
        });
    }
});

gantt.attachEvent("onLightboxSave", function (id, task, is_new) {
    if (task.progress == 0) {
        task.start_date = task.planned_start_date;
        task.end_date = task.planned_end_date;
        task.duration = task.planned_duration;
        task.actual_start_date=null;
        task.actual_end_date = null;
    }else{
        task.start_date = task.actual_start_date;
        task.end_date = task.actual_end_date;
        task.duration = task.actual_duration;

    }

    return true;
})

gantt.attachEvent("onTaskCreated", function (task) {
    var parentId = task.parent;
    if (parentId) {
        var parent = gantt.getTask(parentId);
        task.owner = parent.owner;
        task.studyCode = parent.studyCode;
        task.text = "";
        task.progress = 0;
    }
    return true;
});

function endPopup() {
    modal = null;
    editLinkId = null;
}

function cancelEditLink() {
    endPopup()
}

function deleteLink() {
    gantt.deleteLink(editLinkId);
    endPopup()
}

function saveLink() {
    var link = gantt.getLink(editLinkId);

    var lagValue = durationFormatter.parse(modal.querySelector(".lag-input").value);
    if (!isNaN(parseInt(lagValue, 10))) {
        link.lag = parseInt(lagValue, 10);
    }

    gantt.updateLink(link.id);
    if (gantt.autoSchedule) {
        gantt.autoSchedule(link.source);
    }
    endPopup();
}

var modal;
var editLinkId;
gantt.attachEvent("onLinkDblClick", function (id, e) {
    editLinkId = id;
    var link = gantt.getLink(id);
    var linkTitle;
    switch (link.type) {
        case gantt.config.links.finish_to_start:
            linkTitle = "FS";
            break;
        case gantt.config.links.finish_to_finish:
            linkTitle = "FF";
            break;
        case gantt.config.links.start_to_start:
            linkTitle = "SS";
            break;
        case gantt.config.links.start_to_finish:
            linkTitle = "SF";
            break;
    }

    linkTitle += " " + gantt.getTask(link.source).text || "" + " -> " + gantt.getTask(link.target).text || "";

    modal = gantt.modalbox({
        title: linkTitle,
        text: "<div>" +
            "<label>滞后 <input type='text' class='lag-input' /></label>" +
            "</div>",
        buttons: [
            {label: "保存", css: "link-save-btn", value: "save"},
            {label: "取消", css: "link-cancel-btn", value: "cancel"},
            {label: "删除", css: "link-delete-btn", value: "delete"}
        ],
        width: "500px",
        type: "popup-css-class-here",
        callback: function (result) {
            switch (result) {
                case "save":
                    saveLink();
                    break;
                case "cancel":
                    cancelEditLink();
                    break;

                case "delete":
                    deleteLink();
                    break;
            }
        }
    });
    modal.querySelector(".lag-input").value = link.lag ? durationFormatter.format(link.lag) : '0d';

    //any custom logic here
    return false;
});


gantt.attachEvent("onBeforeTaskUpdate", function (id, task) {
    if (task.progress == 0) {
        task.planned_start_date = task.start_date;
        task.planned_end_date = task.end_date;
        task.planned_duration = task.duration;
        task.actual_start_date=null;
        task.actual_end_date = null;
    } else {
        task.start_date = task.actual_start_date;

        if(task.actual_end_date){
            task.end_date = task.actual_end_date;
        }
    }
    return true;
});

gantt.attachEvent("onBeforeTaskAdd", function (id, task) {
    if (task.progress == 0) {
        task.start_date = task.planned_start_date;
        task.end_date = task.planned_end_date;
        task.duration = task.planned_duration;
        task.actual_start_date=null;
        task.actual_end_date = null;

    } else {
        task.start_date = task.actual_start_date;
        if(task.actual_end_date){
            task.end_date = task.actual_end_date;
        }else{
            task.duration = task.planned_duration;
        }
    }
    return true;
});

gantt.form_blocks["multiselect"] = {
    render: function (sns) {
        var height = (sns.height || "23") + "px";
        var html = "<div class='gantt_cal_ltext gantt_cal_chosen gantt_cal_multiselect' style='height:" + height + ";'><select data-placeholder='...' class='chosen-select' multiple>";
        if (sns.options) {
            for (var i = 0; i < sns.options.length; i++) {
                if (sns.unassigned_value !== undefined && sns.options[i].key == sns.unassigned_value) {
                    continue;
                }
                html += "<option value='" + sns.options[i].key + "'>" + sns.options[i].label + "</option>";
            }
        }
        html += "</select></div>";
        return html;
    },

    set_value: function (node, value, ev, sns) {
        node.style.overflow = "visible";
        node.parentNode.style.overflow = "visible";
        node.style.display = "inline-block";
        var select = $(node.firstChild);

        if (value) {
            value = (value + "").split(",");
            select.val(value);
        } else {
            select.val([]);
        }

        select.chosen();
        if (sns.onchange) {
            select.change(function () {
                sns.onchange.call(this);
            })
        }
        select.trigger('chosen:updated');
        select.trigger("change");
    },

    get_value: function (node, ev) {
        var value = $(node.firstChild).val();
        return value.join(",");
    },

    focus: function (node) {
        $(node.firstChild).focus();
    }
};

gantt.config.lightbox.sections = [
    {name: "studyCode", map_to: "studyCode", type: "select", options: gantt.serverList("StudyOptions"), readonly: true},
    // {name: "studyCode", map_to: "studyCode", type: "template"},
    {name: "name", height: 36, map_to: "text", type: "textarea", focus: true},
    {name: "owner", type: "multiselect", map_to: "owner", options: gantt.serverList("UserOptions")},
    {
        name: "type", type: "typeselect", map_to: "type", lightbox: function (name, value) {
            return !!(value != gantt.config.types.project);
        }
    },
    {
        name: "baseline",
        map_to: {start_date: "planned_start_date", end_date: "planned_end_date", duration: "planned_duration"},
        time_format: ["%Y", "%m", "%d", "%H:%i"],
        formatter: durationFormatter,
        type: "duration"
    },

    {
        name: "progress", height: 22, map_to: "progress", type: "select", options: [
            {key: "0", label: "未开始"},
            {key: "0.1", label: "10%"},
            {key: "0.2", label: "20%"},
            {key: "0.3", label: "30%"},
            {key: "0.4", label: "40%"},
            {key: "0.5", label: "50%"},
            {key: "0.6", label: "60%"},
            {key: "0.7", label: "70%"},
            {key: "0.8", label: "80%"},
            {key: "0.9", label: "90%"},
            {key: "1", label: "完成"}
        ], default_value: "0"
    },


    {
        name: "actualDate",
        type: "time",
        time_format: ["%Y", "%m", "%d", "%H:%i"],
        map_to: {start_date: "actual_start_date", end_date: "actual_end_date"},
        formatter: durationFormatter
    }

    // { name: "time", type: "duration", map_to: "auto",time_format:["%Y","%m","%d","%H:%i"], formatter: durationFormatter }
];
gantt.config.lightbox.project_sections = [
    {name: "studyCode", map_to: "studyCode", type: "select", options: gantt.serverList("StudyOptions")},
    {name: "name", height: 36, map_to: "text", type: "textarea", focus: true},
    {name: "description", height: 80, map_to: "desc", type: "textarea"},
    {
        name: "baseline",
        map_to: {start_date: "planned_start_date", end_date: "planned_end_date", duration: "planned_duration"},
        time_format: ["%Y", "%m", "%d", "%H:%i"],

        formatter: durationFormatter,
        type: "duration"
    },{
        name: "progress", height: 22, map_to: "progress", type: "select", options: [
            {key: "0", label: "未开始"},
            {key: "0.1", label: "10%"},
            {key: "0.2", label: "20%"},
            {key: "0.3", label: "30%"},
            {key: "0.4", label: "40%"},
            {key: "0.5", label: "50%"},
            {key: "0.6", label: "60%"},
            {key: "0.7", label: "70%"},
            {key: "0.8", label: "80%"},
            {key: "0.9", label: "90%"},
            {key: "1", label: "完成"}
        ], default_value: "0"
    },
    {
        name: "actualDate",
        type: "duration",
        time_format: ["%Y", "%m", "%d", "%H:%i"],
        map_to: {start_date: "actual_start_date", end_date: "actual_end_date", duration: "actual_duration"},
        formatter: durationFormatter
    }
];

gantt.config.types["cycle"] = "cycle";
gantt.locale.labels["type_cycle"] = "周期任务";

gantt.config.lightbox["cycle_sections"] = [
    {name: "studyCode", map_to: "studyCode", type: "select", options: gantt.serverList("StudyOptions")},
    {name: "name", height: 36, map_to: "text", type: "textarea", focus: true},
    {name: "type", type: "typeselect", map_to: "type", lightbox: function (name, value) {
            return !!(value != gantt.config.types.project);
        }
    },
    {name: "cycle", height: 36, map_to: "cycle", type: "textarea"},
    {name: "owner", type: "multiselect", map_to: "owner", options: gantt.serverList("UserOptions")},
    {name: "description", height: 80, map_to: "desc", type: "textarea"},
    {
        name: "baseline",
        map_to: {start_date: "planned_start_date", end_date: "planned_end_date", duration: "planned_duration"},
        time_format: ["%Y", "%m", "%d", "%H:%i"],

        formatter: durationFormatter,
        type: "duration"
    },
    {
        name: "progress", height: 22, map_to: "progress", type: "select", options: [
            {key: "0", label: "未开始"},
            {key: "0.1", label: "10%"},
            {key: "0.2", label: "20%"},
            {key: "0.3", label: "30%"},
            {key: "0.4", label: "40%"},
            {key: "0.5", label: "50%"},
            {key: "0.6", label: "60%"},
            {key: "0.7", label: "70%"},
            {key: "0.8", label: "80%"},
            {key: "0.9", label: "90%"},
            {key: "1", label: "完成"}
        ], default_value: "0"
    },
    {
        name: "actualDate",
        type: "duration",
        time_format: ["%Y", "%m", "%d", "%H:%i"],
        map_to: {start_date: "actual_start_date", end_date: "actual_end_date", duration: "actual_duration"},
        formatter: durationFormatter
    }
];

gantt.config.lightbox.milestone_sections = [
    {name: "studyCode", map_to: "studyCode", type: "select", options: gantt.serverList("StudyOptions")},
    {name: "name", height: 36, map_to: "text", type: "textarea", focus: true},
    {name: "description", height: 36, map_to: "desc", type: "textarea"},
    {
        name: "type", type: "typeselect", map_to: "type", filter: function (name, value) {
            return !!(value != gantt.config.types.project);
        }
    },
    {
        name: "baseline",
        map_to: {start_date: "planned_start_date", end_date: "planned_end_date", duration: "planned_duration"},
        time_format: ["%Y", "%m", "%d", "%H:%i"],

        formatter: durationFormatter,
        type: "duration_optional"
    },
    {
        name: "progress", height: 22, map_to: "progress", type: "select", options: [
            {key: "0", label: "未开始"},
            {key: "0.1", label: "10%"},
            {key: "0.2", label: "20%"},
            {key: "0.3", label: "30%"},
            {key: "0.4", label: "40%"},
            {key: "0.5", label: "50%"},
            {key: "0.6", label: "60%"},
            {key: "0.7", label: "70%"},
            {key: "0.8", label: "80%"},
            {key: "0.9", label: "90%"},
            {key: "1", label: "完成"}
        ], default_value: "0"
    },

    {
        name: "actualDate",
        type: "duration",
        time_format: ["%Y", "%m", "%d", "%H:%i"],
        map_to: {start_date: "actual_start_date", end_date: "actual_end_date", duration: "actual_duration"},
        formatter: durationFormatter
    }
];

//Make resize marker for two columns

gantt.attachEvent("onColumnResizeStart", function (ind, column) {
    if (!column.tree || ind == 0) return;

    setTimeout(function () {
        const marker = document.querySelector(".gantt_grid_resize_area");
        if (!marker) return;
        const cols = gantt.getGridColumns();
        const delta = cols[ind - 1].width || 0;
        if (!delta) return;
        marker.style.boxSizing = "content-box";
        marker.style.marginLeft = -delta + "px";
        marker.style.paddingRight = delta + "px";
    }, 1);
});

gantt.templates.tooltip_text = function (start, end, task) {
    return renderTooltip(task);
};

gantt.templates.timeline_cell_class = function (item, date) {
    const {unit} = gantt.getScale();
    if (gantt.isWorkTime({date, unit})) return '';
    else return 'week_end';
};

gantt.config.date_format = "%Y-%m-%d %H:%i";
gantt.config.duration_unit = "minute";
gantt.config.row_height = 36;
gantt.config.bar_height = 24;
gantt.config.order_branch = "marker";
gantt.config.order_branch_free = true;
gantt.config.grid_resize = true;
gantt.config.link_line_width = 1;
gantt.config.drag_project = false;
gantt.config.drag_progress = false;
gantt.config.drag_resize = false;
gantt.config.drag_links = false;
gantt.config.drag_move = false;
gantt.config.reorder_grid_columns = false;

gantt.config.auto_scheduling_strict = true;
gantt.config.auto_scheduling_use_progress = true;

gantt.config.drag_timeline = {
    ignore: ".gantt_task_line, .gantt_task_link",
    useKey: false
};

gantt.config.work_time = true;  // 非工作日特殊显示
gantt.config.correct_work_time = false; // 自动修改非工作日内的任务
gantt.config.task_scroll_offset = 100; // 设置时间线的偏移量 默认值100
gantt.config.show_tasks_outside_timescale = true; // 显示时间范围外的任务

// gantt.setWorkTime({date: new Date(), hours: false});
// gantt.config.skip_off_time = true;    // hides non-working time in the chart
// gantt.setWorkTime({ hours:["9:00-18:00"] });
// gantt.addCalendar({
//     id: "custom",
//     worktime: {
//         hours: [8, 17],
//         days: [1, 1, 1, 1, 1, 1, 1]
//     }
// });

gantt.event(document.querySelector(".actions"), "click", function (e) {
    let target = e.target || e.srcElement;

    while (!target.hasAttribute("data-action") && target !== document.body) {
        target = target.parentNode;
    }

    if (target && target.hasAttribute("data-action")) {
        toolbarMenu(target.getAttribute("data-action"))
    }
});

let config = {
    collapse: false,
    baseline: true,
    auto_scheduling: false,
    critical_path: false,
    zoom_to_fit: false,
    simpleTable: true,
    fullscreen: false,
};

function toggleCheckbox(checkbox, state, disabled) {
    state
        ? checkbox.classList.add("checked")
        : checkbox.classList.remove("checked");
    disabled
        ? checkbox.classList.add("disabled")
        : checkbox.classList.remove("disabled");
}

let
    collapseCheckbox = document.querySelector("#collapse"),
    simpleTable = document.getElementById('simple-table'),
    baselineCheckbox = document.querySelector("#baseline"),
    // autoSchedulingCheckbox = document.querySelector("#auto-scheduling"),
    autoSchedulingCheckbox  ,
    criticalPathCheckbox = document.querySelector("#critical-path"),
    zoomToFitCheckbox = document.querySelector("#zoom-to-fit"),
    scaleComboElement = document.getElementById("scale_combo");
    // tasksGroupingElement = document.getElementById("tasks_grouping");

if (baselineCheckbox) {
    toolbarMenu("baseline");
    toggleCheckbox(baselineCheckbox, config.baseline);
    baselineCheckbox.addEventListener("click", function () {
        config.baseline = !config.baseline;
        toolbarMenu("baseline");
        toggleCheckbox(baselineCheckbox, config.baseline);
    });
}

if (collapseCheckbox) {
    collapseCheckbox.addEventListener("click", function () {
        config.collapse = !config.collapse;
        toggleCheckbox(collapseCheckbox, config.collapse);
        if (config.collapse) {
            toolbarMenu("collapseAll")
        } else {
            toolbarMenu("expandAll")
        }    
    });
}



if(autoSchedulingCheckbox){
    autoSchedulingCheckbox.addEventListener("click", function () {
        config.auto_scheduling = !config.auto_scheduling;
        toggleCheckbox(autoSchedulingCheckbox, config.auto_scheduling);
        toolbarMenu("toggleAutoScheduling")
    });
}

if(criticalPathCheckbox){
    criticalPathCheckbox.addEventListener("click", function () {
        config.critical_path = !config.critical_path;
        toggleCheckbox(criticalPathCheckbox, config.critical_path);
        toolbarMenu("toggleCriticalPath")
    });
}
if(simpleTable){
    toggleCheckbox(simpleTable, config.simpleTable);
    toolbarMenu("toggleShowColumns");
    simpleTable.addEventListener("click", function () {
        config.simpleTable = !config.simpleTable;
        toggleCheckbox(simpleTable, config.simpleTable);
        toolbarMenu("toggleShowColumns");
    });
}

zoomToFitCheckbox.addEventListener("click", function () {
    config.zoom_to_fit = !config.zoom_to_fit;
    toggleCheckbox(zoomToFitCheckbox, config.zoom_to_fit);
    toolbarMenu("zoomToFit");
});

scaleComboElement.addEventListener("change", function () {
    handleLevelChange(this.value);
    config.zoom_to_fit = zoomToFitMode = false;
    toggleCheckbox(zoomToFitCheckbox, config.zoom_to_fit);
    toolbarMenu('today');
});

function showGroups(listname) {
    var property = listname;
    if (listname) {

        if (listname == 'owner') {
            listname = studyId ? studyId + '_UserOptions' : "allUserOptions";
        }

        if (listname == 'eventType') {
            listname = "eventTypeOpts";

        }

        gantt.groupBy({
            groups: gantt.serverList(listname),
            relation_property: property,
            group_id: "key",
            group_text: "label"
        });
    } else {
        gantt.groupBy(false);

    }
}


// tasksGroupingElement.addEventListener("change", function () {
//     let groupValue = this.value;
//     showGroups(groupValue)
//
// });


gantt.attachEvent("onBeforeTaskAutoSchedule", function (task, start, link, predecessor) {
    if (task.progress > 0) {
        return false;
    }
    return true;
});


gantt.attachEvent("onTaskLoading", function (task) {
    task.planned_start_date = task.planned_start_date?gantt.date.parseDate(task.planned_start_date, "xml_date"):null;
    task.planned_end_date = task.planned_end_date?gantt.date.parseDate(task.planned_end_date, "xml_date"):null;
    task.actual_start_date = task.actual_start_date?gantt.date.parseDate(task.actual_start_date, "xml_date"):null;
    task.actual_end_date =task.actual_end_date?gantt.date.parseDate(task.actual_end_date, "xml_date"):null;
    return true;
});

gantt.init("gantt_here");
gantt.i18n.setLocale(local);

gantt.load("/scheduleGantt.getTaskList.do" + param, 'json', function () {
    loader.style.display = 'none';
}).then(function (xhr) {
    handleLevelChange(GANTT_DEFAULT_LEVEL)
    toolbarMenu.today();
    if (studyId && gantt.getTaskCount() == 0) {
        initializeModal.style.display = "block"
        loader.style.display = "block";
        initialize();
    }
    initGanttScroll();
});

gantt.addTaskLayer(function (task) {
    function _renderFragment (tasks) {
        const pDom = document.createElement('div');
        let hasDom = false;
        [].concat(tasks).forEach(function (task) {
            if (task.marks && task.marks.length) {
                hasDom = true;
                task.marks.forEach(function (mark) {
                    const cDom = document.createElement('div');
                    const markDate = new Date(mark.date);
                    const {top, left} = gantt.getTaskPosition(task, markDate, markDate);
                    cDom.className = 'fragment-mark tooltip-reference';
                    cDom.dataset.actual_end_date = mark.date;
                    cDom.dataset.text = mark.text;
                    cDom.style.top = top + gantt.config.row_height / 2;
                    cDom.style.left = left;
                    cDom.classList.add('fragment-mark');
                    const markClass = {
                        0: 'fragment-mark--5',
                        1: 'fragment-mark--1',
                        2: 'fragment-mark--2',
                        3: 'fragment-mark--3',
                        4: 'fragment-mark--4',
                    }[mark.type] || 'fragment-mark--5';
                    cDom.classList.add(markClass);                    
                    pDom.appendChild(cDom);
                })
            }
        })
        
        if (hasDom) return pDom;
        return false;
    }

    if (task.render === 'split') {
        const items = gantt.getChildren(task.id);
        return _renderFragment(items.map(id => gantt.getTask(id)));
    } else if (task.type === 'task') {
        return _renderFragment(task);
    }
})

gantt.ext.tooltips.tooltipFor({
    selector: '.tooltip-reference',
    html: function (event, dom) {
        if (dom.dataset.task_id) {
            return renderTooltip(gantt.getTask(dom.dataset.task_id));
        } else {
            return renderTooltip({
                text: dom.dataset.text,
                actual_end_date: new Date(dom.dataset.actual_end_date),
            });
        }
    }
})

var dp = gantt.createDataProcessor({url: "/scheduleGantt.callback.do", mode: "POST"});
dp.init(gantt);


dp.deleteAfterConfirmation = true;
dp.attachEvent("onAfterUpdate", function (id, action, tid, response) {
    
    debugger;
    // if(action=="inserted"){
    //     // console.log(tid);
    //     // console.log(id);
    //     // gantt.changeTaskId(id, tid);
    //     // taskIdChangedMap.set(id,tid);
    // }
    // if(action=="inserted" && response["ganttMode"]=="links"){
    //     gantt.changeLinkId(id, tid);
    // }

    if (action == "error" || response.reload) {
    gantt.clearAll();
    gantt.load('/scheduleGantt.getTaskList.do' + param, 'json');
    }
});

/**
 * 渲染悬浮提示框
 * @param {Task} task 任务信息
 * @returns 
 */
function renderTooltip (task) {
    const labels = [];
    if (task.$target) {
        const links = task.$target;
        for (let i = 0; i < links.length; i++) {
            const link = gantt.getLink(links[i]);
            labels.push(linksFormatter.format(link));
        }
    }
    const predecessors = labels.join(", ");
    const list = [];
    if (task.planned_start_date && (task.planned_start_date !== 'undefined' || task.planned_start_date !== 'null')) list.push(['计划开始日期', gantt.templates.tooltip_date_format(task.planned_start_date)]);
    if (task.planned_end_date && (task.planned_end_date !== 'undefined' || task.planned_end_date !== 'null')) list.push(['计划完成日期', gantt.templates.tooltip_date_format(task.planned_end_date)]);
    if (task.planned_duration && (task.planned_duration !== 'undefined' || task.planned_duration !== 'null')) list.push(['计划时长', durationFormatter.format(task.planned_duration)]);
    if (task.actual_start_date && (task.actual_start_date !== 'undefined' || task.actual_start_date !== 'null')) list.push(['实际开始日期', gantt.templates.tooltip_date_format(task.actual_start_date)]);
    if (task.actual_end_date && (task.actual_end_date !== 'undefined' || task.actual_end_date !== 'null')) list.push(['实际完成日期', gantt.templates.tooltip_date_format(task.actual_end_date)]);
    if (predecessors) list.push(['前置任务', predecessors]);
    if (task.nodefieldid && (task.nodefieldid !== 'undefined' || task.nodefieldid !== 'null')) list.push(['分段变量', gantt.nodefieldid]);

    return `
        <div class="gantt_tooltip_title">${task.text || ""}</div>
        <ul class="gantt_tooltip_list">
            ${list.map((item) => `<li class="gantt_tooltip_item">${item.join('：')}</li>`).join('')}
        </ul>
    `
}

/**
 * 初始gantt滚动
 */
function initGanttScroll () {
    // 监听gantt滚动
    gantt.attachEvent('onGanttScroll', function (left, top) {
        ganttScrollState = [left, top];
    }, {id: 'gantt-scroll'})

    // 获取缓存中的sessionGanttScroll
    const sessionGanttScroll = window.sessionStorage.getItem('sessionGanttScroll');
    if (sessionGanttScroll) {
        ganttScrollState = JSON.parse(sessionGanttScroll);
        window.sessionStorage.removeItem('sessionGanttScroll');
    }

    const taskCount = gantt.getTaskCount();
    if (!taskCount) return false;

    // 滚动到记录位置
    if (ganttScrollState) return gantt.scrollTo(...ganttScrollState);

    // 滚动到指定任务
    let doingTask = null;
    let completedTask = null;
    let overdueTask = null;
    let futureTask = null;
    gantt.eachTask(function (task) {
        if (task.type === 'task') {
            switch (Number(task.status)) {
                case 0:
                    if (!futureTask || (futureTask.planned_start_date && task.planned_start_date && futureTask.planned_start_date > task.planned_start_date)) futureTask = task;
                    break;
                case 1:
                    if (!doingTask || (task.actual_start_date && doingTask.actual_start_date && doingTask.actual_start_date > task.actual_start_date)) doingTask = task;
                    break;
                case 2:
                    break;
                case 3:
                    if (!overdueTask || (overdueTask.actual_start_date && task.actual_start_date && overdueTask.actual_start_date > task.actual_start_date)) overdueTask = task;
                    break;
            }
        }
    })
    if (doingTask) {
        gantt.showTask(doingTask.id);
    } else if (overdueTask) {
        gantt.showTask(overdueTask.id);
    } else if (futureTask) {
        gantt.showTask(futureTask.id);
    }
}

/**
 * 表格任务名称点击事件
 */
function handleTaskGridClick (e) {
    const studyCode = e.target.dataset.studycode;
    const menuPosition = e.target.dataset.menuposition;
    window.sessionStorage.setItem("sessionGanttScroll", JSON.stringify(ganttScrollState));
    window.top.vueMaskDialogShow({
        title: "临床试验项目",
        url: `/uapvue/index.html#/reportform?tableid=xsht&recordid=${studyCode}&menuPosition=${menuPosition}`,
    });
}

/**
 * 通过缩放等级获取scale对象
 * @param {String | Number} level 
 * @returns {Object} scale
 */
function getScaleByLevel (level) {
    let levelConfig = null;
    if (level instanceof Number) {
        levelConfig = zoomConfig.levels[level]
    } else {
        levelConfig = zoomConfig.levels.find(item => item.name === level);
    }
    const scales = levelConfig && levelConfig.scales ? levelConfig.scales : [];
    return scales[scales.length - 1] || {};
}

/**
 * 在季、年单位下不可点击 “起始”、“今天”按钮
 */
function disableButtonWithScaleUnit () {
    const {unit} = gantt.getScale();
    if (['year', 'quarter'].includes(unit)) {
        disableButton("today");
        disableButton("toStartTime");
    } else {
        enableButton("today");
        enableButton("toStartTime");
    }
}

/**
 * 切换缩放等级
 * @param {String} level 缩放等级
 */
function handleLevelChange (level) {
    const {unit} = getScaleByLevel(level);
    // 在setLevel之前执行，否则需要在执行次gantt.render()
    if (['year', 'quarter'].includes(unit)) {
        // year、quarter 不展示左右两个年份或季度
        const {start_date, end_date} = gantt.getSubtaskDates();
        gantt.config.start_date = start_date;
        gantt.config.end_date = end_date;
    } else {
        gantt.config.start_date = gantt.config.end_date = null;
    }

    scaleComboElement.value = level;
    gantt.ext.zoom.setLevel(level);
    disableButtonWithScaleUnit();
}
