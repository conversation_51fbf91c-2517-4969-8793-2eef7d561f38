package net.bioknow.cdtms.lightpdfSign;

import com.lowagie.text.DocumentException;
import com.lowagie.text.pdf.*;

import java.io.FileOutputStream;
import java.io.IOException;

public class PDFSplitMerge {
    public static void main(String[] args) {
        String inputFilePath1 = "path/to/first/input.pdf";
        String inputFilePath2 = "path/to/second/input.pdf";
        String outputFilePath = "path/to/output/merged_file.pdf";

        try {
            // 拆分第一个 PDF 文件，提取第一页
            PdfReader inputReader1 = new PdfReader(inputFilePath1);
            PdfReader inputReader2 = new PdfReader(inputFilePath2);

            PdfCopyFields copy1 = new PdfCopyFields(new FileOutputStream(outputFilePath));
            copy1.addDocument(inputReader1, "1");

            copy1.addDocument(inputReader2);


            copy1.close();
            inputReader1.close();

            // 合并第一个 PDF 文件剩下的页面和第二个 PDF 文件

            System.out.println("文件合并成功。");
        } catch (IOException | DocumentException e) {
            e.printStackTrace();
        }
    }
}
