<package>

	<handler classname="net.bioknow.cdtms.lightpdfSign.EHAppStart"/>
	<handler classname="net.bioknow.cdtms.lightpdfSign.ListPageInitHandlerSignProg"/>
	<handler classname="net.bioknow.cdtms.lightpdfSign.ListPageInitHandlerSignUrging"/>
	<handler classname="net.bioknow.cdtms.lightpdfSign.ListPageInitHandlerSignUrgingVue"/>
	<handler classname="net.bioknow.cdtms.lightpdfSign.ListPageInitHandlerSignProgVue"/>


	<action name="/LightpdfSignIntergrate" classname="net.bioknow.cdtms.lightpdfSign.ActionLightpdfSignIntegrate">
		<goto name="eSign" path="/cdtms/LightpdfSign/esign.jsp"/>
		<goto name="View" path="/cdtms/LightpdfSign/esignView.jsp"/>
		<goto name="Verify" path="/cdtms/LightpdfSign/verification.jsp"/>
		<goto name="verify" path="/cdtms/LightpdfSign/verification.jsp"/>
		<goto name="signCreate" path="/cdtms/LightpdfSign/signCreate.jsp"/>
		<goto name="ajaxCaneclPage" path="/cdtms/LightpdfSign/ajaxCaneclPage.jsp"/>
		<goto name="setSigner" path="/cdtms/LightpdfSign/setSigner.jsp"/>
		<goto name="register" path="/cdtms/LightpdfSign/register.jsp"/>
		<goto name="register2" path="/cdtms/LightpdfSign/register.jsp"/>
		<goto name="ajaxsetregister" path="/cdtms/LightpdfSign/ajaxmenu.jsp"/>
	</action>


	<action name="/lightpdfSign" classname="net.bioknow.cdtms.lightpdfSign.ActionLightpdfSignIntegrate">
		</action>
</package>

