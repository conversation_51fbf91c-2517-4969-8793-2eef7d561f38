package net.bioknow.cdtms.extdatabind;

import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class DTRFexdatabindMailTeam extends DTRecordFuncAction {

	private String receiver=null;
	private Long zq=null;

	public boolean canUse(int auth, String tableid, Long recordid) {

		try {

			if (!StringUtils.equals(tableid,"ext_data_bind")) {
				return false;
			}
			if(Long.valueOf(SessUtil.getSessInfo().getUserid())<=2) return  true;


			String projectid = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng = new DAODataMng(projectid);

			Map exdataBindMap = daoDataMng.getRecord(tableid, recordid);



			String blind_status = (String) exdataBindMap.get("blind_status");

			this.zq=zq;

			if (!StringUtils.equals(blind_status,"2")) {
				return false;

			}

			Long studyid = (Long) exdataBindMap.get("study_id");
			Map userMap = SessUtil.getSessInfo().getUser();

			List StudyUserList = daoDataMng.listRecord("roles", "obj.studyid=" + studyid + " and obj.active='1' and obj.member=" + (String) userMap.get("id"), null, 1);
			if (CollectionUtils.isEmpty(StudyUserList)) {

				return false;

			}



			Map StudyUserMap = (Map) StudyUserList.get(0);

			String role = (String) StudyUserMap.get("limitnum");


			if ((StringUtils.equals(role,"EDM"))) {
				this.receiver= (String) exdataBindMap.get("file_receive_emails");


				return true;

			}




		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}

	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
		try {





			String projectId = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng=new DAODataMng(projectId);
			List<Map> engineList = daoDataMng.listRecord("email_template","obj.tableid='"+fpb.getTableid()+"' and obj.name='发送TEAMQC'",null,100);
			ArrayList<Map> currEngineList = new ArrayList<>();

			engineList.get(0).put("receiver",this.receiver);

			engineList.get(0).put("isSign", "2");

			currEngineList.add(engineList.get(0));
			request.setAttribute("listr", currEngineList);
//			request.setAttribute("receiver", this.receiver);
			this.forwardByUri(request,response,"/formMail.ajaxmenu.do");




		} catch (Exception e) {
			Log.error("",e);
		}
	}

	public FuncInfoBean getFIB(String tableid) {
		FuncInfoBean fib = new FuncInfoBean();

		fib.setName("发送给项目组");
		fib.setType(FuncInfoBean.FUNCTYPE_AJAXMENU);
		fib.setWinHeight(800);
		fib.setWinWidth(800);
		fib.setSimpleViewShow(true);


		return fib;
	}

}
