package net.bioknow.cdtms.lightpdfSign;

import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfDictionary;
import com.itextpdf.text.pdf.PdfDocument;
import com.itextpdf.text.pdf.PdfName;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.parser.PdfReaderContentParser;
import com.itextpdf.text.pdf.parser.PdfTextExtractor;
import org.apache.pdfbox.text.TextPosition;
import sun.security.util.Length;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;

/**
 * @ClassName PdfHelper
 * @Description Pdf帮助类
 * <AUTHOR>
 * @Date 2020/3/7 17:40
 * @Version 1.0
 */
public class PdfHelper {


    public static void main(String[] args) {

        try{


            PdfReader pdfReader = new PdfReader("C:\\Users\\<USER>\\Desktop\\222222.pdf");
//            PdfReader pdfReader = new PdfReader("D:\\360data\\WXWork\\1688851929312792\\Cache\\File\\2023-09\\SHR-A1921-201_数据管理计划_V2.0_2023-09-01 (2).pdf");


            float X =getKeyWords(pdfReader, "签名")[0];
            float[]  coordinate = getKeyWords(pdfReader, "董志超");
            float page = coordinate[2];
            float Y = coordinate[1];
            float X2 = coordinate[0];
            pdfReader.close();
            System.out.println("郭:");
            System.out.println("page:"+(long) page);
            System.out.println("名字X:"+X2);
            System.out.println("签名X:"+X);
            System.out.println("Y:"+Y);
            System.out.println("左下Y:"+coordinate[3]);
            System.out.println("页面高:"+coordinate[4]);

        } catch (IOException e) {
            e.printStackTrace();
        }
//        return coordinate;

    }



//    public static void main(String[] args) throws IOException {
//        String filePath ="D:\\SHR-5495-I-101_UAT报告_V1.1 2023-10-08_签字页.pdf"; // PDF文件路径
//        String keyword = "徐然"; // 指定关键字
//
//        PDDocument document = PDDocument.load(new File(filePath));
//        PDFTextStripper stripper = new PDFTextStripper() {
//            @Override
//            protected void writeString(String text, List<TextPosition> textPositions) throws IOException {
//                if (text.contains(keyword)) {
//                    for (TextPosition textPosition : textPositions) {
//                        System.out.println(textPosition.getPageHeight());
//                        System.out.println(text + ": " + textPosition.getXDirAdj() + "," + textPosition.getYDirAdj());
//
//                    }
//                }
//                super.writeString(text, textPositions);
//            }
//        };
//        stripper.setSortByPosition(true);
//        stripper.getText(document);
//        document.close();
//    }



    /**
     * <AUTHOR>
     * @Date 18:26 2020/3/7
     * @Description 获取关键字所在PDF坐标
     * @param pdfReader
     * @param keyWords
     * @return float[]
     */
    public static float[] getKeyWords(PdfReader pdfReader, String keyWords) {
        float[] coordinate = null;
        int page = 0;
        try{
            int pageNum = pdfReader.getNumberOfPages();
            PdfReaderContentParser pdfReaderContentParser = new PdfReaderContentParser(pdfReader);
            CustomRenderListener renderListener = new CustomRenderListener();
            renderListener.setKeyWord(keyWords);
            for (page = 1; page <= pageNum; page++) {
                renderListener.setPage(page);

                Rectangle pageSize = pdfReader.getPageSizeWithRotation(page);
                float height = pageSize.getHeight();
                renderListener.setPageSize(height);

//                float height = pdfReader.getPageSize(page).getHeight();
//                byte[] pageContent = pdfReader.getPageContent(page);
//                System.out.println(new String(pageContent, StandardCharsets.UTF_8));

                PdfTextExtractor.getTextFromPage(pdfReader, page, renderListener);

//                pdfReaderContentParser.processContent(page, renderListener);

                coordinate = renderListener.getPcoordinate();

                if (coordinate != null){



                    break;


                };



            }



        } catch (IOException e) {
            e.printStackTrace();
        }
        return coordinate;
    }

}