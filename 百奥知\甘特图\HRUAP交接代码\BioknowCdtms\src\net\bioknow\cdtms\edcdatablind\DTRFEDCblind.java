package net.bioknow.cdtms.edcdatablind;

import com.google.gson.Gson;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

import static net.bioknow.webutil.tools.URLUtil.getContent;


public class DTRFEDCblind extends DTRecordFuncAction {


    private static final String EDC_DATA_URL = "/report/report/listReportForCDTMS";
    private static final String EDC_DOWNLOAD_URL = "/report/report/download";

    public boolean canUse(int auth, String tableid, Long recordid) {

        try {
            if ("study_data_blind".equals(tableid)) return true;
        } catch (Exception e) {
            Log.error("", e);
        }
        return false;
    }

    public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
        try {
            String projectid = SessUtil.getSessInfo().getProjectid();
            DAODataMng dmdao = new DAODataMng(projectid);

            Map map = dmdao.getRecord("study_data_blind", Long.parseLong(fpb.getRecordid()));
            Long studyid = (Long)map.get("study_id");
            if(studyid == null) {
                this.gotoMsgWin(request, response, "studycode为空");
                return;
            }
            Map mapstudy = dmdao.getRecord("xsht", studyid);
            String studycode = (String) mapstudy.get("studyid");
            String edcUrl = PathUtil.getStudyPath(projectid, studycode);
            if(StringUtils.isEmpty(edcUrl)){
                this.gotoMsgWin(request, response, "edcurl为空");
                return;
            }
            String content = getContent(edcUrl + EDC_DATA_URL + "?studyCode=" + studycode + "&env=dev", 30, "UTF8", "");
            Map mapdata = new Gson().fromJson(content, Map.class);
            List listdata = (List) mapdata.get("data");

            request.setAttribute("listdata", listdata);
            request.setAttribute("rid", fpb.getRecordid());
            request.setAttribute("studycode", studycode);
            this.forwardByUri(request, response, "/cdtms/edcdatablind/datablind.jsp");
        } catch (Exception e) {
            Log.error("", e);
        }
    }

    public FuncInfoBean getFIB(String tableid) {
        FuncInfoBean fib = new FuncInfoBean();
        fib.setName("提取数据");
        fib.setType(FuncInfoBean.FUNCTYPE_AJAXMENU);
        fib.setWinHeight(400);
        fib.setWinWidth(600);
        return fib;
    }

}
