package net.bioknow.cdtms.edmFileOutput;



import com.google.gson.Gson;
import net.bioknow.mvc.RootAction;
import net.bioknow.mvc.tools.WebPath;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.schemaplug.attach.AttachDAO;
import net.bioknow.webutil.fileup.DAOFileup;
import net.bioknow.webutil.tools.Log;
import net.bioknow.webutil.tools.URLUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.regex.Pattern;


public class Actionedmfile extends RootAction {


    public void download(HttpServletRequest request, HttpServletResponse response) {
        try {

//edmfile.download.do?studycode=aaa&filename=aaaa.doc
            String projectId = "edm_uap";
            String tableId = "outboard_data_manager";
            String filedId = "outboard_data_doc";
            String studyCode = request.getParameter("studycode");
            String fileNameReq = request.getParameter("filename");
            String token = request.getParameter("token");


            String url = "https://cdtms-tst.hengrui.com/tokensession.getsession.do?param="+token;

            String sessStr = URLUtil.getContentUTF8(url, 3000);


            Log.info(sessStr);
            Gson gson = new Gson();
            Map<String, Object> su = gson.fromJson(sessStr, Map.class);

//            BeanSessUser su = new Gson().fromJson(sessStr, BeanSessUser.class);
            if(!String.valueOf(su.get("status")).equals("200")) {
                response.getOutputStream().write("Token err".getBytes());

                return;

            }


            Log.info("Token OK");

            if (StringUtils.isEmpty(studyCode)) {
                response.getOutputStream().write("studyCode is Null".getBytes());
                return;

            }
            Log.info("studyCode OK");


            DAODataMng daoDataMng = new DAODataMng(projectId);


            List<Map> StudyList = daoDataMng.listRecord("xsht", "obj.studyid='" + studyCode + "'", null, 1);

            if (CollectionUtils.isEmpty(StudyList)) {
                response.getOutputStream().write("Study Not Found".getBytes());
                return;
            }


            Log.info("Study Found");

            Map StudyMap = StudyList.get(0);
            Long StudyId = (Long) StudyMap.get("id");

            List<Map> ProductionUseList = daoDataMng.listRecord(tableId, "obj.studyid=" + StudyId+" and obj.outboard_data_doc like '%"+fileNameReq+"%'", null, 1);

            if (CollectionUtils.isEmpty(ProductionUseList)) {
                response.getOutputStream().write("File Record Not Found".getBytes());
                return;
            }
            Log.info("File Record Found");

            Map ProductionUseMap = ProductionUseList.get(0);

            String EDCProductionReportStr = (String) ProductionUseMap.get(filedId);

            if (StringUtils.isEmpty(EDCProductionReportStr)) {
                response.getOutputStream().write("File File Not Found".getBytes());
                return;

            }
            Log.info("File  Found");

            AttachDAO attachDAO = new AttachDAO(projectId);

            File DCProductionReportFile = attachDAO.getFiles(EDCProductionReportStr, tableId)[0];
            String fileName = attachDAO.getFileNames(EDCProductionReportStr, tableId)[0];
//            String fileName = DCProductionReportFile.getName();
//            int index = fileName.lastIndexOf(".");
//            String extension = fileName.substring(index + 1);


            String filePath = WebPath.getRootPath() + DAOFileup.tempfolder+ "/"+fileName;


            String filePath2 =DAOFileup.tempfolder+ "/"+URLUtil.urlEncodeUTF8(fileName);
            File targetFile = new File(filePath);

            FileUtils.copyFile(DCProductionReportFile,targetFile);


            this.redirectByUrl(request,response,filePath2);
        }  catch (Exception e) {
            Log.error("", e);
        }

    }

    private static Pattern FilePattern = Pattern.compile("[\\r\\n\\\\/:*?\"<>|]");
    public static String httpGet(String url) {
        // 获取连接客户端工具
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse httpResponse = null;
        String finalString = null;
        HttpGet httpGet = new HttpGet(url);
        /**公共参数添加至httpGet*/
        /**header中通用属性*/
        httpGet.setHeader("Accept", "*/*");
        httpGet.setHeader("Accept-Encoding", "gzip, deflate");
        httpGet.setHeader("Cache-Control", "no-cache");
        httpGet.setHeader("Connection", "keep-alive");
        httpGet.setHeader("Content-Type", "application/json;charset=UTF-8");
        /**业务参数*/

        try {
            httpResponse = httpClient.execute(httpGet);
            HttpEntity entity = httpResponse.getEntity();
            finalString = EntityUtils.toString(entity, "UTF-8");
            try {
                httpResponse.close();
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return finalString;
    }


    public static void main(String[] args) throws Exception {


        String url = "https://cdtms-tst.hengrui.com/tokensession.getsession.do?param=D770A184ACA7437587FB41B54485F847";

//        String sessStr = URLUtil.getContent(url, 3000);
        String sessStr = "{\"loginid\":\"<EMAIL>\",\"username\":\"寮犳?″畞\",\"email\":\"<EMAIL>\",\"remoteproject\":\"A033A0F5C0864011A6CEEE3AB74C11CA\",\"projectid\":\"cdtmsen_val\",\"status\":\"200\",\"note\":\"OK\",\"role\":\"EDM\",\"visittime\":\"Feb 6, 2024 4:21:49 PM\",\"mapInfo\":{\"fileName\":\"CDTMS鎵嬪唽浼樺寲娴嬭瘯椤圭洰_1111_plasma_20240206_zyn.csv\",\"venderName\":\"姹熻嫃鎭掔憺(CRO)\",\"role\":\"EDM\",\"dataType\":\"PKPD\",\"testFileID\":\"null\",\"id\":\"**********\",\"projectName\":\"CDTMS鎵嬪唽浼樺寲娴嬭瘯椤圭洰\",\"type\":\"t\",\"DTABMList\":\"[{\\\"role\\\":\\\"缁熻\uE178缂栫▼缁忕悊\\\",\\\"method\\\":\\\"鍙橀噺涓嶄紶杈揬\"},{\\\"role\\\":\\\"鑽\uE21C悊缁忕悊\\\",\\\"method\\\":\\\"鍙橀噺涓嶄紶杈揬\"}]\",\"account\":\"<EMAIL>\"}}";

        System.out.println(sessStr);
        Gson gson = new Gson();
        Map<String, Object> su = gson.fromJson(sessStr, Map.class);

//            BeanSessUser su = new Gson().fromJson(sessStr, BeanSessUser.class);

        System.out.println(su.get("status"));





    }

}