package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.mvc.tools.Language;
import net.bioknow.services.entity.BeanListFieldParam;
import net.bioknow.services.uap.dbdatamng.face.FaceListPageInit;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.face.ListPageInitFace;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ListPageInitHandlerSignUrgingVue implements FaceListPageInit {

	@Override
	public boolean canUse(int auth, String projectId, String tableid) {
		try {
			//获取当前用户是否有在线签字权限
			if (StringUtils.equals(tableid, "esign_signer")){
				return true;
			}

		}
		catch (Exception e){
			Log.error("",e);
		}
		return false;
	}

	@Override
	public BeanListFieldParam initBeanlistFieldParam() {
		BeanListFieldParam bean = new BeanListFieldParam();
		bean.setLabel("功能");
		bean.setWidth(180);

		bean.setProp("cdtms_signUrging");
		bean.setTextHtml(true);
		return bean;
	}

	@Override
	public void initValue(List list, List list1, String projectId, String tableid) throws Exception {
		try {

			DAODataMng daoDataMng = new DAODataMng(projectId);
			for (Object dataObj : list1) {
				Map dataMap = (Map) dataObj;
				Long Id = (Long) dataMap.get("id");
				Map recordMap = daoDataMng.getRecord(tableid, Id);

				if (!StringUtils.equals(SessUtil.getSessInfo().getUserid(),String.valueOf(recordMap.get("userid")))) {
					continue;
				}

				List esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.id="+recordMap.get("esign_instance_id"),null,1);

				if (CollectionUtils.isEmpty(esignInstanceList)) {
					continue;
				}

				Map  esignInstanceMap = (Map) esignInstanceList.get(0);

				if(!StringUtils.equals("1", (String) esignInstanceMap.get("status"))){
					continue;

				}

			String js=	"<script type=\"text/javascript\">\n" +
						"\n" +
						"\n" +
						"    //发送表单ajax请求\n" +
						"\n" +
						"\n" +
						"\n" +
						"    function Urging(id){\n" +
						"        $.ajax({\n" +
						"            url: \"LightpdfSignIntergrate.Urging.do\",\n" +
						"            type: \"POST\",\n" +
						"            data: {\n" +
						"                id: id\n" +
						"            },\n" +
						"            dataType: 'json',\n" +
						"            success: function (data) {\n" +
						"\n" +
						"                if (data.status == '200') {\n" +
						"                    alert(\"发送成功\");\n" +
						"\n" +
						"                } else {\n" +
						"\n" +
						"                    alert(data);\n" +
						"                }\n" +
						"\n" +
						"            }\n" +
						"        });\n" +
						"    }\n" +
						"</script>"+"<script type=\"text/javascript\">\n" +
						"\n" +
						"\n" +
						"    //发送表单ajax请求\n" +
						"\n" +
						"\n" +
						"\n" +
						"    function Remove(id){\n" +
						"        $.ajax({\n" +
						"            url: \"LightpdfSignIntergrate.RemoveSigner.do\",\n" +
						"            type: \"POST\",\n" +
						"            data: {\n" +
						"                id: id\n" +
						"            },\n" +
						"            dataType: 'json',\n" +
						"            success: function (data) {\n" +
						"\n" +
						"                if (data.status == '200') {\n" +
						"                    alert(\"移除成功\");window.location.reload();\n" +
						"\n" +
						"                } else {\n" +
						"\n" +
						"                    alert(data);\n" +
						"                }\n" +
						"\n" +
						"            }\n" +
						"        });\n" +
						"    }\n" +
						"</script>";


				String status = (String) recordMap.get("status");
				String Html="";
				if (StringUtils.equals(status,"0")) {
					Html="<button class=\"el-button el-button--default el-button--mini button-margin-right\" href=\"javascript:void(0);\" onclick=\"Urging("+Id+");return false;\">提醒</button>";
				}
				if (StringUtils.equals(status,"0")||StringUtils.equals(status,"1")||StringUtils.equals(status,"5")) {
					Html+="<button class=\"el-button el-button--default el-button--mini button-margin-right\" href=\"javascript:void(0);\" onclick=\"Remove("+Id+");return false;\">移除</button>";
				}

				dataMap.put("cdtms_signUrging",Html);





			}
		} catch (Exception e) {
			Log.error("",e);
		}
	}

	@Override
	public String getFieldid() {
		return null;
	}

	@Override
	public Integer getIndex() {
		return null;
	}
}
