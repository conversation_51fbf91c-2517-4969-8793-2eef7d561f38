﻿<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=utf-8" %>
<%@ page language="java" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html>
<head>
    <script src="/cdtms/dhtmlxGantt/codebase/dhtmlxgantt.js"></script>
<%--    <link href="/cdtms/dhtmlxGantt/codebase/dhtmlxgantt.css" rel="stylesheet">--%>
<%--    <script src="https://export.dhtmlx.com/gantt/api.js?v=7.1.12"></script>--%>

    <link href="/cdtms/dhtmlxGantt/codebase/skins/dhtmlxgantt_material.css" rel="stylesheet">





    <style>
           .gantt_task_cell.week_end {
            background-color: #EFF5FD;
        }

        .gantt_task_row.gantt_selected .gantt_task_cell.week_end {
            background-color: #F8EC9C;
        }


        .gantt_grid{
            text-align: right;
        }

        .gantt_task_progress {
            text-align: right;
            box-sizing: border-box;
            color: white;
            font-weight: bold;
        }

    </style>


    <style>
        body,
        html {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: "Roboto", sans-serif;
        }

        .demo-main-container {
            display: -ms-flexbox;
            display: -webkit-flex;
            display: flex;
            -webkit-flex-direction: column;
            -ms-flex-direction: column;
            flex-direction: column;

            height: 100%;
            min-height: 641px;
            min-width: 1024px;

            background-color: #fff;

            z-index: 4;
        }

        .demo-main-content {
            position: relative;

            height: 100%;
            margin: 10px 10px 0;

            background-color: #fff;
        }

        #gantt_here {
            position: absolute;

            width: 100%;
            height: 100%;

            background-color: #fff;

            overflow: auto;
        }

        .status_line {
            background-color: #0ca30a;
        }

        .gantt_grid_wbs {
            position: absolute;
        }

        .gantt_grid_scale {
            position: relative;
            z-index: 1;
        }

        .dnd_highlighter {
            position: absolute;
            height: 4px;
            width: 500px;
            background-color: #3498db;
        }

        .dnd_highlighter::before {
            background: transparent;
            width: 12px;
            height: 12px;
            box-sizing: border-box;
            border: 3px solid #3498db;
            border-radius: 6px;
            content: "";
            line-height: 1px;
            display: block;
            position: absolute;
            margin-left: -11px;
            margin-top: -4px;
        }

        .gantt_tooltip {
            font-size: 14px;
            line-height: 120%;
            border-bottom: 1px solid #b3b3b3;
            border-right: 1px solid #b3b3b3;
        }

        .gantt_drag_marker {
            opacity: 0.6;
        }

        .gantt_drag_marker.gantt_grid_resize_area {
            z-index: 1;
        }

        .gantt_parent_row {
            font-weight: bold;
        }

        .gantt_task_line div.gantt_side_content {
            bottom: 0;
        }

        .gantt-top-panel {
            position: relative;
            color: #fff;
            padding: 11px 16px;
            background: #3d3d3d;
        }

        .gantt-top-panel__btn {
            display: inline-block;
            color: #fff;
            padding: 7px 24px;
            text-decoration: none;
            border-radius: 20px;
            background: #2095f3;

            position: absolute;
            right: 8px;
            top: 50%;
            margin-top: -16px;
        }

        .gantt-top-panel__btn:hover {
            background: #03a9f4;
        }

        .gantt-top-panel__btn:focus {
            outline: none;
        }

        .gantt-top-panel__btn:active {
            transform: translateY(1px);
            -webkit-transform: translateY(1px);
        }

        .status-control {
            font-size: 0;
        }

        .status-control .status {
            position: relative;
            width: 37px;
            height: 22px;
            margin-right: 9px;
            transition: all 0.4s ease;
            border-radius: 11px;
            background-color: #e6e6e6;
        }

        .status-control.checked .status {
            background-color: #2095f3;
        }

        .dhx_checkbox_grip {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 18px;
            height: 18px;
            transition: all 0.2s ease;
            border-radius: 9px;
            background-color: #fff;
            box-shadow: 0 3px 9px 0 rgba(0, 0, 0, 0.2);
        }

        .status-control.checked .dhx_checkbox_grip {
            left: 17px;
        }

        .dhx_checkbox_title {
            color: #5f5f5f;
            font: 500 14px/32px "Roboto", Arial, sans-serif;
        }

        .button-with-icon.active {
            background-color: #e5e5e5;
        }

        .disabled {
            opacity: 0.5;
        }

        .dhx_checkbox_title,
        .status-control .status,
        .dhx_demo_checkbox {
            display: inline-block;
            vertical-align: middle;
        }

        .dhx_demo_checkbox {
            font-size: 14px;
        }

        .dhx_demo_checkbox_group {
            position: relative;
            white-space: nowrap;
            margin-right: 24px;
        }

        .dhx_demo_checkbox {
            margin-left: 10px;
            cursor: pointer;
            user-select: none;
        }

        .dhx_demo_checkbox:first-child {
            margin-left: 0;
        }

        .demo-actions-container{
            position: relative;

            font-size: 0;
            line-height: 0;

            padding: 15px 10px 10px;

            user-select: none;
        }

        .demo-actions{
            position: relative;

            display: -ms-flexbox;
            display: -webkit-flex;
            display: flex;
            -webkit-flex-wrap: wrap;
            -ms-flex-wrap: wrap;
            flex-wrap: wrap;
            -webkit-align-items: center;
            -ms-flex-align: center;
            align-items: center;

            padding: 18px 130px 18px 24px;

            border: 1px solid #dfdfdf;
        }

        .demo-actions__row{
            padding: 5px 0;
        }

        .demo-actions__col{
            display: inline-block;
            vertical-align: middle;

            margin-right: 24px;
        }

        .demo-actions__last-elem{
            position: absolute;
            right: 24px;
            top: 50%;
            margin-top: -16px;
        }

        .demo-btn{
            position: relative;

            display: inline-block;
            vertical-align: middle;

            color: #0288d1;
            font: 500 14px/20px "Roboto", Arial, sans-serif;
            text-align: center;
            text-transform: uppercase;
            text-decoration: none;
            white-space: nowrap;

            margin-left: 10px;
            padding: 7px 16px 5px;

            border-radius: 32px;
            border: 1px solid #0288d1;
            background-color: transparent;

            transition: background-color 0.2s ease-in, box-shadow 0.2s ease-in;

            outline: none;
            user-select: none;

            cursor: pointer;
        }

        .demo-btn:first-child{
            margin: 0;
        }

        .demo-btn:hover{
            background-color: #d9edf8;
        }

        .demo-btn:active{
            background-color: #b8def2;
        }

        .demo-btn.outline-btn{
            color: #fff;

            border: none;
            background-color: #0288d1;
        }

        .demo-btn.outline-btn:hover{
            background-color: #35a0da;
        }

        .demo-btn.outline-btn:active{
            background-color: #0288d1;
        }

        .scale-combo{
            display: block;

            color: #5f5f5f;
            font: 500 14px/32px "Roboto", Arial, sans-serif;

            width: 120px;
            padding: 0 20px 0 16px;

            border-radius: 0;
            border: 1px solid #ededed;

            background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAAZiS0dEAAAAAAAA+UO7fwAAAAlwSFlzAAAASAAAAEgARslrPgAAAEFJREFUSMftzssJACAMBNERi/YolubBurQB8UPiRfZBrjsBEflfAvrm8suIeXwVcRufRY7H40WgAQGoQPH+XkQMBhiHGdCscwwgAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE5LTAyLTI2VDExOjIwOjUzKzAwOjAwRvZ3FgAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxOS0wMi0yNlQxMToyMDo1MyswMDowMDerz6oAAAAodEVYdHN2ZzpiYXNlLXVyaQBmaWxlOi8vL3RtcC9tYWdpY2stMnZxZlhWUVL3RMOVAAAAAElFTkSuQmCC') 96% / 15% no-repeat #fbfbfb;

            -webkit-appearance: none;
            appearance: none;

            outline: none;
        }

        .scale-combo:active,
        .scale-combo:focus{
            border-color: #2095F3;

            outline: none;
        }

        .icon-btn{
            display: block;

            color: #5f5f5f;
            font: 500 14px/32px "Roboto", Arial, sans-serif;
            text-align: center;
            text-decoration: none;

            padding: 0;

            cursor: pointer;
        }

        .icon-btn.disabled{
            opacity: 0.6;

            pointer-events: none;
        }

        .icon-btn img{
            position: relative;
            top: -1px;

            display: inline-block;
            vertical-align: middle;

            width: 24px;
            margin-right: 5px;
        }

        @media screen and (max-width: 1280px){
            .demo-actions{
                padding: 4px 130px 10px 24px;
            }
        }

        @media screen and (max-width: 1150px){
            .demo-actions{
                padding: 4px 120px 10px 15px;
            }

            .demo-actions__col{
                margin-right: 10px;
            }

            .demo-actions__last-elem{
                right: 15px;
            }
        }
    </style>


</head>
<body>


<div class="demo-main-container">
    <div class="demo-main-content">
        <div id="gantt_here"></div>
    </div>

    <div class="demo-actions-container">
        <div class="demo-actions">
            <div class="demo-actions__row">
                <div class="demo-actions__col">
                    <div class="dhx_demo_checkbox">
                        <div id="collapse" class="status-control">
                            <div class="status">
                                <div class="dhx_checkbox_grip"></div>
                            </div>

                            <div class="dhx_checkbox_title">Collapse Rows</div>
                        </div>
                    </div>
                </div>

                <div class="demo-actions__col">
                    <span data-action="undo" class="icon-btn disabled js-action-btn">
                        <img src="//dhtmlx.com/docs/products/demoApps/advanced-gantt-chart/demo/imgs/ic_undo_24.png" alt="" />
                        Undo
                    </span>
                </div>

                <div class="demo-actions__col">
                    <span data-action="redo" class="icon-btn disabled js-action-btn">
                        <img src="//dhtmlx.com/docs/products/demoApps/advanced-gantt-chart/demo/imgs/ic_redo_24.png" alt="" />
                        Redo
                    </span>
                </div>

                <div class="demo-actions__col">
                    <div class="dhx_demo_checkbox">
                        <div id="auto-scheduling" class="status-control">
                            <div class="status">
                                <div class="dhx_checkbox_grip"></div>
                            </div>

                            <div class="dhx_checkbox_title">Auto Scheduling</div>
                        </div>
                    </div>
                </div>

                <div class="demo-actions__col">
                    <div class="dhx_demo_checkbox">
                        <div id="critical-path" class="status-control">
                            <div class="status">
                                <div class="dhx_checkbox_grip"></div>
                            </div>

                            <div class="dhx_checkbox_title">Critical Path</div>
                        </div>
                    </div>
                </div>

                <div class="demo-actions__col">
                    <div class="dhx_demo_checkbox">
                        <div id="zoom-to-fit" class="status-control">
                            <div class="status">
                                <div class="dhx_checkbox_grip"></div>
                            </div>

                            <div class="dhx_checkbox_title">Zoom to Fit</div>
                        </div>
                    </div>
                </div>

                <div class="demo-actions__col">
                    <div class="scale-container">
                        <select class="scale-combo" id="scale_combo" placeholder="">
                            <option value="" disabled="" selected="">Zoom to:</option>
                            <option value="years">Years</option>
                            <option value="quarters">Quarters</option>
                            <option value="months">Months</option>
                            <option value="weeks">Weeks</option>
                            <option value="days">Days</option>
<%--                            <option value="hours">Hours</option>--%>
                        </select>
                    </div>
                </div>
            </div>

            <div class="demo-actions__row">
                <span class="demo-btn js-action-btn" data-action="toPNG">Export to PNG</span>
            </div>

            <div class="demo-actions__last-elem">
                <span class="demo-btn outline-btn js-action-btn" data-action="fullscreen">Fullscreen</span>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">


    //拖动
    gantt.plugins({
        drag_timeline: true
    });


    gantt.config.sort = true;
    gantt.i18n.setLocale("cn");


    window.ganttModules = {};

    function addClass(node, className) {
        node.classList.add(className);
    }

    function removeClass(node, className) {
        node.classList.remove(className);
    }

    function getButton(name) {
        return document.querySelector(".demo-actions [data-action='" + name + "']");
    }

    function highlightButton(name) {
        addClass(getButton(name), "menu-item-active");
    }
    function unhighlightButton(name) {
        removeClass(getButton(name), "menu-item-active");
    }

    function disableButton(name) {
        addClass(getButton(name), "disabled");
    }

    function enableButton(name) {
        removeClass(getButton(name), "disabled");
    }

    function refreshUndoBtns() {
        if (!gantt.getUndoStack || !gantt.getUndoStack().length) {
            disableButton("undo");
        } else {
            enableButton("undo");
        }

        if (!gantt.getRedoStack || !gantt.getRedoStack().length) {
            disableButton("redo");
        } else {
            enableButton("redo");
        }
    }

    setInterval(refreshUndoBtns, 1000);


    const toolbarMenu = {
        undo: function () {
            gantt.undo();
            refreshUndoBtns();
        },
        redo: function () {
            gantt.redo();
            refreshUndoBtns();
        },
        zoomToFit: function () {
            ganttModules.zoomToFit.toggle();
            toggleCheckbox(zoomToFitCheckbox, config.zoom_to_fit);
            const scalesDropdown = document.querySelector("#scale_combo");
            const zoomLevelName = zoomConfig.levels[gantt.ext.zoom.getCurrentLevel()].name;
            scalesDropdown.value = zoomLevelName;
        },
        fullscreen: function () {
            gantt.ext.fullscreen.toggle();
        },
        collapseAll: function () {
            gantt.eachTask(function (task) {
                task.$open = false;
            });
            gantt.render();

        },
        expandAll: function () {
            gantt.eachTask(function (task) {
                task.$open = true;
            });
            gantt.render();
        },
        toggleAutoScheduling: function () {
            gantt.config.auto_scheduling = !gantt.config.auto_scheduling;

            if (gantt.config.auto_scheduling) {
                gantt.autoSchedule();
            }
        },
        toggleCriticalPath: function () {
            gantt.config.highlight_critical_path = !gantt.config.highlight_critical_path;

            gantt.render();
        },
        toPDF: function () {
            // workaround for the bug with the export
            gantt.config.columns[5].editor = null;
            gantt.exportToPDF({
                header: `<style>.timeline_cell{width: ${gantt.$task_data.scrollWidth}px !important;}</style>`,
                raw: true
            });
            gantt.config.columns[5].editor = predecessorEditor;
        },
        toPNG: function () {
            // workaround for the bug with the export
            gantt.config.columns[5].editor = null;
            gantt.exportToPNG({
                header: `<style>.timeline_cell{width: ${gantt.$task_data.scrollWidth}px !important;}</style>`,
                raw: true
            });
            gantt.config.columns[5].editor = predecessorEditor;
        },
        toExcel: function () {
            // workaround for the bug with the export
            gantt.config.columns[5].editor = null;
            gantt.exportToExcel();
            gantt.config.columns[5].editor = predecessorEditor;
        },
        toMSProject: function () {
            // workaround for the bug with the export
            gantt.config.columns[5].editor = null;
            gantt.exportToMSProject();
            gantt.config.columns[5].editor = predecessorEditor;
        }
    };

    const zoomConfig = {
        levels: [

            {
                name: "days",
                scales: [
                    { unit: "month", step: 1, format: "%M" },
                    { unit: "day", step: 1, format: "%j" },
                ],
                round_dnd_dates: true,
                min_column_width: 60,
                scale_height: 60
            },
            {
                name: "weeks",
                scales: [
                    { unit: "month", step: 1, format: "%M" },
                    {
                        unit: "week", step: 1, format: function (date) {
                            const dateToStr = gantt.date.date_to_str("%d %M");
                            const endDate = gantt.date.add(gantt.date.add(date, 1, "week"), -1, "day");
                            return dateToStr(date) + " - " + dateToStr(endDate);
                        }
                    }
                ],
                round_dnd_dates: false,
                min_column_width: 60,
                scale_height: 60
            },
            {
                name: "months",
                scales: [
                    { unit: "year", step: 1, format: "%Y" },
                    { unit: "month", step: 1, format: "%M" }
                ],
                round_dnd_dates: false,
                min_column_width: 50,
                scale_height: 60
            },
            {
                name: "quarters",
                scales: [
                    { unit: "year", step: 1, format: "%Y" },
                    {
                        unit: "quarter", step: 1, format: function quarterLabel(date) {
                            const month = date.getMonth();
                            let q_num;

                            if (month >= 9) {
                                q_num = 4;
                            } else if (month >= 6) {
                                q_num = 3;
                            } else if (month >= 3) {
                                q_num = 2;
                            } else {
                                q_num = 1;
                            }

                            return "Q" + q_num;
                        }
                    },
                    { unit: "month", step: 1, format: "%M" }
                ],
                round_dnd_dates: false,
                min_column_width: 50,
                scale_height: 60
            },
            {
                name: "years",
                scales: [
                    { unit: "year", step: 1, format: "%Y" },
                    {
                        unit: "year", step: 5, format: function (date) {
                            const dateToStr = gantt.date.date_to_str("%Y");
                            const endDate = gantt.date.add(gantt.date.add(date, 5, "year"), -1, "day");
                            return dateToStr(date) + " - " + dateToStr(endDate);
                        }
                    }
                ],
                round_dnd_dates: false,
                min_column_width: 50,
                scale_height: 60
            },
            {
                name: "years",
                scales: [
                    {
                        unit: "year", step: 10, format: function (date) {
                            const dateToStr = gantt.date.date_to_str("%Y");
                            const endDate = gantt.date.add(gantt.date.add(date, 10, "year"), -1, "day");
                            return dateToStr(date) + " - " + dateToStr(endDate);
                        }
                    },
                    {
                        unit: "year", step: 100, format: function (date) {
                            const dateToStr = gantt.date.date_to_str("%Y");
                            const endDate = gantt.date.add(gantt.date.add(date, 100, "year"), -1, "day");
                            return dateToStr(date) + " - " + dateToStr(endDate);
                        }
                    }
                ],
                round_dnd_dates: false,
                min_column_width: 50,
                scale_height: 60
            }
        ],
        useKey: "ctrlKey",
        trigger: "wheel",
        element: function(){
            return gantt.$root.querySelector(".gantt_task");
        }

    }

    gantt.ext.zoom.init(zoomConfig);



    // 周末暗色
    // gantt.config.work_time = true;
    // gantt.config.duration_unit = "day";
    // gantt.config.duration_step = 1;
    // gantt.config.correct_work_time = true;
    // gantt.setWorkTime({day: 0,hours: true});
    // gantt.setWorkTime({day: 6,hours: true});
    // gantt.setWorkTime({ hours:["9:00-18:00"] });
    //
    // gantt.templates.timeline_cell_class = function (task, date) {
    //     // alert(gantt.isWorkTime(date));
    //     if (!gantt.isWorkTime(date))return "week_end";
    //     return "";
    // };

    // gantt.ignore_time = function (date) {
    //     return !gantt.isWorkTime(date);
    // };






    let zoomToFitMode = false;
    ganttModules.zoomToFit = (function (gantt) {
        let cachedSettings = {};

        function saveConfig() {
            const config = gantt.config;
            cachedSettings = {};
            cachedSettings.scales = config.scales;
            cachedSettings.template = gantt.templates.date_scale;
            cachedSettings.start_date = config.start_date;
            cachedSettings.end_date = config.end_date;
            cachedSettings.zoom_level = gantt.ext.zoom.getCurrentLevel();
        }

        function restoreConfig() {
            applyConfig(cachedSettings);
        }

        function applyConfig(config, dates) {
            if (config.scales[0].date) {
                gantt.templates.date_scale = null;
            }
            else {
                gantt.templates.date_scale = config.scales[0].template;
            }

            gantt.config.scales = config.scales;

            if (dates && dates.start_date && dates.start_date) {
                const unit = config.scales[config.scales.length - 1].unit
                gantt.config.start_date = gantt.date.add(dates.start_date, -1, unit);
                gantt.config.end_date = gantt.date.add(gantt.date[unit + "_start"](dates.end_date), 2, unit);
            } else {
                gantt.config.start_date = gantt.config.end_date = null;
            }

            if (config.zoom_level !== undefined) {
                gantt.ext.zoom.setLevel(config.zoom_level);
            }
        }


        function zoomToFit() {
            const project = gantt.getSubtaskDates(),
                areaWidth = gantt.$task.offsetWidth;
            const scaleConfigs = zoomConfig.levels

            let zoomLevel = 0;
            for (let i = 0; i < scaleConfigs.length; i++) {
                zoomLevel = i;
                const level = scaleConfigs[i].scales;
                const lowestScale = level[level.length - 1]
                const columnCount = getUnitsBetween(project.start_date, project.end_date, lowestScale.unit, lowestScale.step || 1);
                if ((columnCount + 2) * gantt.config.min_column_width <= areaWidth) {
                    break;
                }
            }

            if (zoomLevel == scaleConfigs.length) {
                zoomLevel--;
            }

            gantt.ext.zoom.setLevel(scaleConfigs[zoomLevel].name);
            applyConfig(scaleConfigs[zoomLevel], project);

            gantt.render();
        }

        // get number of columns in timeline
        function getUnitsBetween(from, to, unit, step) {
            let start = new Date(from),
                end = new Date(to);
            let units = 0;
            while (start.valueOf() < end.valueOf()) {
                units++;
                start = gantt.date.add(start, step, unit);
            }
            return units;
        }

        return {
            enable: function () {
                zoomToFitMode = true;
                saveConfig();
                zoomToFit();
                gantt.render();
            },
            toggle: function () {
                if (zoomToFitMode) {
                    this.disable();
                } else {
                    this.enable();
                }
            },
            disable: function () {
                zoomToFitMode = false;
                restoreConfig();
                gantt.render();
            }
        };
    })(gantt);


    gantt.templates.grid_row_class = function (start, end, task) {
        return gantt.hasChild(task.id) ? "gantt_parent_row" : "";
    };

    const font_width_ratio = 7;

    gantt.templates.leftside_text = function leftSideTextTemplate(start, end, task) {
        if (getTaskFitValue(task) === "left") {
            return task.text;
        }
        return "";
    };

    gantt.templates.rightside_text = function rightSideTextTemplate(start, end, task) {
        if (getTaskFitValue(task) === "right") {
            return task.text;
        }
        return "";
    };


    gantt.locale.labels.section_rollup = "Rollup";
    gantt.locale.labels.section_hide_bar = "Hide bar";

    gantt.templates.task_text = function taskTextTemplate(start, end, task) {
        if (getTaskFitValue(task) === "center") {
            return task.text;
        }
        return "";
    };

    function getTaskFitValue(task) {
        let taskStartPos = gantt.posFromDate(task.start_date),
            taskEndPos = gantt.posFromDate(task.end_date);

        const width = taskEndPos - taskStartPos;
        const textWidth = (task.text || "").length * font_width_ratio;

        if (width < textWidth) {
            let ganttLastDate = gantt.getState().max_date;
            let ganttEndPos = gantt.posFromDate(ganttLastDate);
            if (ganttEndPos - taskEndPos < textWidth) {
                return "left"
            }
            else {
                return "right"
            }
        }
        else {
            return "center";
        }
    }

    gantt.config.static_background = true;

    gantt.plugins({
        marker: true,
        fullscreen: true,
        critical_path: true,
        auto_scheduling: true,
        tooltip: true,
        undo: true
    });

    const date_to_str = gantt.date.date_to_str(gantt.config.task_date);
    const today = new Date();
    gantt.addMarker({
        start_date: today,
        css: "today",
        text: "Today",
        title: "Today: " + date_to_str(today)
    });

    // const start = new Date();
    // gantt.addMarker({
    //     start_date: start,
    //     css: "status_line",
    //     text: "Start project",
    //     title: "Start project: " + date_to_str(start)
    // });

    // gantt.attachEvent("onTaskCreated", function (item) {
    //     if (item.duration == 1) {
    //         item.duration = 72;
    //     }
    //     return true;
    // });

    gantt.ext.fullscreen.getFullscreenElement = function () {
        return document.querySelector(".demo-main-container");
    };

    // const durationFormatter = gantt.ext.formatters.durationFormatter(
    //     {
    //     enter: "day",
    //     store: "hour",
    //     format: "day",
    //     hoursPerDay: 24,
    //     hoursPerWeek: 40,
    //     daysPerMonth: 30,
    //     short: true
    // }
    //
    // );

    // const linksFormatter = gantt.ext.formatters.linkFormatter({ durationFormatter: durationFormatter });

    // const hourFormatter = gantt.ext.formatters.durationFormatter({
    //     enter: "hour",
    //     store: "hour",
    //     format: "hour",
    //     short: true
    // });

    // const textEditor = { type: "text", map_to: "text" };
    // const dateEditor = { type: "date", map_to: "start_date", min: new Date(), max: new Date() };
    // const durationEditor = { type: "duration", map_to: "duration", formatter: durationFormatter, min: 0, max: 10000 };
    // const hourDurationEditor = { type: "duration", map_to: "duration", formatter: hourFormatter, min: 0, max: 10000 };
    // const predecessorEditor = { type: "predecessor", map_to: "auto", formatter: linksFormatter };

    // gantt.config.columns = [
    //     { name: "", width: 15, resize: false, template: function (task) { return "<span class='gantt_grid_wbs'>" + gantt.getWBSCode(task) + "</span>" } },
    //     { name: "text", tree: true, width: 180, resize: true, editor: textEditor },
    //     { name: "start_date", label: "Start", align: "center", resize: true, width: 80, editor: dateEditor },
    //     {
    //         name: "duration_formatted", label: "Duration", resize: true, align: "center", width: 60, template: function (task) {
    //             return durationFormatter.format(task.duration);
    //         }, editor: durationEditor
    //     },
    //     {
    //         name: "duration_hours", label: "<div style='white-space: normal;line-height: 20px;margin: 10px 0;'>Duration (hours)</div>", resize: true, align: "center", width: 65, template: function (task) {
    //             return hourFormatter.format(task.duration);
    //         }, editor: hourDurationEditor
    //     },
    //     {
    //         name: "predecessors", label: "Predecessors", width: 80, align: "left", editor: predecessorEditor, resize: true, template: function (task) {
    //             const links = task.$target;
    //             const labels = [];
    //             for (let i = 0; i < links.length; i++) {
    //                 const link = gantt.getLink(links[i]);
    //                 labels.push(linksFormatter.format(link));
    //             }
    //             return labels.join(", ")
    //         }
    //     },
    //     { name: "add", "width": 44 }
    // ];


    // gantt.config.lightbox.sections = [
    //     { name: "description", height: 70, map_to: "text", type: "textarea", focus: true },
    //     {
    //         name: "type", type: "typeselect", map_to: "type", filter: function (name, value) {
    //             return !!(value != gantt.config.types.project);
    //         }
    //     },
    //     { name: "time", type: "duration", map_to: "auto", formatter: durationFormatter }
    // ];
    //
    //
    // gantt.config.lightbox.project_sections = [
    //     { name: "description", height: 70, map_to: "text", type: "textarea", focus: true },
    //
    //     { name: "time", type: "duration", readonly: true, map_to: "auto", formatter: durationFormatter }
    // ];
    // gantt.config.lightbox.milestone_sections = [
    //     { name: "description", height: 70, map_to: "text", type: "textarea", focus: true },
    //     {
    //         name: "type", type: "typeselect", map_to: "type", filter: function (name, value) {
    //             return !!(value != gantt.config.types.project);
    //         }
    //     },
    //     { name: "time", type: "duration", single_date: true, map_to: "auto", formatter: durationFormatter }
    // ];

    //Make resize marker for two columns
    gantt.attachEvent("onColumnResizeStart", function (ind, column) {
        if (!column.tree || ind == 0) return;

        setTimeout(function () {
            const marker = document.querySelector(".gantt_grid_resize_area");
            if (!marker) return;
            const cols = gantt.getGridColumns();
            const delta = cols[ind - 1].width || 0;
            if (!delta) return;

            marker.style.boxSizing = "content-box";
            marker.style.marginLeft = -delta + "px";
            marker.style.paddingRight = delta + "px";
        }, 1);
    });

    gantt.templates.tooltip_text = function (start, end, task) {
        const links = task.$target;
        const labels = [];
        for (let i = 0; i < links.length; i++) {
            const link = gantt.getLink(links[i]);
            labels.push(linksFormatter.format(link));
        }
        const predecessors = labels.join(", ");

        let html = "<b>Task:</b> " + task.text + "<br/><b>Start:</b> " +
            gantt.templates.tooltip_date_format(start) +
            "<br/><b>End:</b> " + gantt.templates.tooltip_date_format(end) +
            "<br><b>Duration:</b> " + durationFormatter.format(task.duration);
        if (predecessors) {
            html += "<br><b>Predecessors:</b>" + predecessors;
        }
        return html;
    };

    gantt.config.auto_types = true;
    gantt.config.date_format = "%Y-%m-%d";
    gantt.config.duration_unit = "day";

    // gantt.config.row_height = 23;
    gantt.config.order_branch = "marker";
    gantt.config.order_branch_free = true;
    gantt.config.grid_resize = true;

    gantt.config.auto_scheduling_strict = true;
    gantt.ext.zoom.setLevel("days");






    gantt.attachEvent("onAfterTaskUpdate", function (id, task) {
        parentProgress(id)
    });

    gantt.attachEvent("onTaskDrag", function (id, mode, task, original) {
        if (mode == "progress") {
            parentProgress(id)
        }
    });
    gantt.attachEvent("onAfterTaskAdd", function (id) {
        parentProgress(id)
    });
    gantt.attachEvent("onAfterTaskDelete", function (id, task) {
        if (task.parent) {
            const siblings = gantt.getChildren(task.parent);
            if (siblings.length) {
                parentProgress(siblings[0])
            }
        }
    });

    function parentProgress(id) {
        gantt.eachParent(function (task) {
            const children = gantt.getChildren(task.id);
            let childProgress = 0;
            for (let i = 0; i < children.length; i++) {
                const child = gantt.getTask(children[i])
                childProgress += (child.progress * 100);
            }
            task.progress = childProgress / children.length / 100;
        }, id)
        gantt.render();
    }





    gantt.templates.progress_text = function (start, end, task) {
        return "<span style='text-align:left;'>" + Math.round(task.progress * 100) + "% </span>";
    };



    const navBar = document.querySelector(".demo-actions");

    gantt.event(navBar, "click", function (e) {
        let target = e.target || e.srcElement;

        while (!target.hasAttribute("data-action") && target !== document.body) {
            target = target.parentNode;
        }

        if (target && target.hasAttribute("data-action")) {
            const action = target.getAttribute("data-action");
            if (toolbarMenu[action]) {
                toolbarMenu[action]();
            }
        }
    });

    let config = {
        collapse: false,
        auto_scheduling: false,
        critical_path: false,
        zoom_to_fit: false
    };

    function toggleCheckbox(checkbox, state, disabled) {
        state
            ? checkbox.classList.add("checked")
            : checkbox.classList.remove("checked");
        disabled
            ? checkbox.classList.add("disabled")
            : checkbox.classList.remove("disabled");
    }

    let collapseCheckbox = document.querySelector("#collapse"),
        autoSchedulingCheckbox = document.querySelector("#auto-scheduling"),
        criticalPathCheckbox = document.querySelector("#critical-path"),
        zoomToFitCheckbox = document.querySelector("#zoom-to-fit"),
        scaleComboElement = document.getElementById("scale_combo");

    collapseCheckbox.addEventListener("click", function () {
        let action = "expandAll";

        config.collapse = !config.collapse;
        toggleCheckbox(collapseCheckbox, config.collapse);

        if (config.collapse) {
            action = "collapseAll";
        }

        if (toolbarMenu[action]) {
            toolbarMenu[action]();
        }
    });

    autoSchedulingCheckbox.addEventListener("click", function () {
        let action = "toggleAutoScheduling";

        config.auto_scheduling = !config.auto_scheduling;
        toggleCheckbox(autoSchedulingCheckbox, config.auto_scheduling);

        if (toolbarMenu[action]) {
            toolbarMenu[action]();
        }
    });

    criticalPathCheckbox.addEventListener("click", function () {
        let action = "toggleCriticalPath";

        config.critical_path = !config.critical_path;
        toggleCheckbox(criticalPathCheckbox, config.critical_path);

        if (toolbarMenu[action]) {
            toolbarMenu[action]();
        }
    });

    zoomToFitCheckbox.addEventListener("click", function () {
        let action = "zoomToFit";

        config.zoom_to_fit = !config.zoom_to_fit;
        toggleCheckbox(zoomToFitCheckbox, config.zoom_to_fit);

        if (toolbarMenu[action]) {
            toolbarMenu[action]();
        }
    });

    scaleComboElement.addEventListener("change", function () {
        let scaleValue = this.value;

        gantt.ext.zoom.setLevel(scaleValue);
        config.zoom_to_fit = false;
        zoomToFitMode = false;
        toggleCheckbox(zoomToFitCheckbox, false);
    });
    gantt.init("gantt_here");
    gantt.load('/dhtmlxGantt.getTaskList.do<c:if test="${not empty studyId}">?studyId=${studyId}</c:if>','json');

    var dp = gantt.createDataProcessor({url: "/dhtmlxGantt.callback.do<c:if test="${not empty studyId}">?studyId=${studyId}",mode: "POST"});
    dp.init(gantt);

    dp.attachEvent("onAfterUpdate", function(id, action, tid, response){
        if(action == "error"){
            gantt.clearAll();
            gantt.load('/dhtmlxGantt.getTaskList.do','json');
        }
    });

</script>

</body>
</html>