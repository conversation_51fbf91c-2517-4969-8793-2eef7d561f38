package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.webutil.tools.Log;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

public class URLEncodeUtil {


    /**
     * 对字符串中的中文进行编码
     * @param str
     * @return
     */
    public static String urie(String str) {
        if (org.apache.commons.lang3.StringUtils.isBlank(str)) {
            return "";
        }
        String uri = "";
        try {
            for (int i=0;i<str.length();i++){
                String c=str.substring(i,i+1);
                if(c.matches("[\\u4e00-\\u9fa5]")){
                    c= URLEncoder.encode(c, "UTF-8");
                }
                uri=uri+c;
            }
        } catch (UnsupportedEncodingException e) {
            Log.error("url中文参数编码失败：{}",e);
            return "";
        }
        return uri;
    }

    /**
     * 测试
     * @param args
     */
    public static void main(String[] args) {
        System.out.println(urie("http://tarly.aoscdn.com/resource/output/20230418/35/SOP-CDSC-001F2eCRF审批表V2.0.pdf"));
    }
}
