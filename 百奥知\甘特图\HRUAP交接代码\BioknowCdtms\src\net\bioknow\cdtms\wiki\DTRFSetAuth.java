package net.bioknow.cdtms.wiki;

import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;


public class DTRFSetAuth extends DTRecordFuncAction {



	public boolean canUse(int auth, String tableid, Long recordid) {

		try {

			if (!StringUtils.equals(tableid,"xxyjl1")) {
				return false;
			}
			Map currentUserMap = SessUtil.getSessInfo().getUser();
			String[] currentRole = String.valueOf(currentUserMap.get("ryjs_org")).split(",");

			Long currUserId = Long.valueOf(SessUtil.getSessInfo().getUserid());
			String projectid = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng = new DAODataMng(projectid);
			Map wikiMap = daoDataMng.getRecord(tableid, recordid);
			Long userid = (Long) wikiMap.get("userid");
			String[] AuthorizedRole = ("AccountAdmin").split(",");

//			StringUtils.equals(wikiMap.get("userid")

			if (currUserId<=2 || currUserId.equals(userid)) {
				return true;
			}


		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}

	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
		try {

			this.forwardByUri(request,response,"/wiki.setAuthButton.do");

		} catch (Exception e) {
			Log.error("",e);
		}
	}

	public FuncInfoBean getFIB(String tableid) {
		FuncInfoBean fib = new FuncInfoBean();
		fib.setName("设置权限");
		fib.setType(FuncInfoBean.FUNCTYPE_AJAXMENU);


		return fib;
	}

}
