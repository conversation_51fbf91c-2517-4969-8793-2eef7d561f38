package net.bioknow.cdtms.schedule;

import com.google.gson.Gson;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbcore.schema.Schema;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.uapplug.publicworkflow.FaceWorkflowInjection;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WFInjectionHandlereUpdateSchedule implements FaceWorkflowInjection{
	
	//工作流之后触发
	@Override
	public void afterProcess(String projectid, String tableid, Map mapVV,Map mapVAll, String param, Map mapFunction, 
			HttpServletRequest request,String userid, String note) {

		try {
		String scheduleId = (String) mapVAll.get("scheduleId");
		String actual_start_date = (String) mapVAll.get("actual_start_date");
		String actual_end_date = (String) mapVAll.get("actual_end_date");
		String milestone = (String) mapVAll.get("milestone");
		if (StringUtils.isEmpty(scheduleId)) return;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm");

			HashMap<Object, Object> studyTaskToSaveMap = new HashMap<>();
		studyTaskToSaveMap.put("id",Long.valueOf(scheduleId));
		studyTaskToSaveMap.put("actual_end_date",sdf.parse(actual_end_date));

			if (StringUtils.equals(milestone,"1")) {
				studyTaskToSaveMap.put("actual_start_date",sdf.parse(actual_end_date));

			}else{
				studyTaskToSaveMap.put("actual_start_date",sdf.parse(actual_start_date));


			}

		studyTaskToSaveMap.put("work_table_id",tableid);
		studyTaskToSaveMap.put("work_record_id",(Long) mapVV.get("id"));
		Map userMap = SessUtil.getSessInfo().getUser();
		studyTaskToSaveMap.put("completer",userMap.get("username"));
		studyTaskToSaveMap.put("progress",1d);
		DAODataMng daoDataMng = new DAODataMng(projectid);
		daoDataMng.saveRecord("schedule",studyTaskToSaveMap);
		} catch (Exception e) {
			Log.error("",e);
		}


	}


	//
	@Override
	public void afterProcessBatch(String projectid, String tableid, String rids, Map mapVFunction,
            HttpServletRequest request, String userid, String note) {
		
	}
	//工作流之前触发
	@Override
	public String beforeProcess(String projectId, String tableid, Map dataMap, Map mapVAll, String param, Map mapVFunction) {
		try {
		DtrefDAO dtrefDAO = new DtrefDAO(projectId);
		String RefField = dtrefDAO.getRefField("xsht", tableid);
			DAODataMng daoDataMng = new DAODataMng(projectId);

			int scheduleCountInt = daoDataMng.count("schedule", "obj.study_id=" + dataMap.get(RefField));


		if (StringUtils.isEmpty(RefField) || scheduleCountInt==0) {
			return "";
		}
		DAODbApi daoDbApi = new DAODbApi(projectId);


//		studyTaskList = null;
		List<Map> workRenameList = daoDataMng.listRecord("schedule_work_rename", "obj.table_id ='"+tableid +"'", null, 10);

		String getStudyTaskListWhere="obj.type in ('task','milestone') and obj.study_id='" + dataMap.get(RefField) + "' and obj.progress!=1 and obj.name like '%"+(String) daoDbApi.getMapTable(tableid).get("name")+"%'";
		if (!CollectionUtils.isEmpty(workRenameList)) {

			Map workRenameMap = workRenameList.get(0);
			String filedId = (String) workRenameMap.get("filed_id");
			if (StringUtils.isNotEmpty(filedId)) {

				String RenameValue = (String) dataMap.get(filedId);
				Schema schema = daoDbApi.getFieldType(tableid, filedId);
				Map mapF = daoDbApi.getMapFieldCopy(tableid, filedId);


				String Rename = schema.formatToOutput(tableid, mapF, dataMap);

				if (StringUtils.isNotEmpty(Rename)) {
					getStudyTaskListWhere = "obj.type in ('task','milestone') and obj.study_id='" + dataMap.get(RefField) + "' and obj.progress!=1 and (obj.name like '%" + (String) daoDbApi.getMapTable(tableid).get("name") + "%' or obj.name like '%" + Rename + "%')";
				}
			}

		}

		List<Map> 	studyTaskList = daoDataMng.listRecord("schedule",getStudyTaskListWhere , "obj.planned_start_date asc", 10);

		if (CollectionUtils.isEmpty(studyTaskList)){

			return "请在工作计划中创建该工作的计划";

		};

		String  milestone= (String) mapVAll.get("milestone");
		if (!StringUtils.equals(milestone,"1")) {
			String actual_start_date = (String) mapVAll.get("actual_start_date");

			if (StringUtils.isEmpty(actual_start_date)) {
				return "实际开始时间必须填写";
			}

		}


		String actual_end_date = (String) mapVAll.get("actual_end_date");

		if (StringUtils.isEmpty(actual_end_date)) {
			return "实际结束时间必须填写";
		}

		String scheduleId = (String) mapVAll.get("scheduleId");

		if (StringUtils.isEmpty(scheduleId)) {
			return "工作计划必须选中一项";
		}
		} catch (Exception e) {
			Log.error("",e);
		}
		return "";
	}
	//
	@Override
	public String beforeProcessBatch(String projectid, Map mapVFunction) {
		return "";
	}
	//页面显示
	@Override
	public String getFormHtml(String projectId, String tableid, Map dataMap, String param) {
		try {

			Gson gson = new Gson();


			DAODbApi daoDbApi = new DAODbApi(projectId);
			DAODataMng daoDataMng = new DAODataMng(projectId);
			DtrefDAO dtrefDAO = new DtrefDAO(projectId);
			String RefField = dtrefDAO.getRefField("xsht", tableid);

			int scheduleCountInt = daoDataMng.count("schedule", "obj.study_id=" + dataMap.get(RefField));

			if (StringUtils.isEmpty(RefField) || scheduleCountInt==0) {
				return "";
			}
			List<Map> workRenameList = daoDataMng.listRecord("schedule_work_rename", "obj.table_id ='"+tableid +"'", null, 10);
			String getStudyTaskListWhere="obj.type in ('task','milestone') and obj.study_id='" + dataMap.get(RefField) + "' and obj.progress!=1 and obj.name like '%"+(String) daoDbApi.getMapTable(tableid).get("name")+"%'";

			String startDateStr= "";
			String endDateStr = "";

			boolean Milestone=false;
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm");
			String startDateFiledId = null;
			String endDateFiledId = null;

			if (CollectionUtils.isNotEmpty(workRenameList)) {

				Map workRenameMap = workRenameList.get(0);
				String filedId = (String) workRenameMap.get("filed_id");

				if (StringUtils.isNotEmpty(filedId)) {
					String RenameValue = (String) dataMap.get(filedId);
					Schema schema = daoDbApi.getFieldType(tableid, filedId);
					Map mapF = daoDbApi.getMapFieldCopy(tableid, filedId);


					String Rename = schema.formatToOutput(tableid, mapF, dataMap);

					if (StringUtils.isNotEmpty(Rename)) {
						getStudyTaskListWhere="obj.type in ('task','milestone') and obj.study_id='" + dataMap.get(RefField) + "' and obj.progress!=1 and (obj.name like '%"+(String) daoDbApi.getMapTable(tableid).get("name")+"%' or obj.name like '%"+Rename+"%')";
					}
				}



				startDateFiledId = (String) workRenameMap.get("strat_date_filed_id");
				endDateFiledId = (String) workRenameMap.get("end_date_filed_id");
				String isMilestone = (String) workRenameMap.get("is_milestone");

				if (StringUtils.isNotEmpty(startDateFiledId)) {

					Object startDate = dataMap.get(startDateFiledId);

					if (ObjectUtils.isNotEmpty(startDate)) {
						startDateStr=sdf.format(startDate);
					}
				}


				if (StringUtils.isNotEmpty(endDateFiledId)) {

					Object endDate = dataMap.get(endDateFiledId);

					if (ObjectUtils.isNotEmpty(endDate)) {
						endDateStr=sdf.format(endDate);
					}
				}

				if (StringUtils.equals(isMilestone,"1") ) {
					Milestone=true;
				}


			}

			List<Map> studyTaskList = daoDataMng.listRecord("schedule", getStudyTaskListWhere, "obj.planned_start_date asc", 100);



			if (CollectionUtils.isEmpty(studyTaskList)){
					return "";
 			}



			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

			String options="";
			for (Map studyTaskMap : studyTaskList) {

				Long TaskId = (Long) studyTaskMap.get("id");
				String TaskName = (String) studyTaskMap.get("name");

				String TaskNamePlanStratDate = simpleDateFormat.format((Date) studyTaskMap.get("planned_start_date"));
				String TaskNamePlanFinishDate =simpleDateFormat.format((Date) studyTaskMap.get("planned_end_date"));

				options+="<option value=\""+TaskId+"\">"+TaskName+"/"+TaskNamePlanStratDate+"~"+TaskNamePlanFinishDate+"</option>";

			}


//			Map id = studyTaskList.stream().findFirst().get("id");
			if (StringUtils.isEmpty(startDateFiledId)) {

				Long taskFirstId = (Long) studyTaskList.get(0).get("id");

				List<Map> linkList = daoDataMng.listRecord("schedule_links", "obj.target_schedule_id=" + taskFirstId, "id", 1);

				if (CollectionUtils.isNotEmpty(linkList)) {
					Long scheduleId = (Long) linkList.get(0).get("schedule_id");

					if (scheduleId != null) {


						Map beforeTaskMao = daoDataMng.getRecord("schedule", scheduleId);

						if (!MapUtils.isEmpty(beforeTaskMao)) {
							Date beforeactualEndDate = (Date) beforeTaskMao.get("actual_end_date");
							if (beforeactualEndDate != null) {
								startDateStr = sdf.format(new Date(beforeactualEndDate.getTime()+(1000*60*60*24)));

							}

						}

					}


				}
			}
			if (StringUtils.isEmpty(endDateFiledId)) {
				Date currentDate = new Date();
				endDateStr = sdf.format(currentDate);
			}



			//Log.info(this.getClass().getPackage().getName()+" "+lang.get("当前用户：",true));
			//username
			String html = null;
			if (!Milestone) {
				html = "<span>完成选中的工作计划:</span>" +
						"<br/><select name=\"scheduleId\" id=\"scheduleId\">\n" +
						options +
						"    </select><br/>" +
						"<span>实际开始时间：</span><input type=\"datetime-local\"  name=\"actual_start_date\" id=\"actual_start_date\" value=\"" + startDateStr + "\" ><br/>" +
						"<span>实际结束时间：</span><input type=\"datetime-local\" name=\"actual_end_date\" id=\"actual_end_date\"  value=\"" + endDateStr + "\"><br/>"
						;
			}else{

				html = "<span>完成选中的工作计划:</span>" +
						"<br/><select name=\"scheduleId\" id=\"scheduleId\">\n" +
						options +
						"    </select><br/>" +
						"<span>里程碑完成时间：</span><input type=\"datetime-local\" name=\"actual_end_date\" id=\"actual_end_date\"  value=\"" + endDateStr + "\"><br/>" +
						"<input hidden type=\"text\" name=\"milestone\" id=\"milestone\"  value=\"1\"><br/>";

			}



			return  html;
		} catch (Exception e) {
			Log.error("",e);
		}
		return "";
	}
	//
	@Override
	public String getName() {
		return "UpdateSchedule";
	}
	//
	@Override
	public String getNote() {
		return "更新进度完成时间";
	}

}
