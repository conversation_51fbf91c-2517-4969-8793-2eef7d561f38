package net.bioknow.cdtms.wiki;

import net.bioknow.mvc.tools.Language;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.face.ListPageInitFace;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ListPageInitHandlerLernProg implements ListPageInitFace {
	private Map lernProg = null;
	@Override
	public boolean canUse(int auth, String tableid, String projectId) {
        try {

			if (StringUtils.equals(tableid,"xxyjl1")) {
				return true;
			}
        }
        catch (Exception e){
            Log.error("",e);
        }
        return false;
    }
	@Override
	public String getColName() {
		Language lang = Language.getInstance(this.getClass());
		return lang.get("学习进展");
	}
	@Override
    public String getJsInc() {

		return "";
    }

	@Override
	public Map getValueMap() {
		return this.lernProg;
	}
	@Override
	public void init(List listV,String tableid,String projectId) {
		try {

			DAODataMng daoDataMng = new DAODataMng(projectId);
			this.lernProg=new HashMap<>();
			for (Object dataObj : listV) {
				Map dataMap = (Map) dataObj;
				Long wikiId = (Long) dataMap.get("id");
				String instanceName = (String) dataMap.get("wjtm");

				List wikiAuthList = daoDataMng.listRecord("wiki_auth", "obj.wiki_id=" + wikiId, null, 100);

				int lernerCount = 0;
				int lernerlernedCount=0;
				if (CollectionUtils.isEmpty(wikiAuthList)) {
					lernerCount = daoDataMng.count("ryjbzl", "obj.zt=1");
					lernerlernedCount = daoDataMng.count("knowledge_base_learner", "obj.knowledge_base_id=" + wikiId + " and obj.status='10' and EXISTS(select obj2.id from Ryjbzl as obj2 where obj2.id=obj.account_id and obj2.zt=1)");

				}else {
					lernerCount = daoDataMng.count("ryjbzl", "obj.zt=1 and EXISTS(select obj2.id from Wiki_auth as obj2 where obj2.wiki_id="+wikiId+" and nvl(obj2.is_elective,'0')!='1' and obj.auth_dept like concat('%',obj2.dept_name,'%') and obj2.auth_code<=obj.auth_code)");
					lernerlernedCount = daoDataMng.count("ryjbzl", "obj.zt=1 and EXISTS(select obj2.id from Knowledge_base_learner as obj2 where obj2.knowledge_base_id=" + wikiId+" and obj2.status='10' and obj.id=obj2.account_id) and EXISTS(select obj2.id from Wiki_auth as obj2 where obj2.wiki_id="+wikiId+" and nvl(obj2.is_elective,'0')!='1' and obj.auth_dept like concat('%',obj2.dept_name,'%') and obj2.auth_code<=obj.auth_code)");
//					lernerlernedCount = daoDataMng.count("knowledge_base_learner", "obj.knowledge_base_id=" + wikiId + " and obj.status='10' and EXISTS(select obj2.id from Ryjbzl as obj2 where obj2.id=obj.account_id and obj2.zt=1)");

				}
				String lernProgHtml = "<a href=\"javascript:void(0);\" onclick=\"window.top.webmask_show('" + instanceName + "','/dtplugins_dbview.show.do?prjid=&uuid=4B5C6E28983C4FDA93D709BA8814E9F2&where=id=" + wikiId + "&simplelist=true&readonly=true',800,600);return false;\">" + lernerlernedCount + "/" + lernerCount + "</a>";
				this.lernProg.put("rid" + wikiId, lernProgHtml);
			}
		} catch (Exception e) {
			Log.error("",e);
		}
	}

}
