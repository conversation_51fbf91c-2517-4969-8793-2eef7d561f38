package net.bioknow.cdtms.esign;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import net.bioknow.dbplug.wordreport.CNT_WORDREPORT;
import net.bioknow.dbplug.wordreport.DAOWordreport;
import net.bioknow.dbplug.wordreport.DAOWordreportCFG;
import net.bioknow.mvc.RootAction;
import net.bioknow.mvc.tools.WebPath;
import net.bioknow.passport.datamng.DAOPPData;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.FormUtil;
import net.bioknow.uap.schemaplug.attach.AttachDAO;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.webutil.fileup.DAOFileup;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


public class ActioneSignIntegrate extends RootAction {

    public void Receive(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {


            InputStream is = request.getInputStream();
            String ReceiveMsg = IOUtils.toString(is, StandardCharsets.UTF_8);

            is.close();
            if (StringUtils.isBlank(ReceiveMsg)) {
                response.getOutputStream().write("{\"code\":\"0\",\"status\":\"200\",\"msg\":\"success\"}".getBytes("UTF8"));
                response.getOutputStream().close();
            }
//            Gson gson = new GsonBuilder().
//                    registerTypeAdapter(new TypeToken<Map<String,Object>>(){}.getType(),new ObjectTypeAdapterRewrite()).
//                    setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES).
//                    setDateFormat("yyyy-MM-dd'T'HH:mm:ss").create();
//            String projectId="cdtmsen_val";
            String projectId=request.getParameter("projectid");
            DAODataMng daoDataMng = new DAODataMng(projectId);

            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();
            Map<String, Object> ReceiveMap = gson.fromJson(ReceiveMsg, new TypeToken<Map<String, Object>>() {
            }.getType());


            Integer callBackEnum = (Integer) ReceiveMap.get("callBackEnum");

            String callBackDesc = (String) ReceiveMap.get("callBackDesc");

            Map callBackProcessVOMap = (Map) ReceiveMap.get("callBackProcessVO");

            String backProcessId = (String) callBackProcessVOMap.get("processId");
            String backFlowStatus = callBackProcessVOMap.get("flowStatus") + "";

            List esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.sign_flow_id='" + backProcessId + "'", null, 1);

            if (CollectionUtils.isEmpty(esignInstanceList)) {

                response.getOutputStream().write("{\"code\":\"0\",\"status\":\"200\",\"msg\":\"success\"}".getBytes("UTF8"));
                response.getOutputStream().close();
                return;
            }

            Map esignInstanceMap = (Map) esignInstanceList.get(0);
            Long esignInstanceId = (Long) esignInstanceMap.get("id");
            Integer esignInstanceVersion = (Integer) esignInstanceMap.get("version");
            String tableid = (String) esignInstanceMap.get("tableid");
            Long recordid = (Long) esignInstanceMap.get("recordid");
            Long studyid = (Long) esignInstanceMap.get("study_id");


            List<Map<String, Object>> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id='" + esignInstanceId + "'", null, 100);
            List<Map<String, Object>> esignFileList = daoDataMng.listRecord("esign_file", "obj.esign_instance_id='" + esignInstanceId + "'", null, 100);





            Map esignInstanceToSaveMap = new HashMap<>();


            esignInstanceToSaveMap.put("id", esignInstanceId);
            esignInstanceToSaveMap.put("status", backFlowStatus);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


            Map esignLogToSaveMap = new HashMap<>();
            esignLogToSaveMap.put("esign_instance_id", esignInstanceId);
            esignLogToSaveMap.put("tableid", tableid);
            esignLogToSaveMap.put("recordid", recordid);
            esignLogToSaveMap.put("study_id", studyid);
            esignLogToSaveMap.put("esign_status", callBackEnum+"");
            esignLogToSaveMap.put("msg", ReceiveMsg);
            esignLogToSaveMap.put("esign_desc", callBackDesc);
            List<Map<String, Object>> signerList = (List) callBackProcessVOMap.get("signerList");

            Map<String, Object> signerMap = signerList.stream().max(Comparator.comparing((Map map) -> map.get("signDate") == null ? "" : (String) map.get("signDate"))).get();

            esignInstanceToSaveMap.put("version", esignInstanceVersion);
            Map esignSignerToSaveMap = new HashMap<>();

            switch (callBackEnum) {
                case 0: {
                    //流程开启业务
                    String backProcessBeginTime = (String) callBackProcessVOMap.get("processBeginTime");
                    Date processBeginTime = dateFormat.parse(backProcessBeginTime);
                    esignInstanceToSaveMap.put("esign_begin_date", processBeginTime);
                    esignInstanceToSaveMap.put("esign_begin_date", processBeginTime);

                    esignLogToSaveMap.put("execute_date", processBeginTime);
                    esignLogToSaveMap.put("name", (String) esignInstanceMap.get("initiator"));

                    break;


                }
                case 1: {



                    String userCode = (String) signerMap.get("userCode");
                    String userName = (String) signerMap.get("userName");
                    Date signDate = dateFormat.parse((String) signerMap.get("signDate"));
                    Map<String, Object> esignSignerMap = esignSignerList.stream().filter((Map map) -> StringUtils.equals(userCode, (String) map.get("user_code"))).findFirst().get();

                    esignLogToSaveMap.put("execute_date", signDate);
                    esignLogToSaveMap.put("name", userName);

                    esignSignerToSaveMap.put("id", (Long) esignSignerMap.get("id"));
                    esignSignerToSaveMap.put("status", signerMap.get("signStatus") + "");
                    esignSignerToSaveMap.put("execute_date", signDate);
                    daoDataMng.save("esign_signer", esignSignerToSaveMap);

                    break;
                }
                case 2: {

                    //流程完成业务

                    String backprocessEndTime = (String) callBackProcessVOMap.get("processEndTime");
                    Date processEndDate = dateFormat.parse(backprocessEndTime);
                    esignInstanceToSaveMap.put("esign_end_date", processEndDate);


                    esignLogToSaveMap.put("execute_date", processEndDate);
                    esignLogToSaveMap.put("name", "SA");

                    List<Map<String, Object>> signFileList = (List) signerMap.get("signFileList");

                    String backStrAttachToSave="";
                    for (Map<String, Object> esignFileMap : esignFileList) {

                        Map esignFileTOSaveMap =new HashMap<>();

                        String esignFileKey = (String) esignFileMap.get("esign_file_key");

                        Map<String, Object> backSignFileMap = signFileList.stream().filter((Map map) -> StringUtils.equals(esignFileKey, (String) map.get("fileKey"))).findFirst().get();

                        String  signedFileKey = (String) backSignFileMap.get("signedFileKey");
                        String filePath = WebPath.getRootPath() + DAOFileup.tempfolder + "/eSign";

                        eSignIntegrateUtil.downloadByNIO("/file/v1/getFileStream?fileKey="+signedFileKey, filePath, signedFileKey,projectId);

                        esignFileTOSaveMap.put("esign_signed_file_key",signedFileKey);
                        esignFileTOSaveMap.put("id",(Long) esignFileMap.get("id"));
                        AttachDAO attachDAO = new AttachDAO(projectId);

                        String fileIndex = (String) esignFileMap.get("file");
                        String fileName = fileIndex.split("\\*")[0];


                        File fileLocal = new File(filePath+"/"+signedFileKey);
                        if (!fileLocal.exists() && !fileLocal.isDirectory()) {
                            fileLocal.mkdir();
                        } else {
                            String fileUuid = attachDAO.saveFile(fileLocal, "esign_file");
                            String backFileUuid = attachDAO.saveFile(fileLocal, tableid);
                            String  strAttachToSave ="" + fileName.toString().split("\\*")[0] + "*" + fileUuid + "|" ;
                            backStrAttachToSave+="" + fileName.toString().split("\\*")[0] + "*" + backFileUuid + "|" ;
                            esignFileTOSaveMap.put("signed_file", strAttachToSave);
                        }
                        daoDataMng.save("esign_file",esignFileTOSaveMap);

                        Map signDataMap = daoDataMng.getRecord(tableid, recordid);
                        Map signEngineMap = daoDataMng.getRecord("esign_engine", (Long) esignInstanceMap.get("esign_engine_id"));

                        signDataMap.put(signEngineMap.get("back_file_filed"),backStrAttachToSave);
                        daoDataMng.save(tableid,signDataMap);





                    }


                    break;
                }
                case 3: {

                    //流程过期业务
                    String backprocessEndTime = (String) callBackProcessVOMap.get("processEndTime");
                    Date processEndDate = dateFormat.parse(backprocessEndTime);

                    esignLogToSaveMap.put("execute_date", processEndDate);
                    esignLogToSaveMap.put("name", "SA");


                    esignInstanceToSaveMap.put("esign_end_date", processEndDate);

                    break;
                }
                case 4: {
                    Log.info("我是流程签署截止前处理啊");
                    break;
                }
                case 5: {
                    Log.info("我是流程签署人拒签处理啊");

                    String backprocessEndTime = (String) callBackProcessVOMap.get("processEndTime");
                    Date processEndDate = dateFormat.parse(backprocessEndTime);
                    Map refuseSignerMap = (Map) callBackProcessVOMap.get("refuseSigner");
                    String refuseName = (String) refuseSignerMap.get("userName");
                    String refuseUserCode = (String) refuseSignerMap.get("userCode");
                    Date refuseDate = dateFormat.parse((String) refuseSignerMap.get("refuseDate"));


                    esignLogToSaveMap.put("execute_date", refuseDate);
                    esignLogToSaveMap.put("name",refuseName);


                    esignInstanceToSaveMap.put("esign_end_date", processEndDate);

                    Map<String, Object> esignSignerMap = esignSignerList.stream().filter((Map map) -> StringUtils.equals(refuseUserCode, (String) map.get("user_code"))).findFirst().get();

                    esignSignerToSaveMap.put("id", (Long) esignSignerMap.get("id"));
                    esignSignerToSaveMap.put("status", refuseSignerMap.get("signStatus") + "");
                    esignSignerToSaveMap.put("execute_date", refuseDate);
                    esignSignerToSaveMap.put("refuse_cause", refuseSignerMap.get("refuse_cause"));
                    daoDataMng.save("esign_signer", esignSignerToSaveMap);


                    break;



                }
                case 6: {

                    Log.info("我是流程作废处理啊");
                    String processEndTime = (String) callBackProcessVOMap.get("processEndTime");
                    Date processEndDate =new Date();

                    if(!StringUtils.isEmpty(processEndTime)){

                         processEndDate = dateFormat.parse(processEndTime);


                    }
                    String cancelCause = (String) callBackProcessVOMap.get("cancelCause");
                    Map cancelActorVOMap = (Map) callBackProcessVOMap.get("cancelActorVO");

                    String  cancelor = (String) cancelActorVOMap.get("userName");


                    esignLogToSaveMap.put("execute_date", processEndDate);
                    esignLogToSaveMap.put("name",cancelor);


                    esignInstanceToSaveMap.put("cancelor", cancelor);
                    esignInstanceToSaveMap.put("cancel_cause", cancelCause);

                    break;
                }
                case 7: {
                    Log.info("我是流程完成作废处理啊");

                    String processEndTime = (String) callBackProcessVOMap.get("processEndTime");
                    Date processEndDate =new Date();

                    if(!StringUtils.isEmpty(processEndTime)){

                        processEndDate = dateFormat.parse(processEndTime);


                    }                    String cancelCause = (String) callBackProcessVOMap.get("cancelCause");
                    Map cancelActorVOMap = (Map) callBackProcessVOMap.get("cancelActorVO");

                    String  cancelor = (String) cancelActorVOMap.get("userName");


                    esignLogToSaveMap.put("execute_date", processEndDate);
                    esignLogToSaveMap.put("name",cancelor);


//                    esignInstanceToSaveMap.put("cancelor", cancelor);
//                    esignInstanceToSaveMap.put("cancel_cause", cancelCause);
                    break;
                }
                case 8: {
                    Log.info("我是流程邀请签署通知处理啊");
                    break;
                }
                case 9: {
                    Log.info("我是流程签署失败处理啊");
                    break;
                }

                default: {
                    response.getOutputStream().write("{\"code\":\"0\",\"status\":\"200\",\"msg\":\"success\"}".getBytes("UTF8"));
                    response.getOutputStream().close();
                    return;
                }
            }


            daoDataMng.save("esign_instance", esignInstanceToSaveMap);
            daoDataMng.save("esign_log", esignLogToSaveMap);


            response.getOutputStream().write("{\"code\":\"0\",\"status\":\"200\",\"msg\":\"success\"}".getBytes("UTF8"));
            response.getOutputStream().close();

        } catch (IOException e) {

            Log.error("", e);

        } catch (Exception e) {
            Log.error("", e);
        }

    }


    public void Revoke(HttpServletRequest request, HttpServletResponse response) {

        try {

            String projectId = SessUtil.getSessInfo().getProjectid();

            String signFlowId = request.getParameter("signFlowId");
            String revokeReson = request.getParameter("revokeReson");
            Long esignInstanceId = Long.valueOf(request.getParameter("esignInstanceId"));

            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();

            Map revokeParamMap = new HashMap<>();
            revokeParamMap.put("signFlowId", signFlowId);
            revokeParamMap.put("reason", revokeReson);

            String revokeParamJson = gson.toJson(revokeParamMap);

            List<Map<String, String>> listR = DAOeSignIntegrate.listRule(projectId,"1");

            String esignUrl = listR.get(0).get(DAOeSignIntegrate.esignUrl);
            String revokeMsg = eSignIntegrateUtil.sendPost(esignUrl + "/esign-signs/v1/signFlow/revoke", revokeParamJson,projectId);

            Map revokeMsgMap = gson.fromJson(revokeMsg, new TypeToken<Map<String, Object>>() {
            }.getType());

            if (!StringUtils.equals(String.valueOf(revokeMsgMap.get("code")) ,"200")) {
                String message = (String) revokeMsgMap.get("message");
                response.getOutputStream().write(message.getBytes("UTF8"));
                response.getOutputStream().close();
                return;

            }
            String projectid = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectid);


            Map esignInstanceToSaveMap = new HashMap<>();

            esignInstanceToSaveMap.put("id", esignInstanceId);
            esignInstanceToSaveMap.put("revoke_msg", revokeMsg);

            daoDataMng.save("esign_instance", esignInstanceToSaveMap);
//			daoDataMng.listRecord("")

            response.getOutputStream().write("200".getBytes("UTF8"));
            response.getOutputStream().close();



        } catch (Exception e) {
            Log.error("", e);
        }

    }


    public void eSignRegister(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            Map userMap = SessUtil.getSessInfo().getUser();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            String esignInstanceJson = request.getParameter("eSigndata");
            Long esignInstanceId = Long.valueOf(request.getParameter("esignInstanceId"));
//		String signersJson = request.getParameter("signersJson");

//            daoDataMng.listRecordUI()

            Map currentSignInstanceMap = daoDataMng.getRecord("esign_instance", esignInstanceId);
            String tableid = request.getParameter("tableid");
            String recordid = request.getParameter("recordid");
            Map currentUserMap = (Map) SessUtil.getSessInfo().getUser();
            String initiatorUserCode = (String) currentUserMap.get("domain_accounts");
            if (StringUtils.isEmpty(initiatorUserCode)) {
                initiatorUserCode = eSignIntegrateUtil.getUserCode((String) currentUserMap.get("loginid"),null,projectId);
                if (StringUtils.equals(initiatorUserCode, "404")) {
                    response.getOutputStream().write("未查询到您的域帐号，请核对手机号填写是否准确".getBytes("UTF8"));
                    response.getOutputStream().close();
                }

                currentUserMap.put("domain_accounts", initiatorUserCode);
            }
            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();


            Map esignInstanceMap = gson.fromJson(esignInstanceJson, new TypeToken<Map<String, Object>>() {
            }.getType());

            String signers = (String) esignInstanceMap.get("signers");


            ArrayList<Object> signerInfosList = new ArrayList<>();
            String[] signersArr = signers.split(",");

            for (String signerEmail : signers.split(",")) {

//				signerMap.put("email", signer);
//                    signerMap.put("esign_file_id", fileId);

//				signersFileJoinList.add(signerMap);

                String userCode = eSignIntegrateUtil.getUserCode(signerEmail,(Long)esignInstanceMap.get("study_id"),projectId);
                Map signerInfosMap = new HashMap<>();
                signerInfosMap.put("userType", 1);
                signerInfosMap.put("userCode", userCode);
                signerInfosMap.put("signMode", signersArr.length==1?0:1);
                signerInfosMap.put("signNode", "1");

                signerInfosList.add(signerInfosMap);
            }


            esignInstanceMap.put("initiatorUserCode", initiatorUserCode);

//		Map analysisSignersResultMap = eSignIntegrateUtil.analysisSigners(signers);
//
//		List signerInfosList = (List) analysisSignersResultMap.get("signerInfosList");
            String CNSMsg = eSignIntegrateUtil.eSignRegister(esignInstanceId, esignInstanceMap, signerInfosList, tableid, projectId);


            Map CNSMsgMap = gson.fromJson(CNSMsg, new TypeToken<Map<String, Object>>() {
            }.getType());
            Map esignInstanceToSaveMap = new HashMap<>();

            if (!StringUtils.equals(String.valueOf(CNSMsgMap.get("code")) ,"200")) {
                String message = (String) CNSMsgMap.get("message");
                response.getOutputStream().write(message.getBytes("UTF8"));
                response.getOutputStream().close();
                return;

            }


            Map GUUDataMap = (Map) CNSMsgMap.get("data");
            String signFlowId = (String) GUUDataMap.get("signFlowId");
            List signUrlInfosList = (List) GUUDataMap.get("signUrlInfos");
            esignInstanceToSaveMap.put("sign_flow_id", signFlowId);
            esignInstanceToSaveMap.put("id", esignInstanceId);
            esignInstanceToSaveMap.put("status", "1");
            esignInstanceToSaveMap.put("esign_msg", CNSMsg);
            String signFlowExpireTime = esignInstanceMap.get("signFlowExpireTime") + ":00";
            SimpleDateFormat DateFormatYMDHMS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date signFlowExpireDate = DateFormatYMDHMS.parse(signFlowExpireTime);

            esignInstanceToSaveMap.put("remark", esignInstanceMap.get("remark"));
            esignInstanceToSaveMap.put("subject", esignInstanceMap.get("subject"));
            esignInstanceToSaveMap.put("sign_expire_data", signFlowExpireDate);
            esignInstanceToSaveMap.put("initiator", userMap.get("username"));




            ArrayList<Map> signUrlInfosToSaveList = new ArrayList<>();
            for (Object signUrlInfosItem : signUrlInfosList) {
                Map signUrlInfosToSaveMap = new HashMap<>();
                Map signUrlInfosMap = (Map) signUrlInfosItem;
                signUrlInfosToSaveMap.put("name", signUrlInfosMap.get("userName"));
                signUrlInfosToSaveMap.put("user_code", signUrlInfosMap.get("userCode"));
                signUrlInfosToSaveMap.put("esign_url", signUrlInfosMap.get("signUrlShort"));
                signUrlInfosToSaveMap.put("esign_instance_id", esignInstanceId);
                signUrlInfosToSaveMap.put("status", "0");
                signUrlInfosToSaveList.add(signUrlInfosToSaveMap);
//				daoDataMng.save("esign_signer",signUrlInfosToSaveMap );
//				daoDataMng.saveBatch("esign_signer", (List) signUrlInfosToSaveList, 2L,null);

            }

            daoDataMng.save("esign_instance", esignInstanceToSaveMap);
            daoDataMng.saveBatch("esign_signer", (List) signUrlInfosToSaveList, 2L, null);
            response.getOutputStream().write("200".getBytes());
            response.getOutputStream().close();
//			List signersFileJoinList = (List) analysisSignersResultMap.get("signersFileJoinList");
//			Map emailUsercodeMap = (Map) analysisSignersResultMap.get("emailUsercodeMap");


//			List SignersFileToSaveList = new ArrayList<>();
//
////			for (Object signersFileJoinItem : signersFileJoinList) {
////				Map signersFileJoinMap = (Map) signersFileJoinItem;
////
////				Map SignersFileToSaveMap = new HashMap<>();
////				String email = (String) signersFileJoinMap.get("email");
////				SignersFileToSaveMap.put("esign_instance_id",esignInstanceId);
////				SignersFileToSaveMap.put("esign_file_id",(Long)signersFileJoinMap.get("esign_file_id"));
////				SignersFileToSaveMap.put("email",email);
////				SignersFileToSaveMap.put("user_code",emailUsercodeMap.get(email));
////				SignersFileToSaveList.add(SignersFileToSaveMap);
////
////			}
//
//			daoDataMng.saveBatch("esign_file_signer", (List) SignersFileToSaveList, 2L,null);


        } catch (Exception e) {
            Log.error("", e);

        } finally {

        }
    }

    public void eSign(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {


            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);

            String tableid = request.getParameter("tableid");
            Long recordid = Long.valueOf(request.getParameter("recordid"));

            String anewSign = request.getParameter("anewSign");
            String anewInstanceId = request.getParameter("id");

            if(StringUtils.isNotEmpty(anewSign) && StringUtils.isNotEmpty(anewInstanceId)){

              Map anewInstanceToSaveMap = new HashMap<>();
                anewInstanceToSaveMap.put("id",Long.valueOf(anewInstanceId));
                anewInstanceToSaveMap.put("active","0");

                daoDataMng.save("Esign_instance",anewInstanceToSaveMap);

            }

            List esignInstanceList = daoDataMng.listRecord("Esign_instance", "obj.active = 1 and obj.tableid='" + tableid + "'   and obj.recordid=" + recordid, null, 1);
            List<Map<String, String>> eSignEngineList = daoDataMng.listRecord("esign_engine", "obj.tableid='" + tableid + "'", null, 100);

            Map eSignEngineMap = eSignEngineList.get(0);
            String sginRole = (String) eSignEngineMap.get("sgin_role");

            DtrefDAO dtrefDAO = new DtrefDAO(projectId);

            String eSignDataRefField = dtrefDAO.getRefField("xsht", tableid);

            Map eSignDataMap = daoDataMng.getRecord(tableid, recordid);

            Long studyId = (Long) eSignDataMap.get(eSignDataRefField);

            String stuydUser = eSignIntegrateUtil.getStudyUser(studyId, sginRole,projectId);

            if (CollectionUtils.isNotEmpty(esignInstanceList)) {
                Map esignInstanceMap = (Map) esignInstanceList.get(0);
                Long esignInstanceId = (Long) esignInstanceMap.get("id");
                Map esignInstanceUIMap=eSignIntegrateUtil.getRecordUI("esign_instance",esignInstanceMap,projectId);
                List<Map> eSignFileList = daoDataMng.listRecord("esign_file", "obj.tableid='" + tableid + "' and obj.esign_instance_id=" + esignInstanceId, null, 100);


                for (Map eSignFileMap : eSignFileList) {

                    String fileIndex = (String) eSignFileMap.get("file");
                    eSignFileMap.put("filename", fileIndex.split("\\*")[0]);
                    eSignFileMap.put("file_uuid", fileIndex.substring(fileIndex.lastIndexOf("*") + 1, fileIndex.lastIndexOf("|")));

                }
                request.setAttribute("eSignFileList", eSignFileList);
                request.setAttribute("esignInstanceMap", esignInstanceUIMap);
                request.setAttribute("stuydUser", stuydUser);

                this.forward(request, response, "eSign");

                return;

            }


            Map esignInstanceToSaveMap = new HashMap();
            esignInstanceToSaveMap.put("tableid", tableid);
            esignInstanceToSaveMap.put("recordid", recordid);
            esignInstanceToSaveMap.put("active", "1");
            esignInstanceToSaveMap.put("study_id", studyId);
            esignInstanceToSaveMap.put("edition_name", eSignDataMap.get(eSignEngineMap.get("edition_filed")));
            esignInstanceToSaveMap.put("edition_date", eSignDataMap.get(eSignEngineMap.get("edition_date_filed")));
            esignInstanceToSaveMap.put("status", "0");
            esignInstanceToSaveMap.put("esign_engine_id", eSignEngineMap.get("id"));


//			List esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.status != 0  and obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "'", null, 1);
//			esignInstanceToSaveMap = (Map)esignInstanceList.get(0);

//			Long esignEngineId = (Long) esignInstanceToSaveMap.get("esign_engine_id");


//			List ownedEngineIdList = daoDataMng.list("select esign_engine_id from Esign_file where recordid="+recordid+" and esign_instance_id="+esignInstanceToSaveMap.get("id"),100,1);

//			String ownedEngineId = StringUtils.join(ownedEngineIdList, ",");


//			String geteSignEngineListWhere="obj.tableid='" + tableid + "'";
//			if (!StringUtils.isEmpty(ownedEngineId))  geteSignEngineListWhere+=" and obj.id not in ("+ownedEngineId+")";

            List<Map> esignFileToSaveList = new ArrayList();
            String eSignEngineTemplate = (String) eSignEngineMap.get("template");
            String[] templateArray = eSignEngineTemplate.split(",");

            for (String template : templateArray) {
                Map<Object, Object> esignFileMap = new HashMap<>();
                AttachDAO attachDAO = new AttachDAO(projectId);
                DAOWordreportCFG daoWordreportCFG = new DAOWordreportCFG(projectId);
                DAOWordreport daoWordreport = new DAOWordreport(projectId);
                List WordreportRulelist = daoWordreportCFG.getRuleByTableid(tableid);
                Map templateRuleMap = null;


                for (int j = 0; j < WordreportRulelist.size(); j++) {
                    Map WordreportRulelistMap = (Map) WordreportRulelist.get(j);
                    String fn = (String) WordreportRulelistMap.get(CNT_WORDREPORT.filename);
                    if (fn.equals(template))
                        templateRuleMap = WordreportRulelistMap;
                }

                String urlroot = "http://" + request.getServerName() + ":" + request.getServerPort()
                        + request.getContextPath();

                String docUri = daoWordreport.getDocFile(templateRuleMap, eSignDataMap, urlroot);

                String filePath = WebPath.getRootPath() + docUri;
                daoWordreport.convertDocToPdf(filePath, "2", "2");

//                filePath = filePath.substring(0, filePath.lastIndexOf(".")) + ".pdf";
//

                File filePdf = new File(WebPath.getRootPath() + docUri + ".pdf");// .doc.pdf
                int p = docUri.lastIndexOf(".");
                docUri = docUri.substring(0, p) + ".pdf";
                File fileLocal = new File(WebPath.getRootPath() + docUri);// .pdf

                filePdf.renameTo(fileLocal);// 重命名
                String fileName = docUri.substring(docUri.lastIndexOf("/") + 1);


                if (!fileLocal.exists() && !fileLocal.isDirectory()) {
                    fileLocal.mkdir();
                } else {
                    String fileUuid = attachDAO.saveFile(fileLocal, "esign_file");
                    String strAttachToSave = "" + fileName + "*" + fileUuid + "|";

                    esignFileMap.put("file", strAttachToSave);
                }

                List<Map<String, String>> listR = DAOeSignIntegrate.listRule(projectId,"1");

                String esignUrl = listR.get(0).get(DAOeSignIntegrate.esignUrl);


                GsonBuilder gsonBuilder = new GsonBuilder();
                gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
                }.getType(), new MapDeserializerDoubleAsIntFix());
                Gson gson = gsonBuilder.create();

                HashMap GUUParamMap = new HashMap<>();
                GUUParamMap.put("requestID", FormUtil.getFormId());
                GUUParamMap.put("type", 0);
                String GUUParamJson = gson.toJson(GUUParamMap);
                String GUUMsg = eSignIntegrateUtil.sendPost(esignUrl + "/file/v1/generateUploadUrl", GUUParamJson,projectId);
                if (StringUtils.isEmpty(GUUMsg)) {
                    response.getOutputStream().write("服务器连接失败".getBytes());
                    response.getOutputStream().close();
                    return;
                }

                Map GUUMsgMap = gson.fromJson(GUUMsg, new TypeToken<Map<String, Object>>() {
                }.getType());
                if (!StringUtils.equals( String.valueOf(GUUMsgMap.get("code")),"200")) {
                    response.getOutputStream().write(String.valueOf(GUUMsgMap.get("message")).getBytes());
                    response.getOutputStream().close();
                    return;

                }
                Map GUUDataMap = (Map) GUUMsgMap.get("data");

                String uploadFileMsgJson  = eSignIntegrateUtil.uploadFile(String.valueOf(GUUDataMap.get("url")), WebPath.getRootPath() + docUri, fileName.replace(" ", ""));

                Map uploadFileMsgMap = gson.fromJson(uploadFileMsgJson, new TypeToken<Map<String, Object>>() {
                }.getType());

                if (!StringUtils.equals( String.valueOf(uploadFileMsgMap.get("code")),"200")) {

                    response.getOutputStream().write(String.valueOf(uploadFileMsgMap.get("message")).getBytes());
                    response.getOutputStream().close();
                    return;
                }
                Map uploadFileDataMap = (Map) uploadFileMsgMap.get("data");



//                String uploadFileMsg = eSignIntegrateUtil.uploadFile(WebPath.getRootPath() + docUri, fileName);

//                if (StringUtils.equals(uploadFileMsg.split("||")[0],"400")) {
//
//                }
               String eSignfileKey= (String) uploadFileDataMap.get("fileKey");

                esignFileMap.put("tableid", tableid);
                esignFileMap.put("recordid", recordid);
                esignFileMap.put("study_id", studyId);
                esignFileMap.put("esign_file_key", eSignfileKey);
                esignFileToSaveList.add(esignFileMap);

            }

//			Map<String, List<Map<String, Object>>> signerGroupEmailMap = esignFileToSaveList.stream().collect(Collectors.toSet())
//					.collect(Collectors.groupingBy((Map map1) -> (String) map1.get("email")));





            for (Map eSignFileMap : esignFileToSaveList) {
//				Map eSignCurrentEngineList = (Map) daoDataMng.listRecord("esign_engine","obj.id='" + eSignFileMap.get("esign_engine_id") + "'",null,1).get(0);
//				Optional<Map<String, String>> eSignEngineFirstOptional = eSignEngineList.stream().filter(m -> m.get("id").equals(esign_engine_id)).findFirst();
//				if (eSignEngineFirstOptional.isPresent()) {
//					String eSignEngine = eSignEngineFirstOptional.get().get("esign");
//				}
//				Map<String, String> sign_role = eSignEngineCollect.get(0);
                String fileIndex = (String) eSignFileMap.get("file");
//				eSignFileMap.put("filename",URLEncoder.encode(fileIndex.substring(0, fileIndex.lastIndexOf("*")),"UTF-8"));
                eSignFileMap.put("filename", fileIndex.split("\\*")[0]);
                eSignFileMap.put("file_uuid", fileIndex.substring(fileIndex.lastIndexOf("*") + 1, fileIndex.lastIndexOf("|")));

            }

            for (Map esignFileToSaveMap : esignFileToSaveList) {

                esignFileToSaveMap=eSignIntegrateUtil.getRecordUI("esign_file",esignFileToSaveMap,projectId);

            }



            daoDataMng.save("Esign_instance", esignInstanceToSaveMap);

            Long esignInstanceId = (Long) esignInstanceToSaveMap.get("id");

            for (Map esignFileToSaveItem : esignFileToSaveList)
                esignFileToSaveItem.put("esign_instance_id", esignInstanceId);

            daoDataMng.saveBatch("esign_file", esignFileToSaveList, 2l, null);


            Map esignInstanceToSaveUIMap =eSignIntegrateUtil.getRecordUI("esign_instance",esignInstanceToSaveMap,projectId);
            request.setAttribute("eSignFileList", esignFileToSaveList);
//            request.setAttribute("eSignDataMap", eSignDataMap);
            request.setAttribute("esignInstanceMap", esignInstanceToSaveUIMap);
            request.setAttribute("stuydUser", stuydUser);

            this.forward(request, response, "eSign");


        } catch (Exception e) {
            Log.error("", e);

        }
    }


    public void View(HttpServletRequest request, HttpServletResponse response) throws IOException {

        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            String tableid = request.getParameter("tableid");
            Map currentUserMap = SessUtil.getSessInfo().getUser();

            Long recordid = Long.valueOf(request.getParameter("recordid"));
            List esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.active =1 and obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "'", null, 1);
            List<Map<String,Object>> esignLogList = daoDataMng.listRecord("esign_log", "obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "'", null, 100);
            SimpleDateFormat sdf_d = new SimpleDateFormat("yyyy-MM-dd");

//            for (Map<String, Object> esignLogMap : esignLogList) {
//                 eSignIntegrateUtil.getRecordUI("esign_log", esignLogMap);
//            }

            for (int i = 0; i < esignLogList.size(); i++) {

                Map<String, Object> esignLogMap = esignLogList.get(i);
                esignLogMap.put("sn",i+1);

                DateFormat formatTo = new SimpleDateFormat("a HH:mm");

                esignLogMap.put("timeStamp",formatTo.format(esignLogMap.get("execute_date")));

                Integer esignStatus = Integer.valueOf((String) esignLogMap.get("esign_status"));


                switch (esignStatus) {
                    case 0: esignLogMap.put("desc","创建签署流程");break;
                    case 1: esignLogMap.put("desc","签署完成"); break;
                    case 2: esignLogMap.put("desc","所有用户均已签署，签署成功"); break;
                    case 3: esignLogMap.put("desc","签署方签署完成"); break;
                    case 4: esignLogMap.put("desc","签署截止前通知"); break;
                    case 5: esignLogMap.put("desc","拒签"); break;
                    case 6: esignLogMap.put("desc","提交作废流程"); break;
                    case 7: esignLogMap.put("desc","作废完成"); break;
                    default:esignLogMap.put("desc","");
                    }



            }



            esignLogList.sort(Comparator.comparing((Map<String,Object> o) -> String.valueOf(o.get("id"))).reversed());





            Map<String, List<Map<String, Object>>>  esignLogGroupDayMap = esignLogList.stream().collect(Collectors.groupingBy((Map map1) ->sdf_d.format(map1.get("execute_date"))));




            Map esignInstanceMap = (Map) esignInstanceList.get(0);
            Map esignInstanceUIMap = eSignIntegrateUtil.getRecordUI("esign_instance", esignInstanceMap,projectId);
            Long userid = (Long) esignInstanceMap.get("userid");
            DAOPPData dao = new DAOPPData(projectId);
            String initiatorName = (String) dao.getUserById(userid).getRealName();

            SimpleDateFormat sdf_s = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            esignInstanceUIMap.put("initiatDate", sdf_s.format(esignInstanceMap.get("createtime")));


            Long esignInstanceId = (Long) esignInstanceMap.get("id");
            String Msg = (String) esignInstanceMap.get("esign_msg");

            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();

            Map CNSMsgMap = gson.fromJson(Msg, new TypeToken<Map<String, Object>>() {
            }.getType());
//			Map msgData = (Map) CNSMsgMap.get("data");
//			List<Map> signUrlInfosList = (List) msgData.get("signUrlInfos");
//			Map<String, Map> signUrlInfosListToMap = signUrlInfosList.stream().collect(Collectors.toMap((Map map) -> (String) map.get("user_code"),t -> t));
//


            List<Map<String, Object>> esignFileList = daoDataMng.listRecord("esign_file", "obj.esign_instance_id=" + esignInstanceId, null, 100);

//            esignInstanceUIMap.put("esign_url", sdf_s.format(esignInstanceMap.get("createtime")));

//			List<Map<String, Object>> esignFileSignerList = daoDataMng.listRecord("esign_file_signer", "obj.esign_instance_id=" + esignInstanceId, null, 100);
            List<Map<String, Object>> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + esignInstanceId, null, 100);

            Map<String, Map> esignSignerListToMap = esignSignerList.stream().collect(Collectors.toMap((Map map) -> (String) map.get("user_code"), t -> t));

            esignInstanceUIMap.put("Signer", esignSignerListToMap.get(currentUserMap.get("domain_accounts")));


//			for (Map<String, Object> esignFileSignerMap : esignFileSignerList) {
//				esignFileSignerMap.put("SignerInfoMap",esignSignerListToMap.get(esignFileSignerMap.get("user_code")));
//
//			}

//			Map<Long, List<Map<String, Object>>> esignFileSignerListToMap = esignFileSignerList.stream().collect(Collectors.groupingBy((Map map1) -> (Long) map1.get("esign_file_id")));


            for (Map esignFileMap : esignFileList) {


                String file = (String) esignFileMap.get("file");
                String signedFile = (String) esignFileMap.get("signed_file");

                String fileIndex = StringUtils.isNotEmpty(signedFile)?signedFile:file;

                esignFileMap.put("filename", fileIndex.split("\\*")[0]);
//				eSignFileMap.put("filename",URLEncoder.encode(fileIndex.substring(0, fileIndex.lastIndexOf("*")),"UTF-8"));


                esignFileMap.put("file_uuid", fileIndex.substring(fileIndex.lastIndexOf("*") + 1, fileIndex.lastIndexOf("|")));


//				Long esignFileId = (Long) esignFileMap.get("id");
//				List fileSignerList = (List) esignFileSignerListToMap.get(esignFileId);
//				esignFileMap.put("fileSignerList",fileSignerList);
            }
////			esignInstanceMap.put("esignFiles",esignFileList);
//			DAODbApi daoDbApi = new DAODbApi(projectId);
//
//			Map mapF = daoDbApi.getMapFieldCopy(tableid,"studyid");
//
//
//			Schema studyid_Schema = daoDbApi.getFieldType(tableid, "studyid");


            List esignSignerUIList = new ArrayList<>();
            for (Map esignSignertMap : esignSignerList) {
                esignSignerUIList.add(eSignIntegrateUtil.getRecordUI("esign_signer",esignSignertMap,projectId));
            }

//			String str = studyid_Schema.formatToOutput(tableid, mapF, eSignDataMap);
            request.setAttribute("esignInstanceMap", esignInstanceUIMap);
            request.setAttribute("esignFileList", esignFileList);
            request.setAttribute("esignLogGroupDayMap", esignLogGroupDayMap);
            request.setAttribute("fileSignerList", esignSignerUIList);


            this.forward(request, response, "View");


        } catch (Exception e) {
            Log.error("", e);
        } finally {


        }
    }


}
