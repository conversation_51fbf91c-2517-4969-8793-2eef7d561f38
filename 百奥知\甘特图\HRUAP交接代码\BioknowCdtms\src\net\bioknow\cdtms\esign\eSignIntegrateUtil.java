package net.bioknow.cdtms.esign;


import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import net.bioknow.dbplug.qywxintegrate.DAOQYWXIntegrate;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbcore.schema.CNT_Schema;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.FormUtil;
import net.bioknow.uapplug.dbview.DAODbview;
import net.bioknow.uapplug.dbview.DbviewUtil;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.net.URL;
import java.nio.channels.Channels;
import java.nio.channels.FileChannel;
import java.nio.channels.ReadableByteChannel;
import java.util.*;
import java.util.stream.Collectors;

import java.util.HashMap;

public class eSignIntegrateUtil {
//    private static String esignSecret = "Jhof8N4y15S1bGM3";
//    private static String esignProject = "1000003";
//    public static String esignUrl = "https://esignapi-tst.hengrui.com:8199";
//    private static String businessTypeCode = "ecae90dabba0b5f0b244a15e21535ac3";
////    private static String rebackUrl = "https://cdtms-tst.hengrui.com/eSignIntergrate.Receive.do";
//    private static String rebackUrl = "http://dev.bioknow.net:7780/eSignIntergrate.Receive.do?projectid=defaultdb";
//    private static String organizationCode = "4e60fff8e64a43a4aa88d6fb6fd2bb8e";
//
//


    public static String eSignRegister(Long esignInstanceId, Map esignInstanceMap, List signerInfos, String tableid, String projectId) {
        String CASParamJson ="";
        try {

            List<Map<String, String>> listR = DAOeSignIntegrate.listRule(projectId,"1");
            String esignSecret = listR.get(0).get(DAOeSignIntegrate.esignSecret);
            String esignProject = listR.get(0).get(DAOeSignIntegrate.esignProject);
            String esignUrl = listR.get(0).get(DAOeSignIntegrate.esignUrl);
            String businessTypeCode = listR.get(0).get(DAOeSignIntegrate.businessTypeCode);
            String rebackUrl = listR.get(0).get(DAOeSignIntegrate.rebackUrl);
            String organizationCode = listR.get(0).get(DAOeSignIntegrate.organizationCode);

            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();



            DAODataMng daoDataMng = new DAODataMng(projectId);
            //获取文件公共信息
            HashMap CASParamMap = new HashMap<>();
            CASParamMap.put("subject", esignInstanceMap.get("subject"));
            CASParamMap.put("businessTypeCode", businessTypeCode);
            CASParamMap.put("remark", esignInstanceMap.get("remark"));
            CASParamMap.put("signFlowExpireTime", esignInstanceMap.get("signFlowExpireTime") + ":00");
            CASParamMap.put("signNotifyUrl", rebackUrl);

//        CASParamMap.put("readComplete", "true");
//        CASParamMap.put("advertisement", "true");


            //获取发起人信息
            Map initiatorInfo = new HashMap<>();
            initiatorInfo.put("userCode", esignInstanceMap.get("initiatorUserCode"));
            initiatorInfo.put("userType", 1);
            initiatorInfo.put("organizationCode", organizationCode);

            CASParamMap.put("initiatorInfo", initiatorInfo);


            //获取文件信息
            ArrayList<Object> signFiles = new ArrayList<>();

            List eSignFileList = daoDataMng.listRecord("esign_file", "obj.tableid='" + tableid + "' and obj.esign_instance_id=" + esignInstanceId, null, 100);


            for (Object eSignFileItem : eSignFileList) {
                Map eSignFileMap = (Map) eSignFileItem;
                Map signFiles1 = new HashMap<>();
                signFiles1.put("fileKey", eSignFileMap.get("esign_file_key"));
                signFiles1.put("fileOrder", 1);
                signFiles.add(signFiles1);
            }

            CASParamMap.put("signFiles", signFiles);

            //获取签署人信息
            CASParamMap.put("signerInfos", signerInfos);

            String CNSParamJson = gson.toJson(CASParamMap);

            Log.info(CNSParamJson);
            CASParamJson = sendPost(esignUrl + "/esign-signs/v1/signFlow/createAndStart", CNSParamJson,projectId);

            return CASParamJson;

        } catch (Exception e) {
           Log.error("",e);
        }
        return CASParamJson;
    }

    public static void downloadByNIO(String url, String saveDir, String fileName,String projectId) {



        ReadableByteChannel rbc = null;
        FileOutputStream fos = null;
        FileChannel foutc = null;
        try {

            List<Map<String, String>> listR = DAOeSignIntegrate.listRule(projectId,"1");

            String esignUrl = listR.get(0).get(DAOeSignIntegrate.esignUrl);


            rbc = Channels.newChannel(new URL(esignUrl+url).openStream());
            File file = new File(saveDir, fileName);
            file.getParentFile().mkdirs();
            fos = new FileOutputStream(file);
            foutc = fos.getChannel();
            foutc.transferFrom(rbc, 0, Long.MAX_VALUE);

        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            Log.error("",e);
        } finally {
            if (rbc != null) {
                try {
                    rbc.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (foutc != null) {
                try {
                    foutc.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }
//    public static String uploadFile(String filePath, String fileName)  {
//        try {
//
//            List<Map<String, String>> listR = DAOeSignIntegrate.listRule(projectId,"1");
//
//            String esignUrl = listR.get(0).get(DAOeSignIntegrate.esignUrl);
//        Gson gson = new Gson();
//        HashMap GUUParamMap = new HashMap<>();
//        GUUParamMap.put("requestID", FormUtil.getFormId());
//        GUUParamMap.put("type", 0);
//        String GUUParamJson = gson.toJson(GUUParamMap);
//        String GUUMsg = sendPost(esignUrl + "/file/v1/generateUploadUrl", GUUParamJson,projectId);
//        Map GUUMsgMap = gson.fromJson(GUUMsg, Map.class);
//
//        if ((Double) GUUMsgMap.get("code") == 200) {
//            Map GUUDataMap = (Map) GUUMsgMap.get("data");
//            String uploadFileMsg = null;
//
//            uploadFileMsg = uploadFile(String.valueOf(GUUDataMap.get("url")), filePath, fileName.replace(" ", ""));
//
//            Map uploadFileMsgMap = gson.fromJson(uploadFileMsg, Map.class);
//
//            if ((Double) uploadFileMsgMap.get("code") == 200) {
//                Map uploadFileDataMap = (Map) uploadFileMsgMap.get("data");
//                return (String) uploadFileDataMap.get("fileKey");
//            }else {
//
//                return "400||"+(String) uploadFileMsgMap.get("message");
//            }
//
//
//         }
//        return "400";
//        } catch (Exception e) {
//            Log.error("", e);
//        }
//
//        return "400";
//    }

    public static String sendPost(String url, String data,String projectId) {

        String response = null;

        try {

            CloseableHttpClient httpclient = null;
            CloseableHttpResponse httpresponse = null;

            try {
                List<Map<String, String>> listR = DAOeSignIntegrate.listRule(projectId,"1");

                String esignProject = listR.get(0).get(DAOeSignIntegrate.esignProject);
                String esignSecret = listR.get(0).get(DAOeSignIntegrate.esignSecret);


                httpclient = HttpClients.createDefault();
                HttpPost httppost = new HttpPost(url);
                httppost.setHeader("x-timevale-project-id", esignProject);
                httppost.setHeader("x-timevale-signature", HMACSHA256(data, esignSecret));
                httppost.setHeader("Content-Type", "application/json");
                StringEntity stringentity = new StringEntity(data,ContentType.create("text/json", "UTF-8"));
                httppost.setEntity(stringentity);
                httpresponse = httpclient.execute(httppost);
                response = EntityUtils.toString(httpresponse.getEntity());
            } finally {

                if (httpclient != null) {
                    httpclient.close();
                }

                if (httpresponse != null) {
                    httpresponse.close();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return response;


    }


    private static String HMACSHA256(String data, String key) {
        try {


        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");

        SecretKeySpec secret_key = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");

        sha256_HMAC.init(secret_key);

        byte[] array = sha256_HMAC.doFinal(data.getBytes("UTF-8"));

        StringBuilder sb = new StringBuilder();

        for (byte item : array) {

            sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));

        }

        return sb.toString();
        } catch (Exception e) {
            Log.error("", e);

        }
        return "";
    }


    public static String uploadFile(String url, String filePath, String filename) throws Exception {

        String result = null;
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("text/plain");
        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("file", filename,
                        RequestBody.create(MediaType.parse("application/octet-stream"),
                                new File(filePath)))
                .build();
        Request request = new Request.Builder()
                .url(url)
                .method("POST", body)
                .build();
        Response response = client.newCall(request).execute();
        if (response.code() == 200) {
            result = response.body().string();

        }

        return result;
    }


    public static String getUserCode(String email, Long studyId,String projectId) {
        try {

            List<Map<String, String>> listR = DAOeSignIntegrate.listRule(projectId,"1");

            String esignUrl = listR.get(0).get(DAOeSignIntegrate.esignUrl);


//            String Projectid = SessUtil.getSessInfo().getProjectid();

            DAODataMng daoDataMng = new DAODataMng(projectId);
//        ArrayUtils

            String mobile = null;
            Long accountId=null;

            List accountList = (List) daoDataMng.listRecord("ryjbzl", "obj.loginid='" + email + "'", null, 1);


            if (!CollectionUtils.isEmpty(accountList)) {

            Map accountMap = (Map) accountList.get(0);
                if (!StringUtils.isEmpty((String) accountMap.get("domain_accounts"))) {
                    return (String) accountMap.get("domain_accounts");
                }

                accountId= (Long) accountMap.get("id");

            List currentUserInfoList = (List) daoDataMng.listRecord("ryjl", "obj.email='" + email + "'", null, 1);
                if (!CollectionUtils.isEmpty(currentUserInfoList)) {
                    Map UserInfoMap = (Map) currentUserInfoList.get(0);
                    mobile= (String) UserInfoMap.get("sjh");
                }

            }else if(studyId!=null){

                List partnerUserList = daoDataMng.listRecord("cra", "obj.studyid=" + studyId + " and obj.cra_email='" + email + "'", null, 100);
                if (!CollectionUtils.isEmpty(partnerUserList)) {

                    Map partnerUserMap = (Map) partnerUserList.get(0);
                    mobile= (String) partnerUserMap.get("cra_phone");
                }

            }

            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();



            HashMap GUCParamMap = new HashMap<>();
            GUCParamMap.put("mobile", mobile);
            String GUCParamJson = gson.toJson(GUCParamMap);


            String GUCMsg = eSignIntegrateUtil.sendPost(esignUrl + "/manage/v1/innerUsers/detail", GUCParamJson,projectId);
            Map GUCMsgMap = gson.fromJson(GUCMsg, new TypeToken<Map<String, Object>>() {
            }.getType());

            if (StringUtils.equals(String.valueOf(GUCMsgMap.get("code")) ,"200")) {

                List GUUDataList = (List) GUCMsgMap.get("data");
                Map GUUDataMap = (Map) GUUDataList.get(0);
                String userCode = (String) GUUDataMap.get("userCode");

                if (!CollectionUtils.isEmpty(accountList)) {

                    Map accountToSaveMap = new HashMap();
                    accountToSaveMap.put("id", accountId);
                    accountToSaveMap.put("domain_accounts", userCode);

                    daoDataMng.save("ryjbzl", accountToSaveMap);
                }
                return userCode;


            }
            return GUCMsg;

        } catch (Exception e) {
             Log.error("", e);
        }
        return "";
    }


    public static Map analysisSigners(String singers,String projectId) {
        try {
//            String projectId = SessUtil.getSessInfo().getProjectid();

            Map<String, Object> analysisSignersResult = new HashMap<>();
            Map emailUsercodeMap = new HashMap<>();

            DAODataMng daoDataMng = new DAODataMng(projectId);


            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();



            List signersList = gson.fromJson(singers, new TypeToken<Map<String, Object>>() {
            }.getType());

            List<Map<String, Object>> signersFileJoinList = new ArrayList();


            ArrayList<Object> signerInfosList = new ArrayList<>();

            for (Object signersItem : signersList) {
                Map signersMap = (Map) signersItem;

                Long fileId = Long.valueOf((String) signersMap.get("id"));


                String[] signers = String.valueOf(signersMap.get("signers")).split(",");
                for (String signer : signers) {

                    HashMap signerMap = new HashMap();
                    signerMap.put("email", signer);
                    signerMap.put("esign_file_id", fileId);

                    signersFileJoinList.add(signerMap);

                }

            }




            Map<String, List<Map<String, Object>>> signerGroupEmailMap = signersFileJoinList.stream().collect(Collectors.groupingBy((Map map1) -> (String) map1.get("email")));

            Iterator signerGroupEmailIterator = signerGroupEmailMap.keySet().iterator();


            while (signerGroupEmailIterator.hasNext()) {

                String email = (String) signerGroupEmailIterator.next();
                List signerGroupEmailFilesList = (List) signerGroupEmailMap.get(email);

                String userCode = getUserCode(email, null,projectId);


                Map signerInfosMap = new HashMap<>();
                signerInfosMap.put("userType", 1);
                signerInfosMap.put("userCode", userCode);
                signerInfosMap.put("signMode", "1");
                signerInfosMap.put("signNode", "1");
                ArrayList<Object> sealInfosList = new ArrayList<>();

                emailUsercodeMap.put(email, userCode);


                for (Object signerGroupEmailFilesItem : signerGroupEmailFilesList) {

                    Map signerGroupEmailFileMap = (Map) signerGroupEmailFilesItem;
                    Map sealInfosMap = new HashMap<>();
                    Map esignFileRecord = null;

                    esignFileRecord = daoDataMng.getRecord("esign_file", (Long) signerGroupEmailFileMap.get("esign_file_id"));

                    sealInfosMap.put("fileKey", esignFileRecord.get("esign_file_key"));


                    sealInfosList.add(sealInfosMap);
                }
                signerInfosMap.put("sealInfos", sealInfosList);
                signerInfosList.add(signerInfosMap);

            }
            analysisSignersResult.put("signerInfosList", signerInfosList);
            analysisSignersResult.put("signersFileJoinList", signersFileJoinList);
            analysisSignersResult.put("emailUsercodeMap", emailUsercodeMap);

            return analysisSignersResult;
        } catch (Exception e) {
            Log.error("", e);
        }
//        daoDataMng.saveBatch("esign_file_signer",signersListToSave,null,null);


        return null;
    }


    public static String getStudyUser(Long studyId,String sginRole,String projectId){
        String eSignerSelector = "";
        try {
//            String  projectId = SessUtil.getSessInfo().getProjectid();


                DAODbview daoDbview = new DAODbview(projectId);
                int stuydUserMapIndex = daoDbview.getRuleIndexByName("项目参与人");
                List stuydUserMap = DbviewUtil.getDataList(projectId, stuydUserMapIndex + "", "studyid='" + studyId + "' or studyid is null", "", 999, 1);

                for (Object item2 : stuydUserMap) {
                    Map selectorMap = (Map) item2;
                    String selectorName = (String) selectorMap.get("name");
                    String selectorEmail = (String) selectorMap.get("email");
                    String selectorRole = (String) selectorMap.get("role");
                    eSignerSelector = eSignerSelector + "{name:'" + selectorName + "',value:'" + selectorEmail + "'";
                    if ((","+sginRole+",").contains("," + selectorRole + ","))   eSignerSelector = eSignerSelector + ",selected: true";
                    eSignerSelector = eSignerSelector + "},";


                }

        } catch (Exception e) {
            Log.error("",e);
        }
        return eSignerSelector;
    }


    public static Map getRecordUI(String tableid, Map mapV,String projectId) throws Exception {
//        String  projectId = SessUtil.getSessInfo().getProjectid();
        DAODbApi dadao = new DAODbApi(projectId);
        List listF = dadao.getListFieldByTableId(tableid);
        Map mapNew = new HashMap();
        for (int i = 0; i < listF.size(); i++) {
            Map mapF = (Map) listF.get(i);
            String fid = (String) mapF.get(CNT_Schema.id);
            String v = dadao.getFieldType(tableid, fid).formatToUI(tableid, mapF, mapV);
            mapNew.put(fid, v);
        }

        mapNew.put("id", mapV.get("id"));
        return mapNew;
    }



}
