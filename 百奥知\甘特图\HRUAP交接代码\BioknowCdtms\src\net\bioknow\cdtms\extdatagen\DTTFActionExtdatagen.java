package net.bioknow.cdtms.extdatagen;

import net.bioknow.uap.dbdatamng.function.DTTableFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class DTTFActionExtdatagen extends DTTableFuncAction {

    public boolean canUse(int auth,String tableid ,String refinfo,boolean readonly) {
		if (StringUtils.equals(tableid,"study_ext_test_data")) {
			return true;
		}

		return false;

	}
    
    public void doAction(HttpServletRequest request, HttpServletResponse response,FuncParamBean fpb) {
		String studyid = fpb.getRefinfo().split("__")[1];
		request.setAttribute("studyid",studyid);
		this.redirectByUri(request, response,"/extdatagen.ajaxmenu.do");
		return;
    }

    public FuncInfoBean getFIB(String tableid) {
		FuncInfoBean fib = new FuncInfoBean();
		try{
			fib.setName("生成测试数据");
			fib.setType(FuncInfoBean.FUNCTYPE_AJAXMENU);

		} catch (Exception e) {
			Log.error("",e);
		}
		return fib;
	}
    
}
