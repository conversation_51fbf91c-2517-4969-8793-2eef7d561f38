package net.bioknow.cdtms.schedule;


import com.google.gson.Gson;
import net.bioknow.mvc.RootAction;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class ActionSchedulerIntegrate extends RootAction {

    public void show(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            String studyId = request.getParameter("studyId");
            if (StringUtils.isNotEmpty(studyId)) {
                request.setAttribute("studyId", request.getParameter("studyId"));
            }
            this.forward(request, response, "show");
            return;
        } catch (Exception e) {
            Log.error("", e);
        }


    }

    private List<Map> filterTasksWithoutSubtasks(List<Map> tasks) {
        // 创建一个新列表，用于存储不包含子任务的任务
        List<Map> filteredTasks = new ArrayList<>();

        // 创建一个集合，用于存储具有子任务的任务的 ID
        Set<Long> tasksWithSubtasks = new HashSet<>();

        // 遍历任务列表，找到具有子任务的任务的 ID
        for (Map task : tasks) {
            if (task.get("parent_schedule_id") != null) {
                tasksWithSubtasks.add((Long) task.get("parent_schedule_id"));
            }
        }

        // 遍历任务列表，将不具有子任务的任务添加到新列表中
        for (Map task : tasks) {
            if (!tasksWithSubtasks.contains(task.get("id"))) {
                filteredTasks.add(task);
            }
        }

        return filteredTasks;
    }

    public void getTaskList(HttpServletRequest request, HttpServletResponse response) {
        try {


            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            String studyId = request.getParameter("studyId");
            Map userMap = SessUtil.getSessInfo().getUser();
            String accountId = (String) userMap.get("id");
            String getScheduleWhere = "1=2";
            String getUserWhere = "1=2";
            String getStudyWhere = "1=2";
            if (StringUtils.isNotEmpty(studyId)) {
                getScheduleWhere = "obj.type='task' and concat(',',obj.owner,',') like '%,"+accountId+",%' and obj.study_id=" + studyId;
                getUserWhere = "obj.studyid=" + studyId;
                getStudyWhere = "obj.id=" + studyId;
            } else {

                request.setAttribute("tableid", "xsht");
                String authwhere = DAODataMng.getWhere(request);

                String where = "(obj.closed !='1' or obj.closed is null)";
                if (StringUtils.isNotEmpty(authwhere)) {
                    where += " and (" + authwhere + ")";
                }

                String[] studyIds = daoDataMng.getIdArray("xsht", where, null, 1);
                if (!ArrayUtils.isEmpty(studyIds)) {
                    getScheduleWhere = "obj.type='task' and concat(',',obj.owner,',') like '%,"+accountId+",%' and obj.study_id in (" + String.join(",", studyIds) + ")";
                    getUserWhere = "obj.studyid in (" + String.join(",", studyIds) + ")";
                    getStudyWhere = "obj.id in (" + String.join(",", studyIds) + ")";
                }
            }

            Gson gson = new Gson();
            HashMap<String, Object> TaskMap = new HashMap<>();

            List<Map> scheduleListAll = daoDataMng.listRecord("schedule", getScheduleWhere, "study_id,sortorder", 10000);

            List<Map> scheduleList = filterTasksWithoutSubtasks(scheduleListAll);

            List<Map> scheduleToJsonList = new ArrayList<>();

            SimpleDateFormat SDFymdhms = new SimpleDateFormat("yyyy-MM-dd HH:mm");

            for (Map scheduleMap : scheduleList) {

                Map scheduleToJsonMap = new HashMap<>();

                scheduleToJsonMap.put("studyCode", scheduleMap.get("study_id"));
                scheduleToJsonMap.put("id", scheduleMap.get("id"));
                scheduleToJsonMap.put("text", scheduleMap.get("name"));
                scheduleToJsonMap.put("owner", scheduleMap.get("owner"));
                scheduleToJsonMap.put("type", scheduleMap.get("type"));
                scheduleToJsonMap.put("progress", scheduleMap.get("progress"));
                scheduleToJsonMap.put("start_date",
//                        DateUtil.formatDate((Date) scheduleMap.get("planned_start_date"), "yyyy-MM-dd HH:mm")
                        scheduleMap.get("planned_start_date") != null ? SDFymdhms.format(scheduleMap.get("planned_start_date")) : null

                );
                scheduleToJsonMap.put("end_date",
//                        DateUtil.formatDate((Date) scheduleMap.get("planned_end_date"), "yyyy-MM-dd HH:mm")
                        scheduleMap.get("planned_end_date") != null ? SDFymdhms.format(scheduleMap.get("planned_end_date")) : null
                );
                scheduleToJsonMap.put("actual_start_date",

                        scheduleMap.get("actual_start_date") != null ? SDFymdhms.format(scheduleMap.get("actual_start_date")) : null
//                        DateUtil.formatDate((Date) scheduleMap.get("actual_start_date"), "yyyy-MM-dd HH:mm")

                );
                scheduleToJsonMap.put("actual_end_date",
//                        DateUtil.formatDate((Date) scheduleMap.get("actual_end_date"), "yyyy-MM-dd HH:mm")
                        scheduleMap.get("actual_end_date") != null ? SDFymdhms.format(scheduleMap.get("actual_end_date")) : null
                );

                scheduleToJsonList.add(scheduleToJsonMap);

            }
            TaskMap.put("data", scheduleToJsonList);


            List studyUserObjectList = daoDataMng.list("select obj.studyid as studyid,obj2.xm as label,obj2.id as key from Roles obj,Ryjbzl obj2 where " + getUserWhere + " and obj.member=obj2.id", 10000, 1);


            ArrayList<Map<String, String>> studyUserList = new ArrayList<>();
            for (Object studyUserObject : studyUserObjectList) {
                Object[] studyUserObj = (Object[]) studyUserObject;
                Map studyUserMap = new HashMap<>();
                studyUserMap.put("studyid", studyUserObj[0].toString());
                studyUserMap.put("key", studyUserObj[2].toString());
                studyUserMap.put("label", studyUserObj[1].toString());
                studyUserList.add(studyUserMap);
            }
            Map<Object, Object> allUserMap = new HashMap<>(); // 创建新的 Map 对象，用于存储排重后的结果

            for (Map<String, String> studyUserMap : studyUserList) {
                if (!allUserMap.containsKey(studyUserMap.get("key"))) {
                    allUserMap.put(studyUserMap.get("key"), studyUserMap.get("label"));
                }

            }


            Map<String, List<Map>> StudyUserOptions = studyUserList.stream().collect(Collectors.groupingBy(m -> (m.get("studyid")) + "_UserOptions"));

//            StudyUserOptions.forEach((key, StudyUserList) -> {StudyUserList.forEach(StudyUserMap -> StudyUserMap.remove("studyid"));});

            List stuydToJsonList = new ArrayList();

            List<Map> stuydList = daoDataMng.listRecord("xsht", getStudyWhere + " and (obj.closed !='1' or obj.closed is null)", null, 10000);


            for (Map studyMap : stuydList) {
                HashMap<Object, Object> studyJsonMap = new HashMap<>();
                studyJsonMap.put("key", (Long) studyMap.get("id"));
                studyJsonMap.put("label", (String) studyMap.get("studyid"));
                stuydToJsonList.add(studyJsonMap);
            }
            Map collectionsToJsonMap = new HashMap<>();
            collectionsToJsonMap.put("StudyOptions", stuydToJsonList);
            collectionsToJsonMap.putAll(StudyUserOptions);


            Set<String> allUseruniqueKeys = new HashSet<String>();
            List<Map> allUserList = new ArrayList<Map>();

            for (Map studyUserMap : studyUserList) {
                String userId = (String) studyUserMap.get("key");
                if (!allUseruniqueKeys.contains(userId)) {
                    allUseruniqueKeys.add(userId);
                    allUserList.add(studyUserMap);
                }
            }
            collectionsToJsonMap.put("allUserOptions", allUserList);

            TaskMap.put("collections", collectionsToJsonMap);

            String TaskJson = gson.toJson(TaskMap);


            response.getOutputStream().write(TaskJson.getBytes("UTF-8"));
            response.getOutputStream().close();
            return;
        } catch (Exception e) {
            Log.error("", e);
        }


    }


    public void callback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            String nativeeditorStatus = request.getParameter("!nativeeditor_status");

            Map<String, String[]> parameterMap = request.getParameterMap();
            HashMap<String, Object> MsgMap = new HashMap<>();
            Long taskId = Long.valueOf(request.getParameter("id"));
            String tableId = "schedule";

            SimpleDateFormat SDFymdhms = new SimpleDateFormat("yyyy-MM-dd HH:mm");

            if (StringUtils.equals(nativeeditorStatus, "deleted")) {
//                daoDataMng.delRecord(tableId,taskId);
                daoDataMng.delRecord(tableId, "obj.id=" + taskId);

            } else {

                HashMap<Object, Object> taskToSaveMap = new HashMap<>();
                Map scheduleMap = daoDataMng.getRecord(tableId, Long.valueOf(request.getParameter("id")));
                Long studyCode = Long.valueOf(request.getParameter("studyCode"));

                String progress = request.getParameter("progress");
                if (StringUtils.isEmpty(progress)) {
                    taskToSaveMap.put("progress", 0d);
                } else {
                    taskToSaveMap.put("progress", Double.parseDouble(progress));
                }


                if (!StringUtils.equals(nativeeditorStatus, "inserted")) {
                    taskToSaveMap.put("id", Long.valueOf(request.getParameter("id")));



                    if(Double.parseDouble(request.getParameter("progress")) == 0){

                        taskToSaveMap.put("planned_start_date",

                                request.getParameter("start_date")!=null?SDFymdhms.parse(request.getParameter("start_date")):null
                        );


                        taskToSaveMap.put("planned_end_date",

                                request.getParameter("end_date")!=null?SDFymdhms.parse(request.getParameter("end_date")):null

                        );
                    }
//
//                    if (Double.parseDouble(request.getParameter("progress")) != 0 && (double)scheduleMap.get("progress")==0) {
//                        taskToSaveMap.put("start_date", new Date());
//                    }
//
//
//                    if (Double.parseDouble(request.getParameter("progress")) == 1 && (double)scheduleMap.get("progress")<=1) {
//                        taskToSaveMap.put("end_date", new Date());
//                    }



                }else {

                    Map studyMap = daoDataMng.getRecordUI("xsht", studyCode);
                    List parentScheduleList = daoDataMng.listRecord(tableId, "obj.study_id='" + studyCode + "' and obj.name='" + studyMap.get("zt") + "'", null, 1);
                    Map parentScheduleMap = (Map) parentScheduleList.get(0);
                    Long parentScheduleId = (Long) parentScheduleMap.get("id");
                    taskToSaveMap.put("parent_schedule_id",parentScheduleId);
                    List maxSortorderList = daoDataMng.listRecord("schedule", "obj.study_id=" + studyCode, "sortorder desc", 1);
                    if (CollectionUtils.isNotEmpty(maxSortorderList)) {
                        Map maxSortorder = (Map) maxSortorderList.get(0);

                        taskToSaveMap.put("sortorder",(Long) maxSortorder.get("sortorder"));

                    }

                    taskToSaveMap.put("planned_start_date",

                            request.getParameter("start_date")!=null?SDFymdhms.parse(request.getParameter("start_date")):null

                    );
                    taskToSaveMap.put("planned_end_date",

                            request.getParameter("end_date")!=null?SDFymdhms.parse(request.getParameter("end_date")):null

                    );



                    taskToSaveMap.put("actual_start_date",

                            request.getParameter("actual_start_date")!=null?SDFymdhms.parse(request.getParameter("actual_start_date")):null

                    );
                    taskToSaveMap.put("actual_end_date",

                            request.getParameter("actual_end_date")!=null?SDFymdhms.parse(request.getParameter("actual_end_date")):null

                    );


                }





//                    if (StringUtils.equals(nativeeditorStatus,"inserted")) {
//                        Long sortorder =0l;
//
////                        Map studyMap = daoDataMng.getRecordUI("xsht", Long.valueOf(request.getParameter("studyCode")));
////
////                        String stuydStatus = (String) studyMap.get("zt");
//
//
//                        List maxSortorderList = daoDataMng.listRecord("schedule", "obj.study_id=" + Long.valueOf(request.getParameter("studyCode")), "sortorder desc", 1);
//                            if (CollectionUtils.isNotEmpty(maxSortorderList)) {
//                                Map maxSortorder = (Map) maxSortorderList.get(0);
//                                sortorder = (Long) maxSortorder.get("sortorder");
//                            }
//                        taskToSaveMap.put("sortorder", sortorder);
//
//                    }

//                    taskToSaveMap.put("study_id",Long.valueOf(request.getParameter("studyCode")));
                taskToSaveMap.put("study_id",studyCode);
                taskToSaveMap.put("type","task");
                taskToSaveMap.put("name", request.getParameter("text"));
                taskToSaveMap.put("owner", request.getParameter("owner"));
                taskToSaveMap.put("planned_start_date", DateUtil.parseDate(request.getParameter("start_date"), "yyyy-MM-dd HH:mm"));
                taskToSaveMap.put("planned_end_date", DateUtil.parseDate(request.getParameter("end_date"), "yyyy-MM-dd HH:mm"));


                daoDataMng.saveRecord(tableId, taskToSaveMap);
                if (StringUtils.equals(nativeeditorStatus, "inserted")) {
                    MsgMap.put("tid", (Long) taskToSaveMap.get("id"));
                }


            }


            MsgMap.put("status", "200");

            Gson gson = new Gson();
            String MsgJson = gson.toJson(MsgMap);
            response.getOutputStream().write(MsgJson.getBytes("UTF-8"));
            response.getOutputStream().close();
            return;

        } catch (Exception e) {
            Log.error("", e);
        }


    }


}
