package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTTableFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;


public class DTTFSignCancel extends DTTableFuncAction {


    @Override
    public boolean canUse(int auth,String tableid ,String refinfo,boolean readonly) {
//		if (StringUtils.equals(tableid,"esign_instance")) {
//			return true;
//		}

        return false;

    }

    public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
        try {


            String projectId = SessUtil.getSessInfo().getProjectid();
            Long userid = Long.valueOf(SessUtil.getSessInfo().getUserid());
            String selids = fpb.getSelids();
            String tableid = fpb.getTableid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            int sum = 0;
            ArrayList<Map> StudyList = new ArrayList<>();
            if (StringUtils.isEmpty(selids)) {
                this.gotoMsgWin(request, response, "请至少选中一个签字记录", 1000, "window.parent.reloadFromInnerWindow();");
                return;
            }

            this.forwardByUri(request,response,"/lightpdfSign.ajaxCaneclPage.do?id="+selids);

//            sum = StudyList.size();
//
//            daoDataMng.saveBatch(tableid, StudyList, userid, null);
//
//            this.gotoMsgWin(request, response, "已成功关闭" + sum + "个项目"+errMsg, 5000, "window.parent.reloadFromInnerWindow();");


        } catch (Exception e) {
            Log.error("", e);
        }
    }

    public FuncInfoBean getFIB(String tableid) {
        FuncInfoBean fib = new FuncInfoBean();
        try {
            fib.setName("废除");
            fib.setType(FuncInfoBean.FUNCTYPE_AJAXMENU);
            fib.setSimpleViewShow(true);
        } catch (Exception e) {
            Log.error("", e);
        }
        return fib;
    }


}
