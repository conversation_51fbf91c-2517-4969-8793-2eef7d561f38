/*! Select2 4.1.0-rc.0 | https://github.com/select2/select2/blob/master/LICENSE.md */

!function(){if(jQuery&&jQuery.fn&&jQuery.fn.select2&&jQuery.fn.select2.amd)var e=jQuery.fn.select2.amd;e.define("select2/i18n/lb",[],function(){return{errorLoading:function(){return"D'Resultater konnten net geluede ginn."},inputTooLong:function(e){return"Läscht "+(e.input.length-e.maximum)+" Schrëftzeechen"},inputTooShort:function(e){return"Tippt mindestens "+(e.minimum-e.input.length)+" Schrëftzeechen an"},loadingMore:function(){return"Méi Resultater lueden…"},maximumSelected:function(e){return"Dir kennt nëmmen "+e.maximum+" Element"+(e.maximum>1?"er":"")+" auswielen"},noResults:function(){return"Keng Resultater fonnt"},searching:function(){return"Sichen…"},removeAllItems:function(){return"All Elementer läschen"},removeItem:function(){return"Element läschen"},search:function(){return"Sichen"}}}),e.define,e.require}();