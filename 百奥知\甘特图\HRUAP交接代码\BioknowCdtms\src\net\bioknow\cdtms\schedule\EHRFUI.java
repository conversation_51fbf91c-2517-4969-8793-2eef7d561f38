package net.bioknow.cdtms.schedule;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.bioknow.mvc.tools.Language;
import net.bioknow.passport.datamng.PassportCacheUtil;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.projectauth.DAOProjectAuth;
import net.bioknow.uapplug.reportform.extension.RFButton;
import net.bioknow.uapplug.reportform.extension.RFUIFace;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import net.bioknow.webutil.tools.UUIDUtil;
import org.apache.commons.lang.StringUtils;

//给访视左侧菜单上部增加菜单编辑按钮
public class EHRFUI implements RFUIFace {
    public List<RFButton> getLeftButtons(String projectId, String tableid, String recordid) {

        try {
            ArrayList<RFButton> listRet = new ArrayList<>();


            if (StringUtils.equals(tableid,"xsht")) {
                RFButton btn = new RFButton();
                String link1 = "scheduleGantt.show.do?studyId=" + recordid;
                btn.setName("甘特");
                btn.setLink(link1);
                listRet.add(btn);

                RFButton btn2 = new RFButton();
                String link2 = "scheduler.show.do?studyId=" + recordid;
                btn2.setName("日程");
                btn2.setLink(link2);
                listRet.add(btn2);

                RFButton btn3 = new RFButton();
                String link3 = "kanban.show.do?studyId=" + recordid;
                btn3.setName("看板");
                btn3.setLink(link3);
                listRet.add(btn3);

//                RFButton btn6 = new RFButton();
//                String link6 = "issue.show.do?studyId=" + recordid;
//                btn6.setName("问题");
//                btn6.setLink(link6);
//                listRet.add(btn6);
//
//                RFButton btn4 = new RFButton();
//                String link4 = "fileBox.show.do?studyId=" + recordid;
//                btn4.setName("文件");
//                btn4.setLink(link4);
//                listRet.add(btn4);
//
//
//                RFButton btn5 = new RFButton();
//                String link5 = "wiki.show.do?studyId=" + recordid;
//                btn5.setName("Wiki");
//                btn5.setLink(link5);
//                listRet.add(btn5);

            }

            return listRet;


        } catch (Exception e) {

            Log.error("", e);
        }
        return null;

    }

}
