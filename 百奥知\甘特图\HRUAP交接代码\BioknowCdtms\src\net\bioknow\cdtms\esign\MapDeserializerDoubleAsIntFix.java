package net.bioknow.cdtms.esign;

import com.google.gson.*;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class MapDeserializerDoubleAsIntFix implements JsonDeserializer<Map<String, Object>> {


    @Override
    public Map<String, Object> deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
        return (Map<String, Object>) read(jsonElement);
    }

    public Object read(JsonElement in) {
        if(in.isJsonArray()){
            List<Object> list = new ArrayList<>();
            JsonArray arr = in.getAsJsonArray();
            for (JsonElement anArr : arr) {
                list.add(read(anArr));
            }
            return list;
        }else if(in.isJsonObject()){
            Map<String, Object> map = new LinkedTreeMap<String, Object>();
            JsonObject obj = in.getAsJsonObject();
            Set<Map.Entry<String, JsonElement>> entitySet = obj.entrySet();
            for(Map.Entry<String, JsonElement> entry: entitySet){
                map.put(entry.getKey(), read(entry.getValue()));
            }
            return map;
        }else if( in.isJsonPrimitive()){
            JsonPrimitive prim = in.getAsJsonPrimitive();
            if(prim.isBoolean()){
                return prim.getAsBoolean();
            }else if(prim.isString()){
                return prim.getAsString();
            }else if(prim.isNumber()){
                Number num = prim.getAsNumber();
                // here you can handle double int/long values
                // and return any type you want
                // this solution will transform 3.0 float to long values
                if(Math.ceil(num.doubleValue()) == num.intValue())
                    return num.intValue();
                else{
                    return num.doubleValue();
                }
            }
        }
        return null;
    }

    public static void main(String[] args) {
        String json = "{\"schedule\":[{\"planned_end_date\":\"2023-11-10 17:00:00\",\"type\":\"project\",\"planned_duration\":74400,\"duration\":74400,\"event_type\":\"2\",\"sortorder\":1,\"originalParentScheduleid\":0,\"name\":\"CDTMS手册优化测试项目\",\"planned_start_date\":\"2023-04-10 08:00:00\",\"originalId\":2503376896,\"desc\":\"1231233123123123123\"},{\"planned_end_date\":\"2023-04-09 17:00:00\",\"type\":\"project\",\"planned_duration\":960,\"duration\":960,\"event_type\":\"2\",\"sortorder\":2,\"originalParentScheduleid\":2503376896,\"name\":\"立项阶段\",\"planned_start_date\":\"2023-04-08 08:00:00\",\"originalId\":2503376897,\"desc\":\"\"},{\"planned_end_date\":\"2023-08-08 16:00:00\",\"type\":\"project\",\"planned_duration\":41700,\"duration\":41700,\"event_type\":\"2\",\"sortorder\":3,\"originalParentScheduleid\":2503376896,\"name\":\"设计阶段\",\"planned_start_date\":\"2023-04-10 08:00:00\",\"originalId\":2503376898,\"desc\":\"\"},{\"planned_end_date\":\"2023-09-13 17:00:00\",\"type\":\"project\",\"planned_duration\":36000,\"duration\":36002,\"event_type\":\"2\",\"sortorder\":4,\"originalParentScheduleid\":2503376896,\"name\":\"进行阶段\",\"planned_start_date\":\"2023-06-03 08:00:00\",\"originalId\":2503376899,\"desc\":\"\"},{\"planned_end_date\":\"2023-11-11 13:00:00\",\"type\":\"project\",\"planned_duration\":1374,\"duration\":19440,\"event_type\":\"2\",\"sortorder\":5,\"originalParentScheduleid\":2503376896,\"name\":\"结束阶段\",\"planned_start_date\":\"2023-09-15 07:00:00\",\"originalId\":2503376900,\"desc\":\"\"},{\"planned_end_date\":\"2023-04-16 17:00:00\",\"type\":\"task\",\"planned_duration\":2400,\"duration\":2400,\"event_type\":\"2\",\"sortorder\":5,\"originalParentScheduleid\":2503376898,\"owner\":\"TDM\",\"name\":\"EDC环境创建\",\"planned_start_date\":\"2023-04-10 08:00:00\",\"originalId\":2503376901,\"desc\":\"2222\"},{\"planned_end_date\":\"2023-04-26 17:00:00\",\"type\":\"task\",\"planned_duration\":3360,\"duration\":3360,\"event_type\":\"2\",\"sortorder\":6,\"originalParentScheduleid\":2503376898,\"owner\":\"TDM\",\"name\":\"eCRF设计与搭建\",\"planned_start_date\":\"2023-04-17 08:00:00\",\"originalId\":2503376902,\"desc\":\"\"},{\"planned_end_date\":\"2023-04-29 08:01:00\",\"type\":\"task\",\"planned_duration\":1,\"duration\":63,\"event_type\":\"3\",\"sortorder\":7,\"originalParentScheduleid\":2503376898,\"owner\":\"TDM\",\"name\":\"数据审核计划(DMRP)\",\"planned_start_date\":\"2023-04-29 00:00:00\",\"originalId\":2503376903,\"desc\":\"\"},{\"planned_end_date\":\"2023-05-07 17:00:00\",\"type\":\"task\",\"planned_duration\":3360,\"duration\":3360,\"event_type\":\"2\",\"sortorder\":9,\"originalParentScheduleid\":2503376898,\"owner\":\"TDM\",\"name\":\"数据核查说明(DVS)\\u0026EC编程与测试\",\"planned_start_date\":\"2023-04-29 08:00:00\",\"originalId\":2503376904,\"desc\":\"\"},{\"planned_end_date\":\"2023-05-17 17:00:00\",\"type\":\"task\",\"planned_duration\":3840,\"duration\":3840,\"event_type\":\"2\",\"sortorder\":10,\"originalParentScheduleid\":2503376898,\"owner\":\"TDM\",\"name\":\"Team UAT\",\"planned_start_date\":\"2023-05-08 08:00:00\",\"originalId\":2503376905,\"desc\":\"\"},{\"planned_end_date\":\"2023-05-22 17:00:00\",\"type\":\"task\",\"planned_duration\":1440,\"duration\":1440,\"event_type\":\"2\",\"sortorder\":11,\"originalParentScheduleid\":2503376898,\"owner\":\"TDM\",\"name\":\"数据库QC\",\"planned_start_date\":\"2023-05-20 00:00:00\",\"originalId\":2503376906,\"desc\":\"\"},{\"planned_end_date\":\"2023-05-27 17:00:00\",\"type\":\"task\",\"planned_duration\":1440,\"duration\":1440,\"event_type\":\"2\",\"sortorder\":12,\"originalParentScheduleid\":2503376898,\"owner\":\"TDM\",\"name\":\"eCRF填写指南\",\"planned_start_date\":\"2023-05-22 17:00:00\",\"originalId\":2503376907,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-16 10:39:00\",\"type\":\"project\",\"planned_duration\":639,\"duration\":9061,\"event_type\":\"2\",\"sortorder\":15,\"originalParentScheduleid\":2503376898,\"name\":\"方案修订\",\"planned_start_date\":\"2023-07-13 00:00:00\",\"originalId\":2503376908,\"desc\":\"\"},{\"planned_end_date\":\"2023-05-31 17:00:00\",\"type\":\"task\",\"planned_duration\":1920,\"duration\":1920,\"event_type\":\"2\",\"sortorder\":16,\"originalParentScheduleid\":2503376898,\"owner\":\"TDM\",\"name\":\"eCRF上线\",\"planned_start_date\":\"2023-05-27 17:00:00\",\"originalId\":2503376909,\"desc\":\"\"},{\"planned_end_date\":\"2023-06-03 08:00:00\",\"type\":\"milestone\",\"planned_duration\":0,\"duration\":0,\"event_type\":\"2\",\"sortorder\":17,\"originalParentScheduleid\":2503376898,\"name\":\"上线完成\",\"planned_start_date\":\"2023-06-03 08:00:00\",\"originalId\":2503376910,\"desc\":\"123123\"},{\"planned_end_date\":\"2023-06-03 09:12:00\",\"type\":\"task\",\"planned_duration\":72,\"duration\":72,\"event_type\":\"2\",\"sortorder\":18,\"originalParentScheduleid\":2503376898,\"name\":\"数据管理计划\",\"planned_start_date\":\"2023-06-03 08:00:00\",\"originalId\":2503376911,\"desc\":\"\"},{\"planned_end_date\":\"2023-06-07 10:00:00\",\"type\":\"task\",\"planned_duration\":154,\"duration\":154,\"event_type\":\"2\",\"sortorder\":19,\"originalParentScheduleid\":2503376898,\"owner\":\"TDM\",\"name\":\"账户管理\",\"planned_start_date\":\"2023-06-01 00:00:00\",\"originalId\":2503376912,\"desc\":\"3123123\"},{\"planned_end_date\":\"2023-06-03 12:00:00\",\"type\":\"task\",\"planned_duration\":4,\"duration\":4,\"event_type\":\"2\",\"sortorder\":20,\"originalParentScheduleid\":2503376898,\"owner\":\"TDM\",\"name\":\"实验室参考值\",\"planned_start_date\":\"2023-06-03 00:00:00\",\"originalId\":2503376913,\"desc\":\"\"},{\"planned_end_date\":\"2023-05-02 15:00:00\",\"type\":\"task\",\"planned_duration\":23,\"duration\":1380,\"event_type\":\"2\",\"sortorder\":21,\"originalParentScheduleid\":2503376898,\"name\":\"医学编码计划\",\"planned_start_date\":\"2023-04-29 16:00:00\",\"originalId\":2503376914,\"desc\":\"\"},{\"planned_end_date\":\"2023-05-02 16:00:00\",\"type\":\"task\",\"planned_duration\":1920,\"duration\":1920,\"event_type\":\"2\",\"sortorder\":22,\"originalParentScheduleid\":2503376898,\"owner\":\"DM\",\"name\":\"外部数据管理计划\",\"planned_start_date\":\"2023-04-26 16:00:00\",\"originalId\":2503376915,\"desc\":\"\"},{\"planned_end_date\":\"2023-05-01 08:24:00\",\"type\":\"task\",\"planned_duration\":24,\"duration\":24,\"event_type\":\"2\",\"sortorder\":23,\"originalParentScheduleid\":2503376898,\"owner\":\"MCO\",\"name\":\"数据传输协议\",\"planned_start_date\":\"2023-04-29 16:00:00\",\"originalId\":2503376916,\"desc\":\"\"},{\"planned_end_date\":\"2023-06-03 08:00:00\",\"type\":\"milestone\",\"planned_duration\":0,\"duration\":0,\"event_type\":\"2\",\"sortorder\":24,\"originalParentScheduleid\":2503376899,\"name\":\"FSFV\",\"planned_start_date\":\"2023-06-03 08:00:00\",\"originalId\":2503376917,\"desc\":\"\"},{\"planned_end_date\":\"2023-09-13 17:00:00\",\"type\":\"task\",\"planned_duration\":36000,\"duration\":36000,\"event_type\":\"2\",\"sortorder\":25,\"originalParentScheduleid\":2503376899,\"owner\":\"TDM\",\"name\":\"数据清理\",\"planned_start_date\":\"2023-06-03 08:00:00\",\"originalId\":2503376918,\"desc\":\"\"},{\"planned_end_date\":\"2023-09-13 17:00:00\",\"type\":\"task\",\"planned_duration\":36000,\"duration\":36000,\"event_type\":\"2\",\"sortorder\":26,\"originalParentScheduleid\":2503376899,\"owner\":\"TDM\",\"name\":\"进展报告\",\"planned_start_date\":\"2023-06-03 08:00:00\",\"originalId\":2503376919,\"desc\":\"\"},{\"planned_end_date\":\"2023-09-13 17:00:00\",\"type\":\"task\",\"planned_duration\":600,\"duration\":36000,\"event_type\":\"2\",\"sortorder\":27,\"originalParentScheduleid\":2503376899,\"name\":\"医学审核\",\"planned_start_date\":\"2023-06-03 08:00:00\",\"originalId\":2503376920,\"desc\":\"\"},{\"planned_end_date\":\"2023-06-04 10:00:00\",\"type\":\"task\",\"planned_duration\":600,\"duration\":36000,\"event_type\":\"2\",\"sortorder\":28,\"originalParentScheduleid\":2503376899,\"owner\":\"TDM\",\"name\":\"SAE一致性检查\",\"planned_start_date\":\"2023-06-03 08:00:00\",\"originalId\":2503376921,\"desc\":\"\"},{\"planned_end_date\":\"2023-06-04 10:00:00\",\"type\":\"task\",\"planned_duration\":600,\"duration\":36000,\"event_type\":\"2\",\"sortorder\":29,\"originalParentScheduleid\":2503376899,\"owner\":\"TDM\",\"name\":\"随机化一致性检查\",\"planned_start_date\":\"2023-06-03 08:00:00\",\"originalId\":2503376922,\"desc\":\"\"},{\"planned_end_date\":\"2023-06-04 10:00:00\",\"type\":\"task\",\"planned_duration\":600,\"duration\":36000,\"event_type\":\"2\",\"sortorder\":30,\"originalParentScheduleid\":2503376899,\"owner\":\"TDM\",\"name\":\"医学编码\",\"planned_start_date\":\"2023-06-03 08:00:00\",\"originalId\":2503376923,\"desc\":\"\"},{\"planned_end_date\":\"2023-06-04 10:00:00\",\"type\":\"task\",\"planned_duration\":600,\"duration\":36000,\"event_type\":\"2\",\"sortorder\":31,\"originalParentScheduleid\":2503376899,\"owner\":\"TDM\",\"name\":\"外部数据管理\",\"planned_start_date\":\"2023-06-03 08:00:00\",\"originalId\":2503376924,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-26 11:00:00\",\"type\":\"task\",\"planned_duration\":81,\"duration\":81,\"event_type\":\"2\",\"sortorder\":32,\"originalParentScheduleid\":2503376956,\"name\":\"eCRF冻结清单\",\"planned_start_date\":\"2023-07-15 11:00:00\",\"originalId\":2503376925,\"desc\":\"\"},{\"planned_end_date\":\"2023-08-02 11:00:00\",\"type\":\"task\",\"planned_duration\":40,\"duration\":40,\"event_type\":\"2\",\"sortorder\":33,\"originalParentScheduleid\":2503376956,\"name\":\"数据快照质控\",\"planned_start_date\":\"2023-07-26 11:00:00\",\"originalId\":2503376926,\"desc\":\"\"},{\"planned_end_date\":\"2023-08-07 11:00:00\",\"type\":\"task\",\"planned_duration\":24,\"duration\":24,\"event_type\":\"2\",\"sortorder\":34,\"originalParentScheduleid\":2503376956,\"name\":\"数据快照\",\"planned_start_date\":\"2023-08-02 11:00:00\",\"originalId\":2503376927,\"desc\":\"\"},{\"planned_end_date\":\"2023-08-13 14:00:00\",\"type\":\"task\",\"planned_duration\":34,\"duration\":34,\"event_type\":\"2\",\"sortorder\":35,\"originalParentScheduleid\":2503376956,\"name\":\"数据导出与传输\",\"planned_start_date\":\"2023-08-07 11:00:00\",\"originalId\":2503376928,\"desc\":\"\"},{\"planned_end_date\":\"2023-08-20 11:00:00\",\"type\":\"task\",\"planned_duration\":38,\"duration\":38,\"event_type\":\"2\",\"sortorder\":36,\"originalParentScheduleid\":2503376956,\"name\":\"数据解冻\",\"planned_start_date\":\"2023-08-13 14:00:00\",\"originalId\":2503376929,\"desc\":\"\"},{\"planned_end_date\":\"2023-08-28 05:00:00\",\"type\":\"task\",\"planned_duration\":350,\"duration\":350,\"event_type\":\"2\",\"sortorder\":38,\"originalParentScheduleid\":2503376899,\"name\":\"(盲态)数据审核会议\",\"planned_start_date\":\"2023-08-13 15:00:00\",\"originalId\":2503376930,\"desc\":\"\"},{\"planned_end_date\":\"2023-09-04 00:00:00\",\"type\":\"milestone\",\"planned_duration\":0,\"duration\":0,\"event_type\":\"2\",\"sortorder\":38,\"originalParentScheduleid\":2503376899,\"name\":\"LSLV\",\"planned_start_date\":\"2023-09-04 00:00:00\",\"originalId\":2503376931,\"desc\":\"\"},{\"planned_end_date\":\"2023-09-25 17:00:00\",\"type\":\"task\",\"planned_duration\":64,\"duration\":3840,\"event_type\":\"2\",\"sortorder\":40,\"originalParentScheduleid\":2503376900,\"name\":\"数据库锁定清单\",\"planned_start_date\":\"2023-09-16 08:00:00\",\"originalId\":2503376932,\"desc\":\"\"},{\"planned_end_date\":\"2023-10-10 13:00:00\",\"type\":\"task\",\"planned_duration\":341,\"duration\":5040,\"event_type\":\"2\",\"sortorder\":41,\"originalParentScheduleid\":2503376900,\"name\":\"数据库锁定质控\",\"planned_start_date\":\"2023-09-26 08:00:00\",\"originalId\":2503376933,\"desc\":\"\"},{\"planned_end_date\":\"2023-10-24 13:00:00\",\"type\":\"task\",\"planned_duration\":336,\"duration\":4800,\"event_type\":\"2\",\"sortorder\":42,\"originalParentScheduleid\":2503376900,\"name\":\"数据库锁定\",\"planned_start_date\":\"2023-10-10 13:00:00\",\"originalId\":2503376934,\"desc\":\"\"},{\"planned_end_date\":\"2023-10-19 00:00:00\",\"type\":\"milestone\",\"planned_duration\":0,\"duration\":0,\"event_type\":\"2\",\"sortorder\":43,\"originalParentScheduleid\":2503376900,\"name\":\"数据库锁定完成\",\"planned_start_date\":\"2023-10-19 00:00:00\",\"originalId\":2503376935,\"desc\":\"\"},{\"planned_end_date\":\"2023-11-07 11:00:00\",\"type\":\"task\",\"planned_duration\":334,\"duration\":4740,\"event_type\":\"2\",\"sortorder\":44,\"originalParentScheduleid\":2503376900,\"name\":\"数据导出与传输\",\"planned_start_date\":\"2023-10-24 13:00:00\",\"originalId\":2503376936,\"desc\":\"\"},{\"planned_end_date\":\"2023-10-13 23:00:00\",\"type\":\"task\",\"planned_duration\":329,\"duration\":329,\"event_type\":\"2\",\"sortorder\":46,\"originalParentScheduleid\":2503376900,\"name\":\"(盲态)数据审核会议\",\"planned_start_date\":\"2023-09-30 06:00:00\",\"originalId\":2503376937,\"desc\":\"\"},{\"planned_end_date\":\"2023-11-01 17:00:00\",\"type\":\"task\",\"planned_duration\":52,\"duration\":3120,\"event_type\":\"2\",\"sortorder\":47,\"originalParentScheduleid\":2503376900,\"name\":\"EDC用户权限历史\",\"planned_start_date\":\"2023-10-24 13:00:00\",\"originalId\":2503376938,\"desc\":\"\"},{\"planned_end_date\":\"2023-11-11 13:00:00\",\"type\":\"task\",\"planned_duration\":432,\"duration\":5760,\"event_type\":\"2\",\"sortorder\":48,\"originalParentScheduleid\":2503376900,\"name\":\"受试者eCRF交接\",\"planned_start_date\":\"2023-10-24 13:00:00\",\"originalId\":2503376939,\"desc\":\"\"},{\"planned_end_date\":\"2023-10-21 05:00:00\",\"type\":\"task\",\"planned_duration\":256,\"duration\":3120,\"event_type\":\"2\",\"sortorder\":50,\"originalParentScheduleid\":2503376900,\"name\":\"数据管理计划\",\"planned_start_date\":\"2023-10-10 13:00:00\",\"originalId\":2503376940,\"desc\":\"\"},{\"planned_end_date\":\"2023-11-07 23:00:00\",\"type\":\"task\",\"planned_duration\":346,\"duration\":5040,\"event_type\":\"2\",\"sortorder\":50,\"originalParentScheduleid\":2503376900,\"name\":\"数据管理文件归档\",\"planned_start_date\":\"2023-10-24 13:00:00\",\"originalId\":2503376941,\"desc\":\"\"},{\"planned_end_date\":\"2023-04-09 17:00:00\",\"type\":\"task\",\"planned_duration\":960,\"duration\":960,\"event_type\":\"2\",\"sortorder\":51,\"originalParentScheduleid\":2503376897,\"owner\":\"TDM\",\"name\":\"研究方案\",\"planned_start_date\":\"2023-04-08 08:00:00\",\"originalId\":2503376942,\"desc\":\"333333\"},{\"planned_end_date\":\"2023-07-16 01:00:00\",\"type\":\"task\",\"planned_duration\":73,\"duration\":73,\"event_type\":\"2\",\"sortorder\":52,\"originalParentScheduleid\":2503376957,\"name\":\"eCRF设计与搭建\",\"planned_start_date\":\"2023-07-13 00:00:00\",\"originalId\":2503376943,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-16 01:00:00\",\"type\":\"task\",\"planned_duration\":73,\"duration\":481,\"event_type\":\"2\",\"sortorder\":52,\"originalParentScheduleid\":2503376908,\"name\":\"eCRF设计与搭建\",\"planned_start_date\":\"2023-07-13 00:00:00\",\"originalId\":2503376944,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-18 17:00:00\",\"type\":\"task\",\"planned_duration\":24,\"duration\":24,\"event_type\":\"2\",\"sortorder\":53,\"originalParentScheduleid\":2503376957,\"name\":\"数据审核计划(DMRP)\",\"planned_start_date\":\"2023-07-16 08:00:00\",\"originalId\":2503376945,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-18 17:00:00\",\"type\":\"task\",\"planned_duration\":24,\"duration\":1440,\"event_type\":\"2\",\"sortorder\":53,\"originalParentScheduleid\":2503376957,\"name\":\"数据审核计划(DMRP)\",\"planned_start_date\":\"2023-07-16 08:00:00\",\"originalId\":2503376946,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-16 08:24:00\",\"type\":\"task\",\"planned_duration\":24,\"duration\":1440,\"event_type\":\"3\",\"sortorder\":53,\"originalParentScheduleid\":2503376908,\"owner\":\"TDM\",\"name\":\"数据审核计划(DMRP)\",\"planned_start_date\":\"2023-07-16 08:00:00\",\"originalId\":2503376947,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-19 17:00:00\",\"type\":\"task\",\"planned_duration\":8,\"duration\":8,\"event_type\":\"2\",\"sortorder\":54,\"originalParentScheduleid\":2503376957,\"name\":\"数据核查说明(DVS)\\u0026EC编程与测试\",\"planned_start_date\":\"2023-07-19 08:00:00\",\"originalId\":2503376948,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-19 17:00:00\",\"type\":\"task\",\"planned_duration\":8,\"duration\":480,\"event_type\":\"2\",\"sortorder\":54,\"originalParentScheduleid\":2503376908,\"name\":\"数据核查说明(DVS)\\u0026EC编程与测试\",\"planned_start_date\":\"2023-07-19 08:00:00\",\"originalId\":2503376949,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-24 17:00:00\",\"type\":\"task\",\"planned_duration\":24,\"duration\":24,\"event_type\":\"2\",\"sortorder\":59,\"originalParentScheduleid\":2503376957,\"name\":\"Team UAT\",\"planned_start_date\":\"2023-07-22 08:00:00\",\"originalId\":2503376950,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-24 17:00:00\",\"type\":\"task\",\"planned_duration\":24,\"duration\":1440,\"event_type\":\"2\",\"sortorder\":59,\"originalParentScheduleid\":2503376908,\"name\":\"Team UAT\",\"planned_start_date\":\"2023-07-22 08:00:00\",\"originalId\":2503376951,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-26 17:00:00\",\"type\":\"task\",\"planned_duration\":16,\"duration\":16,\"event_type\":\"2\",\"sortorder\":60,\"originalParentScheduleid\":2503376957,\"name\":\"数据库QC\",\"planned_start_date\":\"2023-07-25 08:00:00\",\"originalId\":2503376952,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-26 17:00:00\",\"type\":\"task\",\"planned_duration\":16,\"duration\":960,\"event_type\":\"2\",\"sortorder\":60,\"originalParentScheduleid\":2503376908,\"name\":\"数据库QC\",\"planned_start_date\":\"2023-07-25 08:00:00\",\"originalId\":2503376953,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-30 17:00:00\",\"type\":\"task\",\"planned_duration\":16,\"duration\":16,\"event_type\":\"2\",\"sortorder\":61,\"originalParentScheduleid\":2503376957,\"name\":\"eCRF填写指南\",\"planned_start_date\":\"2023-07-29 08:00:00\",\"originalId\":2503376954,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-29 08:15:00\",\"type\":\"task\",\"planned_duration\":15,\"duration\":960,\"event_type\":\"3\",\"sortorder\":61,\"originalParentScheduleid\":2503376908,\"owner\":\"TDM\",\"name\":\"eCRF填写指南\",\"planned_start_date\":\"2023-07-29 08:00:00\",\"originalId\":2503376955,\"desc\":\"\"},{\"planned_end_date\":\"2023-08-26 22:00:00\",\"type\":\"project\",\"planned_duration\":277,\"duration\":277,\"event_type\":\"2\",\"sortorder\":62,\"originalParentScheduleid\":2503376899,\"name\":\"数据快照\",\"planned_start_date\":\"2023-07-15 11:00:00\",\"originalId\":2503376956,\"desc\":\"\"},{\"planned_end_date\":\"2023-08-08 16:00:00\",\"type\":\"project\",\"planned_duration\":640,\"duration\":9060,\"event_type\":\"2\",\"sortorder\":62,\"originalParentScheduleid\":2503376899,\"name\":\"数据库修订\",\"planned_start_date\":\"2023-07-13 00:00:00\",\"originalId\":2503376957,\"desc\":\"\"},{\"planned_end_date\":\"2023-08-08 16:00:00\",\"type\":\"task\",\"planned_duration\":55,\"duration\":55,\"event_type\":\"2\",\"sortorder\":62,\"originalParentScheduleid\":2503376957,\"name\":\"eCRF上线\",\"planned_start_date\":\"2023-07-31 08:00:00\",\"originalId\":2503376958,\"desc\":\"\"},{\"planned_end_date\":\"2023-08-08 16:00:00\",\"type\":\"task\",\"planned_duration\":55,\"duration\":3300,\"event_type\":\"2\",\"sortorder\":62,\"originalParentScheduleid\":2503376908,\"name\":\"eCRF上线\",\"planned_start_date\":\"2023-07-31 08:00:00\",\"originalId\":2503376959,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-25 01:00:00\",\"type\":\"task\",\"planned_duration\":72,\"duration\":72,\"event_type\":\"2\",\"sortorder\":102,\"originalParentScheduleid\":113345095,\"name\":\"Team UAT\",\"planned_start_date\":\"2023-07-22 01:00:00\",\"originalId\":2503376960,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-28 01:00:00\",\"type\":\"task\",\"planned_duration\":72,\"duration\":72,\"event_type\":\"2\",\"sortorder\":103,\"originalParentScheduleid\":113345095,\"name\":\"数据库QC\",\"planned_start_date\":\"2023-07-25 01:00:00\",\"originalId\":2503376961,\"desc\":\"\"},{\"planned_end_date\":\"2023-07-31 01:00:00\",\"type\":\"task\",\"planned_duration\":72,\"duration\":72,\"event_type\":\"2\",\"sortorder\":104,\"originalParentScheduleid\":113345095,\"name\":\"eCRF填写指南\",\"planned_start_date\":\"2023-07-28 01:00:00\",\"originalId\":2503376962,\"desc\":\"\"},{\"planned_end_date\":\"2023-08-08 16:00:00\",\"type\":\"task\",\"planned_duration\":207,\"duration\":207,\"event_type\":\"2\",\"sortorder\":105,\"originalParentScheduleid\":113345095,\"name\":\"eCRF上线\",\"planned_start_date\":\"2023-07-31 01:00:00\",\"originalId\":2503376963,\"desc\":\"\"},{\"planned_end_date\":\"2023-08-08 16:00:00\",\"type\":\"task\",\"planned_duration\":0,\"duration\":0,\"event_type\":\"2\",\"sortorder\":107,\"originalParentScheduleid\":113345095,\"name\":\"上线完成\",\"planned_start_date\":\"2023-08-08 16:00:00\",\"originalId\":2503376964,\"desc\":\"111\"}],\"link\":[{\"target_schedule_id\":2503376902,\"type\":\"0\",\"schedule_id\":2503376901},{\"target_schedule_id\":2503376903,\"type\":\"0\",\"schedule_id\":2503376902},{\"target_schedule_id\":2503376904,\"type\":\"0\",\"schedule_id\":2503376903},{\"target_schedule_id\":2503376905,\"type\":\"0\",\"schedule_id\":2503376904},{\"target_schedule_id\":2503376906,\"type\":\"0\",\"schedule_id\":2503376905},{\"target_schedule_id\":2503376907,\"type\":\"0\",\"schedule_id\":2503376906},{\"target_schedule_id\":2503376909,\"type\":\"0\",\"schedule_id\":2503376907},{\"target_schedule_id\":2503376915,\"type\":\"0\",\"schedule_id\":2503376902},{\"target_schedule_id\":2503376911,\"type\":\"0\",\"schedule_id\":2503376909},{\"target_schedule_id\":2503376916,\"type\":\"0\",\"schedule_id\":2503376902},{\"target_schedule_id\":2503376933,\"type\":\"0\",\"schedule_id\":2503376932},{\"target_schedule_id\":2503376934,\"type\":\"0\",\"schedule_id\":2503376933},{\"target_schedule_id\":2503376936,\"type\":\"0\",\"schedule_id\":2503376934},{\"target_schedule_id\":2503376945,\"type\":\"0\",\"schedule_id\":2503376943},{\"target_schedule_id\":2503376938,\"type\":\"0\",\"schedule_id\":2503376934},{\"target_schedule_id\":2503376939,\"type\":\"0\",\"schedule_id\":2503376934},{\"target_schedule_id\":2503376940,\"type\":\"0\",\"schedule_id\":2503376933},{\"target_schedule_id\":2503376941,\"type\":\"0\",\"schedule_id\":2503376934},{\"target_schedule_id\":2503376918,\"type\":\"0\",\"schedule_id\":2503376917},{\"target_schedule_id\":2503376919,\"type\":\"0\",\"schedule_id\":2503376917},{\"target_schedule_id\":2503376921,\"type\":\"0\",\"schedule_id\":2503376917},{\"target_schedule_id\":2503376922,\"type\":\"0\",\"schedule_id\":2503376917},{\"target_schedule_id\":2503376923,\"type\":\"0\",\"schedule_id\":2503376917},{\"target_schedule_id\":2503376924,\"type\":\"0\",\"schedule_id\":2503376917},{\"target_schedule_id\":2503376928,\"type\":\"0\",\"schedule_id\":2503376927},{\"target_schedule_id\":2503376929,\"type\":\"0\",\"schedule_id\":2503376928},{\"target_schedule_id\":2503376910,\"type\":\"0\",\"schedule_id\":2503376909},{\"target_schedule_id\":2503376948,\"type\":\"0\",\"schedule_id\":2503376945},{\"target_schedule_id\":2503376932,\"type\":\"0\",\"schedule_id\":2503376899},{\"target_schedule_id\":2503376947,\"type\":\"0\",\"schedule_id\":2503376944},{\"target_schedule_id\":2503376952,\"type\":\"0\",\"schedule_id\":2503376950},{\"target_schedule_id\":2503376926,\"type\":\"0\",\"schedule_id\":2503376925},{\"target_schedule_id\":2503376927,\"type\":\"0\",\"schedule_id\":2503376926},{\"target_schedule_id\":2503376920,\"type\":\"0\",\"schedule_id\":2503376917},{\"target_schedule_id\":2503376917,\"type\":\"0\",\"schedule_id\":2503376910},{\"target_schedule_id\":2503376914,\"type\":\"0\",\"schedule_id\":2503376902},{\"target_schedule_id\":2503376950,\"type\":\"0\",\"schedule_id\":2503376948},{\"target_schedule_id\":2503376954,\"type\":\"0\",\"schedule_id\":2503376952},{\"target_schedule_id\":2503376958,\"type\":\"0\",\"schedule_id\":2503376954},{\"target_schedule_id\":2503376953,\"type\":\"0\",\"schedule_id\":2503376951},{\"target_schedule_id\":2503376959,\"type\":\"0\",\"schedule_id\":2503376955},{\"target_schedule_id\":2503376949,\"type\":\"0\",\"schedule_id\":2503376947},{\"target_schedule_id\":2503376951,\"type\":\"0\",\"schedule_id\":2503376949},{\"target_schedule_id\":2503376955,\"type\":\"0\",\"schedule_id\":2503376953}]}";


        System.out.println("json == " + json);
        Map<String, Object> map2 = new LinkedTreeMap<>();
        map2 = new Gson().fromJson(json, map2.getClass());
        System.out.println(map2);

        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.setPrettyPrinting();
        gsonBuilder.serializeSpecialFloatingPointValues();

        gsonBuilder.registerTypeAdapter(new TypeToken<Map <String, Object>>(){}.getType(), new MapDeserializerDoubleAsIntFix());
        Gson gson = gsonBuilder.create();
        Map<String, Object> map = gson.fromJson(json, new TypeToken<Map<String, Object>>(){}.getType());
        System.out.println(map);
    }


}