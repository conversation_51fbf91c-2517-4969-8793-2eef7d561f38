package net.bioknow.cdtms.schedule;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * 缓存管理类
 *
 * <AUTHOR>
 *
 */
public class CacheMgr {

    private static Map cacheMap = new HashMap();
    private static Map cacheConfMap = new HashMap();

    private static CacheMgr cm = null;

    // 构造方法
    private CacheMgr() {
    }

    public static CacheMgr getInstance() {
        if (cm == null) {
            cm = new CacheMgr();
        }
        return cm;
    }

    /**
     * 增加缓存
     *
     * @param key
     * @param value
     *            缓存对象
     * @return
     */
    public boolean addCache(Object key, Object value) {
        boolean flag = false;
        try {
            cacheMap.put(key, value);
            flag = true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return flag;
    }

    /**
     * 获取缓存实体
     */
    public Object getValue(String key) {
        Object ob = cacheMap.get(key);
        if (ob != null) {
            return ob;
        } else {
            return null;
        }
    }

    /**
     * 获取缓存数据的数量
     *
     * @return
     */
    public int getSize() {
        return cacheMap.size();
    }

    /**
     * 删除缓存
     *
     * @param key
     * @return
     */
    public boolean removeCache(Object key) {
        boolean flag = false;
        try {
            cacheMap.remove(key);
            cacheConfMap.remove(key);
            flag = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return flag;
    }

    /**
     * 清除缓存的类 继承Thread线程类
     */



}