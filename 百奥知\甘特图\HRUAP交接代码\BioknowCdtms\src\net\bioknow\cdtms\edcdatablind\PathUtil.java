package net.bioknow.cdtms.edcdatablind;


import net.bioknow.mvc.codec.Md5Util;
import net.bioknow.mvc.tools.WebPath;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang.StringUtils;
import redis.clients.jedis.Jedis;

import java.io.File;
import java.io.FileInputStream;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR> LAW
 * @Date : Created in 2022/4/11 下午 5:49
 * @Description : 获取地址的工具类
 */
public class PathUtil {
    // 使用端地址集合
    private static final ConcurrentMap<String, String> PATH_MAP = new ConcurrentHashMap<>();
    private static final String url = null;

    // 从Redis10号库获取使用端地址时所用到的Key
    private static final String COMMON_SYSTEM = "system";
    private static final String COMMON_UAP = "common.uap.%s";
    private static final String COMMON_UAP_URLS = "common.uap.urls";


    /**
     * 获取某一项目的访问地址
     *
     * @param studycode 研究代码
     * @return 访问地址
     */
    public static String getStudyPath(String projectId, String studycode) {
        String path = null;
        Jedis jedis = null;
        try {
            String md5 = Md5Util.encodeToHexString(studycode);
            if (PATH_MAP.containsKey(md5)) {
                path = PATH_MAP.get(md5);
            } else {
                String rootPath = WebPath.getRootPath();
                File file=new File(rootPath,"WEB-INF/cdtmsredis.properties");
                if(file.exists()) {
                    Properties config = new Properties();
                    FileInputStream in = new FileInputStream(file);
                    config.load(in); // 载入文件

                    jedis = PathUtil.getJedis(config.getProperty("host"), config.getProperty("port"), config.getProperty("db"),config.getProperty("auth"));
                    //jedis = PathUtil.getJedis("************", "6379", "10","Bioknow2019");
                    String value = jedis.hget(String.format(COMMON_UAP, md5), "system");
                    if (StringUtils.isNotBlank(value)) {
                        String[] split = value.split(",");
                        String key = "";
                        for (String str : split) {
                            if (str.contains("EDCServer")) {
                                key = str.replaceAll("\\[", "").replaceAll("\"", "");
                                break;
                            }
                        }
                        path = jedis.hget(COMMON_UAP_URLS, key);
                        PATH_MAP.put(md5, path);
                    }
                    jedis.close();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("未找到StudyCode:" + studycode + " 的使用端地址!");
        }
        return path;
    }

    private static Jedis getJedis(String redis_addr,String redis_port,String redis_db,String redis_password) {
        try {
            Jedis jedis = new Jedis(redis_addr, Integer.valueOf(redis_port), 2000,2000);
            jedis.auth(redis_password);
            jedis.select(Integer.valueOf(redis_db));//指定某个数据库
            String ping = jedis.ping();
            if(!ping.equals("PONG")) {
                jedis.close();
                jedis=null;
            }
            return jedis;
        }catch(Exception e) {
            Log.error(e.getMessage(),e);
        }
        return null;
    }
}
