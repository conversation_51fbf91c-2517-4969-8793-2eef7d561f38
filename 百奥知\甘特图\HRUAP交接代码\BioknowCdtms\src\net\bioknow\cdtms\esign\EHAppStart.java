package net.bioknow.cdtms.esign;

import net.bioknow.mvc.ioc.AppStartFace;
import net.bioknow.passport.uiframe.FuncUtil;
import net.bioknow.passport.validate.PageIgnore;
import net.bioknow.uap.dbdatamng.function.FuncFactory;
import net.bioknow.uapplug.AdminMenuHandler;
import net.bioknow.webplug.configdb.ConfigDbUtil;

import javax.servlet.ServletConfig;

public class EHAppStart implements AppStartFace{

    public void onStart(ServletConfig arg) throws Exception {
        FuncFactory.addRecordFunc(new DTRFeSign());
        PageIgnore.addIgnore("/eSignIntergrate.Receive.do");

//        FuncUtil.addFunction("sysfunc", "sysfunc.param", "系统参数设定", "Login Parameters Set", "doc.gif", "authparam.list.do");

        FuncUtil.addFunction(FuncUtil.FuncFolder_prjfunc,"eSign","eSign集成",null,null,"configdb.listcfg.do?xmlpath="+ DAOeSignIntegrate.xmlpath);
//        ConfigDbUtil.registerCFGFile(DAOeSignIntegrate.xmlpath);

    }

}
