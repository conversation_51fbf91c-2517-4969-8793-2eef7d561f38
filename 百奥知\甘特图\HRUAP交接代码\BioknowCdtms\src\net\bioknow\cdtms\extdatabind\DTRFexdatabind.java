package net.bioknow.cdtms.extdatabind;

import net.bioknow.passport.validate.ValidateUtil;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbcore.schema.Schema;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.session.tokensession.UtilTokenSession;
import net.bioknow.webutil.tools.Log;
import net.bioknow.webutil.tools.URLUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class DTRFexdatabind extends DTRecordFuncAction {


	public boolean canUse(int auth, String tableid, Long recordid) {

		try {

			if (!StringUtils.equals(tableid,"outboard_data_manager")) {
				return false;
			}
			if(Long.valueOf(SessUtil.getSessInfo().getUserid())<=2) return  true;

			String projectid = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng = new DAODataMng(projectid);

			Map exdataMap = daoDataMng.getRecord(tableid, recordid);

			Long studyid = (Long) exdataMap.get("studyid");
			Map userMap = SessUtil.getSessInfo().getUser();

			Long currUserid = Long.valueOf(SessUtil.getSessInfo().getUserid());

			if(currUserid==1){
				return true;

			}


			List StudyUserList = daoDataMng.listRecord("roles", "obj.studyid=" + studyid + " and obj.active='1' and obj.member=" + (String) userMap.get("id"), null, 1);
			if (CollectionUtils.isEmpty(StudyUserList)) {

				return false;

			}



			Map StudyUserMap = (Map) StudyUserList.get(0);

			String role = (String) StudyUserMap.get("limitnum");


			if ((!StringUtils.equals(role,"EDM"))) {

				return false;

			}

			String vender = (String) exdataMap.get("outboard_data_department");
			String type = (String) exdataMap.get("outboard_data_category");
			List StudyDTAList = daoDataMng.listRecord("ext_data", "obj.studyid=" + studyid + " and obj.lab_org='"+vender+"' and obj.ext_data_type='" + type+"'", "obj.ext_version desc", 1);



			if (CollectionUtils.isEmpty(StudyDTAList)) {

				return false;

			}

			Map StudyDTAMap = (Map) StudyDTAList.get(0);


			if (StringUtils.equals((String) StudyDTAMap.get("set_binding"),"1")) {

				return true;

			}










		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}



	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
		try {




			this.forwardByUri(request,response,"/extdatabind.ajaxmenu.do?id="+fpb.getRecordid()+"&tableid="+fpb.getTableid()+"&refinfo="+fpb.getRefinfo());





		} catch (Exception e) {
			Log.error("",e);
		}
	}

	public FuncInfoBean getFIB(String tableid) {
		FuncInfoBean fib = new FuncInfoBean();


		fib.setName("遮盲");
		fib.setType(FuncInfoBean.FUNCTYPE_AJAXMENU);
		fib.setWinHeight(800);
		fib.setWinWidth(800);
		fib.setSimpleViewShow(true);


		return fib;
	}

}
