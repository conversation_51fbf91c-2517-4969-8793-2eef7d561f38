/*
@license

dhtmlxGantt v.8.0.0 Standard

This version of dhtmlxGantt is distributed under GPL 2.0 license and can be legally used in GPL projects.

To use dhtmlxGantt in non-GPL projects (and get Pro version of the product), please obtain Commercial/Enterprise or Ultimate license on our site https://dhtmlx.com/docs/products/dhtmlxGantt/#licensing or contact <NAME_EMAIL>

(c) XB Software Ltd.

*/
.buttonBg {
  background: #fff;
}
/* colors for items with inline styles assigned (task.color, link.color)*/
.gridHoverStyle {
  background-color: #5868c5;
}
.gridSelection {
  background-color: #5868c5;
}
.timelineSelection {
  background-color: #5868c5;
}
.header_text_style {
  text-transform: uppercase;
  font-weight: bold;
}
.gantt_grid_scale .gantt_grid_head_cell {
  color: #585858;
  text-transform: uppercase;
  font-weight: bold;
  border-top: none !important;
  border-right: none !important;
}
.gantt_grid_data .gantt_cell {
  border-right: none;
  color: #000;
}
.gantt_grid_data .gantt_row.gantt_selected .gantt_cell,
.gantt_grid_data .gantt_row:hover .gantt_cell {
  color: #fff;
}
/*
	Tasks
*/
.gantt_task_link .gantt_link_arrow_right {
  border-width: 6px 6px 6px 6px;
  margin-top: -3px;
}
.gantt_task_link .gantt_link_arrow_left {
  border-width: 6px 6px 6px 6px;
  margin-left: -6px;
  margin-top: -3px;
}
.gantt_task_link .gantt_link_arrow_up {
  border-width: 6px 6px 6px 6px;
}
.gantt_task_link .gantt_link_arrow_down {
  border-width: 6px 6px 6px 6px;
}
.gantt_task_line .gantt_task_progress_drag {
  bottom: -4px;
  height: 10px;
  margin-left: -8px;
  width: 16px;
}
.chartHeaderBg {
  background-color: #fff;
}
.gantt_task .gantt_task_scale .gantt_scale_cell {
  color: #585858;
  text-transform: uppercase;
  font-weight: bold;
  border-right: 1px solid #000;
}
/*
	project highlight
*/
.gantt_row.gantt_project,
.gantt_row.odd.gantt_project {
  background-color: #edfff4;
}
.gantt_task_row.gantt_project,
.gantt_task_row.odd.gantt_project {
  background-color: #f5fff9;
}
.gantt_task_line.gantt_project {
  background-color: #74FFA9;
}
.gantt_task_line.gantt_project .gantt_task_progress {
  background-color: #47BB55;
}
/*
	milestone
*/
/*
	lightbox
*/
.gantt_cal_larea input,
.gantt_cal_larea select,
.gantt_cal_larea textarea {
  color: #000;
  border: #000;
}
.gantt_cal_larea .gantt_cal_lsection {
  color: #000;
  font-size: 14px;
}
.modalBorder {
  border-color: #000;
  -webkit-box-shadow: inset 0px 0px 0px 2px #000;
  -moz-box-shadow: inset 0px 0px 0px 2px #000;
  box-shadow: inset 0px 0px 0px 2px #000;
  padding: 2px;
}
.gantt_popup_shadow {
  border-color: #000;
  -webkit-box-shadow: inset 0px 0px 0px 2px #000;
  -moz-box-shadow: inset 0px 0px 0px 2px #000;
  box-shadow: inset 0px 0px 0px 2px #000;
  padding: 2px;
}
.gantt_cal_larea {
  background: #fff;
}
.lightboxHeaderBackground {
  background: #fff;
}
.gantt_cal_light .gantt_btn_set {
  margin: 5px 10px;
}
.gantt_btn_set.gantt_cancel_btn_set {
  border-style: solid;
  border-width: 2px;
  border-color: #000;
  background: #fff;
  color: #000;
}
.gantt_btn_set.gantt_save_btn_set {
  border-style: solid;
  border-width: 2px;
  border-color: #000;
  background: #006600;
  color: #000;
  border-color: transparent;
  color: #fff;
}
.gantt_btn_set.gantt_delete_btn_set {
  border-style: solid;
  border-width: 2px;
  border-color: #000;
  background: #9f3500;
  color: #000;
  border-color: transparent;
  color: #fff;
}
.gantt_delete_btn {
  margin-top: 2px;
  width: 20px;
}
.gantt_cal_light_wide {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.gantt_cal_light_wide .gantt_cal_larea {
  border-left: none !important;
  border-right: none !important;
}
/*
	Message
*/
.gantt_popup_button.gantt_ok_button {
  font-weight: bold;
  border-style: solid;
  border-width: 2px;
  border-color: #000;
  background: #006600;
  color: #000;
  border-color: transparent;
  color: #fff;
}
.gantt_popup_button.gantt_cancel_button {
  font-weight: bold;
  color: #454544;
  border-style: solid;
  border-width: 2px;
  border-color: #000;
  background: #fff;
  color: #000;
}
.gantt_popup_title {
  color: #000;
}
/*
	Quick info
*/
.gantt_qi_big_icon {
  border-style: solid;
  border-width: 2px;
  border-color: #000;
  background: #fff;
  color: #000;
}
.gantt_qi_big_icon.icon_edit {
  border-style: solid;
  border-width: 2px;
  border-color: #000;
  background: #fff;
  color: #000;
}
.gantt_qi_big_icon.icon_delete {
  border-style: solid;
  border-width: 2px;
  border-color: #000;
  background: #9f3500;
  color: #000;
  border-color: transparent;
  color: #fff;
}
/*links dnd*/
.gantt_tooltip {
  font-size: 14px;
  color: #000;
  background: #fff;
  border: 1px solid #000;
}
.gantt_container {
  background-color: #fff;
  font-family: "arial";
  font-size: 14px;
  border: 1px solid #000;
  position: relative;
  white-space: nowrap;
  overflow-x: hidden;
  overflow-y: hidden;
}
.gantt_touch_active {
  overscroll-behavior: none;
}
.gantt_task_scroll {
  overflow-x: scroll;
}
.gantt_task,
.gantt_grid {
  position: relative;
  overflow-x: hidden;
  overflow-y: hidden;
  display: inline-block;
  vertical-align: top;
}
.gantt_grid_scale,
.gantt_task_scale {
  color: #262626;
  font-size: 14px;
  border-bottom: 1px solid #000;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.gantt_grid_scale {
  background-color: #fff;
}
.gantt_task_scale {
  background-color: #fff;
}
.gantt_task_vscroll {
  background-color: #fff;
}
.gantt_scale_line {
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  border-top: 1px solid #000;
}
.gantt_scale_line:first-child {
  border-top: none;
}
.gantt_grid_head_cell {
  display: inline-block;
  vertical-align: top;
  border-right: 1px solid #000;
  text-align: center;
  position: relative;
  cursor: default;
  height: 100%;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  line-height: 33px;
  -moz-user-select: -moz-none;
  -webkit-user-select: none;
  user-select: none;
  overflow: hidden;
}
.gantt_scale_line {
  clear: both;
}
.gantt_grid_data {
  width: 100%;
  overflow: hidden;
  position: relative;
}
.gantt_row {
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -moz-user-select: -moz-none;
}
.gantt_add,
.gantt_grid_head_add {
  width: 100%;
  height: 100%;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NkM1NEUwREQ2ODc4MTFFNkJCN0REMkNCNDBBRDc4Q0UiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NkM1NEUwREU2ODc4MTFFNkJCN0REMkNCNDBBRDc4Q0UiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo2QzU0RTBEQjY4NzgxMUU2QkI3REQyQ0I0MEFENzhDRSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo2QzU0RTBEQzY4NzgxMUU2QkI3REQyQ0I0MEFENzhDRSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PopaRyYAAABHSURBVHjaYmCgIfgAxP+h+AMuRYx4DPhPjFomSp05eAxADrD/WPzPgEX+A3LA/CfTAYxU88JHMvR+HE0HhA34iINNXQAQYAAFOhSSoOBmvgAAAABJRU5ErkJggg==);
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
  position: relative;
  -moz-opacity: 0.3;
  opacity: 0.3;
}
.gantt_grid_head_cell.gantt_grid_head_add {
  -moz-opacity: 0.6;
  opacity: 0.6;
  top: 0;
}
.gantt_grid_head_cell.gantt_grid_head_add:hover {
  -moz-opacity: 1;
  opacity: 1;
}
.gantt_grid_data .gantt_row:hover,
.gantt_grid_data .gantt_row.odd:hover {
  background-color: #5868c5;
}
.gantt_grid_data .gantt_row:hover .gantt_add,
.gantt_grid_data .gantt_row.odd:hover .gantt_add {
  -moz-opacity: 1;
  opacity: 1;
}
.gantt_task_row,
.gantt_row {
  border-bottom: 1px solid #000;
}
.gantt_row,
.gantt_task_row {
  background-color: #fff;
}
.gantt_row.odd,
.gantt_task_row.odd {
  background-color: #fff;
}
.gantt_row,
.gantt_cell,
.gantt_task_row,
.gantt_task_cell,
.gantt_grid_head_cell,
.gantt_scale_cell {
  box-sizing: border-box;
  -moz-box-sizing: border-box;
}
.gantt_grid_head_cell,
.gantt_scale_cell {
  line-height: inherit;
}
.gantt_grid_scale .gantt_grid_column_resize_wrap {
  cursor: col-resize;
  position: absolute;
  width: 13px;
  margin-left: -7px;
}
.gantt_grid_column_resize_wrap .gantt_grid_column_resize {
  background-color: #000;
  height: 100%;
  width: 1px;
  margin: 0 auto;
}
.gantt_task_grid_row_resize_wrap {
  cursor: row-resize;
  position: absolute;
  height: 13px;
  margin-top: -7px;
  left: 0px;
  width: 100%;
}
.gantt_task_grid_row_resize_wrap .gantt_task_grid_row_resize {
  background-color: #000;
  top: 6px;
  height: 1px;
  width: 100%;
  margin: 0 auto;
  position: relative;
}
.gantt_drag_marker {
  pointer-events: none;
}
.gantt_drag_marker.gantt_grid_resize_area,
.gantt_drag_marker.gantt_row_grid_resize_area {
  background-color: rgba(231, 231, 231, 0.5);
  height: 100%;
  width: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.gantt_drag_marker.gantt_grid_resize_area {
  border-left: 1px solid #000;
  border-right: 1px solid #000;
}
.gantt_drag_marker.gantt_row_grid_resize_area {
  border-top: 1px solid #000;
  border-bottom: 1px solid #000;
  pointer-events: none;
}
.gantt_row {
  display: flex;
}
.gantt_row > div {
  flex-shrink: 0;
  flex-grow: 0;
}
.gantt_cell {
  vertical-align: top;
  border-right: 1px solid #000;
  padding-left: 6px;
  padding-right: 6px;
  height: 100%;
  overflow: hidden;
  white-space: nowrap;
  font-size: 15px;
}
.gantt_cell_tree {
  display: flex;
  flex-wrap: nowrap;
}
.gantt_grid_scale .gantt_last_cell,
.gantt_grid_data .gantt_last_cell,
.gantt_task .gantt_task_scale .gantt_scale_cell.gantt_last_cell,
.gantt_task_bg .gantt_last_cell {
  border-right-width: 0px;
}
.gantt_task .gantt_task_scale .gantt_scale_cell.gantt_last_cell {
  border-right-width: 1px;
}
.gantt_task_bg {
  overflow: hidden;
}
.gantt_scale_cell {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  border-right: 1px solid #000;
  text-align: center;
  height: 100%;
}
.gantt_task_cell {
  display: inline-block;
  height: 100%;
  border-right: 1px solid #000;
}
.gantt_layout_cell.gantt_ver_scroll {
  width: 0px;
  background-color: transparent;
  height: 1px;
  overflow-x: hidden;
  overflow-y: scroll;
  position: absolute;
  right: 0px;
  z-index: 1;
}
.gantt_ver_scroll > div {
  width: 1px;
  height: 1px;
}
.gantt_hor_scroll {
  height: 0px;
  background-color: transparent;
  width: 100%;
  clear: both;
  overflow-x: scroll;
  overflow-y: hidden;
}
.gantt_layout_cell .gantt_hor_scroll {
  position: absolute;
}
.gantt_hor_scroll > div {
  width: 5000px;
  height: 1px;
}
.gantt_tree_indent,
.gantt_tree_icon {
  flex-grow: 0;
  flex-shrink: 0;
}
.gantt_tree_indent {
  width: 15px;
  height: 100%;
}
.gantt_tree_content,
.gantt_tree_icon {
  vertical-align: top;
}
.gantt_tree_icon {
  width: 28px;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center center;
}
.gantt_tree_content {
  height: 100%;
  white-space: nowrap;
  min-width: 0;
}
.gantt_tree_icon.gantt_open {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6QzVDNzJCOEM2ODc3MTFFNkFGNTdEMzMyODI3QzgxN0EiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6QzVDNzJCOEQ2ODc3MTFFNkFGNTdEMzMyODI3QzgxN0EiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpDNUM3MkI4QTY4NzcxMUU2QUY1N0QzMzI4MjdDODE3QSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpDNUM3MkI4QjY4NzcxMUU2QUY1N0QzMzI4MjdDODE3QSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pin6TiQAAABfSURBVHja1JLBDoAwCEOp2Ufyh/CXnSezLDoU9CAnLi3tC1BVyY6ZySbFacPOh1rMBuLuuKPcax/HogqMkpUZvApxBZMzvE8T4OQy/gXxqk5sMH5YJgEyFUCyxKALMAAp9xIuO3IRpAAAAABJRU5ErkJggg==);
  width: 18px;
  cursor: pointer;
}
.gantt_tree_icon.gantt_close {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6Qzk5M0JBOUI2ODc3MTFFNjg3NkRDN0U4RTlDQjZBRjEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6Qzk5M0JBOUM2ODc3MTFFNjg3NkRDN0U4RTlDQjZBRjEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpDOTkzQkE5OTY4NzcxMUU2ODc2REM3RThFOUNCNkFGMSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpDOTkzQkE5QTY4NzcxMUU2ODc2REM3RThFOUNCNkFGMSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PkhdOa8AAABXSURBVHjatFJBDsAwCJJmj/SH+kt2M91OVi0nYwICEaoqVZiZLGni2WYecvEXiGUCcWw0grh7irQXP+sgWeanpysOUBaofGU7wngH7Aig4gAkWxFeAQYA3h0OMhsl2ScAAAAASUVORK5CYII=);
  width: 18px;
  cursor: pointer;
}
.gantt_tree_icon.gantt_blank {
  width: 18px;
}
.gantt_tree_icon.gantt_folder_open {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RkM2MUMyMzQ2ODc3MTFFNjhFMjE5QzYzREY2ODEzREUiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RkM2MUMyMzU2ODc3MTFFNjhFMjE5QzYzREY2ODEzREUiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpGQzYxQzIzMjY4NzcxMUU2OEUyMTlDNjNERjY4MTNERSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpGQzYxQzIzMzY4NzcxMUU2OEUyMTlDNjNERjY4MTNERSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PlC39FYAAADWSURBVHjaYty4fi0DNQATA5UAskGeQPwMiP9jwfWkGDQXiCVxqGsgZBiyQTBDGNFwI5Jh2FwL8oUnMWHUgGQYNgBywFxiA7sBi0thGGwYVWOtFYi/IIn9JxI7oBtUBMTcZDiiHt2gE1B2C55wQMZiQPwZ6iIHZINgMZIKxFxEuOQ1EE9GdxXIoANQLA7E2UR6qweIP6K7iAHJVaVAzEuEQe+BeBqyAAuUBrloBxB7APEnMgL+BXI6ygfiR2QY8hSIk1mQBG4BsTy5CZLx////VEnZAAEGAO3yNxaCOyWPAAAAAElFTkSuQmCC);
}
.gantt_tree_icon.gantt_folder_closed {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RkZDMUZDMkI2ODc3MTFFNkI5QTdFOTQyM0UxNUEzRjEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RkZDMUZDMkM2ODc3MTFFNkI5QTdFOTQyM0UxNUEzRjEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpGRkMxRkMyOTY4NzcxMUU2QjlBN0U5NDIzRTE1QTNGMSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpGRkMxRkMyQTY4NzcxMUU2QjlBN0U5NDIzRTE1QTNGMSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PvnU3ycAAAB9SURBVHjaYty4fi0DNQATA5UAskGeQPwMiP9jwfWkGDQXiCVxqGsgZBiyQTBDGNFwI5Jh2FwL8oUnMWHUgGQYNgBywFxiA7sBi0thGGwYTWJt1KChaNBzKP2fRAwCL5ANSgEJkOGYp0CczIIksA1PpiUIGP///0+VMAIIMAC11ShRpw6rxAAAAABJRU5ErkJggg==);
}
.gantt_tree_icon.gantt_file {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MDQwMTVBMUU2ODc4MTFFNkFBNDVCQkYzMzdERTM1MzYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MDQwMTVBMUY2ODc4MTFFNkFBNDVCQkYzMzdERTM1MzYiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDowNDAxNUExQzY4NzgxMUU2QUE0NUJCRjMzN0RFMzUzNiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDowNDAxNUExRDY4NzgxMUU2QUE0NUJCRjMzN0RFMzUzNiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PsHnBgAAAAB8SURBVHjaYvz//z/Dpg3rGCgBfgFBDExoYp5A/AyI/+PB9dgMQzdoLhBLEnBAAzbDWND4MEMYsRjwH80wEGjE5SJSAIrLmBgoAw3UMghnGOEDjHjCjHouGjVoOBj0HCmNEMIg8AKXQSnIkgTAUyBOxpWytxFRjGAFAAEGAOcUI0XiA1ItAAAAAElFTkSuQmCC);
}
.gantt_grid_head_cell .gantt_sort {
  position: absolute;
  right: 5px;
  top: 8px;
  width: 7px;
  height: 13px;
  background-repeat: no-repeat;
  background-position: center center;
}
.gantt_grid_head_cell .gantt_sort.gantt_asc {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAANCAYAAABlyXS1AAAARUlEQVR4nGNgQAKGxib/GbABkIS7b8B/DAUwCRiGK0CXwFBAb1DfP/U/LszwHwi2X7qFgUEArBtdAVwCBmAKMCSQFSDzAWXXaOHsXeqkAAAAAElFTkSuQmCC);
}
.gantt_grid_head_cell .gantt_sort.gantt_desc {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAANCAYAAABlyXS1AAAARUlEQVR42mNgQAL1/VP/M2ADIIntF2/9x1AAlrh0C47hCmA60DFYwX88gIFGwNDY5D8uDFbg7hvwHx2jmIBTAlkB0e4BAEjlaNtBWJPnAAAAAElFTkSuQmCC);
}
.gantt_inserted,
.gantt_updated {
  font-weight: bold;
}
.gantt_deleted {
  text-decoration: line-through;
}
.gantt_invalid {
  background-color: #FFE0E0;
}
.gantt_error {
  color: red;
}
.gantt_status {
  right: 1px;
  padding: 5px 10px;
  background: rgba(155, 155, 155, 0.1);
  position: absolute;
  top: 1px;
  -webkit-transition: opacity 0.2s;
  transition: opacity 0.2s;
  opacity: 0;
}
.gantt_status.gantt_status_visible {
  opacity: 1;
}
#gantt_ajax_dots span {
  -webkit-transition: opacity 0.2s;
  transition: opacity 0.2s;
  background-repeat: no-repeat;
  opacity: 0;
}
#gantt_ajax_dots span.gantt_dot_visible {
  opacity: 1;
}
.gantt_column_drag_marker {
  border: 1px solid #CECECE;
  opacity: 0.8;
  pointer-events: none;
}
.gantt_grid_head_cell_dragged {
  border: 1px solid #CECECE;
  opacity: 0.3;
}
.gantt_grid_target_marker {
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: #000;
  transform: translateX(-1px);
}
.gantt_grid_target_marker::before,
.gantt_grid_target_marker::after {
  display: block;
  content: "";
  position: absolute;
  left: -5px;
  width: 0px;
  height: 0px;
  border: 6px solid rgba(0, 0, 0, 0);
}
.gantt_grid_target_marker::before {
  border-top-color: #000;
}
.gantt_grid_target_marker::after {
  bottom: 0;
  border-bottom-color: #000;
}
.gantt_message_area {
  position: fixed;
  right: 5px;
  width: 250px;
  z-index: 1000;
}
.gantt-info {
  min-width: 120px;
  padding: 4px 4px 4px 20px;
  font-family: "arial";
  z-index: 10000;
  margin: 5px;
  margin-bottom: 10px;
  -webkit-transition: all .5s ease;
  -moz-transition: all .5s ease;
  -o-transition: all .5s ease;
  transition: all .5s ease;
}
.gantt-info.hidden {
  height: 0px;
  padding: 0px;
  border-width: 0px;
  margin: 0px;
  overflow: hidden;
}
.gantt_modal_box {
  overflow: hidden;
  display: inline-block;
  min-width: 250px;
  width: 250px;
  text-align: center;
  position: fixed;
  z-index: 20000;
  border-color: #000;
  -webkit-box-shadow: inset 0px 0px 0px 2px #000;
  -moz-box-shadow: inset 0px 0px 0px 2px #000;
  box-shadow: inset 0px 0px 0px 2px #000;
  padding: 2px;
  font-family: "arial";
  border-radius: 6px;
  border: 1px solid #000;
  background: #fff;
}
.gantt_popup_title {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  border-width: 0px;
}
.gantt_button,
.gantt_popup_button {
  border: 1px solid #000;
  height: 30px;
  line-height: 30px;
  display: inline-block;
  margin: 0 5px;
  border-radius: 4px;
  background: #fff;
}
.gantt-info,
.gantt_popup_button,
.gantt_button {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: -moz-none;
  cursor: pointer;
}
.gantt_popup_text {
  overflow: hidden;
}
.gantt_popup_controls {
  border-radius: 6px;
  padding: 10px;
}
.gantt_popup_button {
  min-width: 100px;
}
div.dhx_modal_cover {
  background-color: #000;
  cursor: default;
  filter: progid:DXImageTransform.Microsoft.Alpha(opacity=20);
  opacity: 0.2;
  position: fixed;
  z-index: 19999;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  border: none;
  zoom: 1;
}
.gantt-info img,
.gantt_modal_box img {
  float: left;
  margin-right: 20px;
}
.gantt-alert-error,
.gantt-confirm-error {
  border: 1px solid #ff0000;
}
/*Skin section*/
.gantt_button input,
.gantt_popup_button div {
  border-radius: 4px;
  font-size: 16px;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  padding: 0px;
  margin: 0px;
  vertical-align: top;
}
.gantt_popup_title {
  border-bottom: 1px solid #000;
  height: 40px;
  line-height: 40px;
  font-size: 20px;
}
.gantt_popup_text {
  margin: 15px 15px 5px 15px;
  font-size: 16px;
  color: #000;
  min-height: 30px;
  border-radius: 6px;
}
.gantt-info,
.gantt-error {
  font-size: 16px;
  color: #000;
  box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.07);
  padding: 0px;
  background-color: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #FFFFFF;
}
.gantt-info div {
  padding: 5px 10px 5px 10px;
  background-color: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #000;
}
.gantt-error {
  background-color: #b60000;
  border: 1px solid #000;
}
.gantt-error div {
  background-color: #b60000;
  border: 1px solid #940000;
  color: #FFFFFF;
}
.gantt-warning {
  background-color: #EF7F00;
  border: 1px solid #000;
}
.gantt-warning div {
  background-color: #EF7F00;
  border: 1px solid #C97110;
  color: #FFFFFF;
}
.gantt_grid div,
.gantt_data_area div {
  -ms-touch-action: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.gantt_data_area {
  position: relative;
  overflow-x: hidden;
  overflow-y: hidden;
  -moz-user-select: -moz-none;
  -webkit-user-select: none;
  user-select: none;
}
.gantt_links_area {
  position: absolute;
  left: 0px;
  top: 0px;
}
.gantt_task_content,
.gantt_task_progress,
.gantt_side_content {
  line-height: inherit;
  overflow: hidden;
  height: 100%;
}
.gantt_task_content {
  font-size: 15px;
  color: #000;
  width: 100%;
  top: 0;
  cursor: pointer;
  position: absolute;
  white-space: nowrap;
  text-align: center;
}
.gantt_task_progress {
  text-align: center;
  z-index: 0;
  background: #18EEFF;
}
.gantt_task_progress_wrapper {
  border-radius: inherit;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.gantt_task_line {
  -webkit-border-radius: 1px;
  -moz-border-radius: 1px;
  border-radius: 1px;
  position: absolute;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  background-color: #BBF9FF;
  border: 1px solid #000;
  -webkit-user-select: none;
  -moz-user-select: none;
  -moz-user-select: -moz-none;
}
.gantt_task_line.gantt_drag_move div {
  cursor: move;
}
.gantt_touch_move,
.gantt_touch_progress .gantt_touch_resize {
  -moz-transform: scale(1.02, 1.1);
  -o-transform: scale(1.02, 1.1);
  -webkit-transform: scale(1.02, 1.1);
  transform: scale(1.02, 1.1);
  -moz-transform-origin: 50%;
  -o-transform-origin: 50%;
  -webkit-transform-origin: 50%;
  transform-origin: 50%;
}
.gantt_touch_progress .gantt_task_progress_drag,
.gantt_touch_resize .gantt_task_drag {
  -moz-transform: scaleY(1.3);
  -o-transform: scaleY(1.3);
  -webkit-transform: scaleY(1.3);
  transform: scaleY(1.3);
  -moz-transform-origin: 50%;
  -o-transform-origin: 50%;
  -webkit-transform-origin: 50%;
  transform-origin: 50%;
}
.gantt_side_content {
  position: absolute;
  white-space: nowrap;
  color: #6e6e6e;
  top: 0;
  font-size: 11px;
  font-size: 14px;
}
.gantt_side_content.gantt_left {
  right: 100%;
  padding-right: 20px;
}
.gantt_side_content.gantt_right {
  left: 100%;
  padding-left: 20px;
}
.gantt_side_content.gantt_link_crossing {
  bottom: 8.75px;
  top: auto;
}
.gantt_task_link .gantt_line_wrapper,
.gantt_link_arrow {
  position: absolute;
  cursor: pointer;
}
.gantt_line_wrapper div {
  background-color: #000;
}
.gantt_task_link:hover .gantt_line_wrapper div {
  box-shadow: 0 0 5px 0px #000;
}
.gantt_task_link div.gantt_link_arrow {
  background-color: transparent;
  border-style: solid;
  width: 0px;
  height: 0px;
}
.gantt_link_control {
  position: absolute;
  width: 20px;
  top: 0px;
}
.gantt_link_control div {
  display: none;
  cursor: pointer;
  box-sizing: border-box;
  position: relative;
  top: 50%;
  margin-top: -7.5px;
  vertical-align: middle;
  border: 1px solid #929292;
  -webkit-border-radius: 6.5px;
  -moz-border-radius: 6.5px;
  border-radius: 6.5px;
  height: 13px;
  width: 13px;
  background-color: #f0f0f0;
}
.gantt_link_control.task_right div.gantt_link_point {
  margin-left: 7px;
}
.gantt_link_control div:hover {
  background-color: #FFF;
}
.gantt_link_control.task_left {
  left: -20px;
}
.gantt_link_control.task_right {
  right: -20px;
}
.gantt_task_line.gantt_selected .gantt_link_control div,
.gantt_task_line:hover .gantt_link_control div,
.gantt_task_line.gantt_drag_progress .gantt_link_control div,
.gantt_task_line.gantt_drag_move .gantt_link_control div,
.gantt_task_line.gantt_drag_resize .gantt_link_control div,
.gantt_task_line.gantt_selected .gantt_task_progress_drag,
.gantt_task_line:hover .gantt_task_progress_drag,
.gantt_task_line.gantt_drag_progress .gantt_task_progress_drag,
.gantt_task_line.gantt_drag_move .gantt_task_progress_drag,
.gantt_task_line.gantt_drag_resize .gantt_task_progress_drag,
.gantt_task_line.gantt_selected .gantt_task_drag,
.gantt_task_line:hover .gantt_task_drag,
.gantt_task_line.gantt_drag_progress .gantt_task_drag,
.gantt_task_line.gantt_drag_move .gantt_task_drag,
.gantt_task_line.gantt_drag_resize .gantt_task_drag {
  display: block;
}
.gantt_link_target .gantt_link_control div {
  display: block;
}
.gantt_link_source,
.gantt_link_target {
  box-shadow: 0px 0px 3px #BBF9FF;
}
.gantt_link_target.link_start_allow,
.gantt_link_target.link_finish_allow {
  box-shadow: 0px 0px 3px #262626;
}
.gantt_link_target.link_start_deny,
.gantt_link_target.link_finish_deny {
  box-shadow: 0px 0px 3px #e87e7b;
}
.link_start_allow .gantt_link_control.task_start_date div,
.link_finish_allow .gantt_link_control.task_end_date div {
  background-color: #262626;
  border-color: #000000;
}
.link_start_deny .gantt_link_control.task_start_date div,
.link_finish_deny .gantt_link_control.task_end_date div {
  background-color: #e87e7b;
  border-color: #dd3e3a;
}
.gantt_link_arrow_right {
  border-width: 4px 0 4px 6px;
  border-top-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-color: transparent !important;
  border-left-color: #000;
  /*margin-top: -1px;*/
}
.gantt_link_arrow_left {
  border-width: 4px 6px 4px 0;
  margin-top: -1px;
  border-top-color: transparent !important;
  border-right-color: #000;
  border-bottom-color: transparent !important;
  border-left-color: transparent !important;
}
.gantt_link_arrow_up {
  border-width: 0 4px 6px 4px;
  border-color: transparent transparent #000 transparent;
  border-top-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-color: #000;
  border-left-color: transparent !important;
}
.gantt_link_arrow_down {
  border-width: 4px 6px 0 4px;
  border-top-color: #000;
  border-right-color: transparent !important;
  border-bottom-color: transparent !important;
  border-left-color: transparent !important;
}
.gantt_task_drag,
.gantt_task_progress_drag {
  cursor: ew-resize;
  display: none;
  position: absolute;
}
.gantt_task_drag.task_right {
  cursor: e-resize;
}
.gantt_task_drag.task_left {
  cursor: w-resize;
}
.gantt_task_drag {
  height: 100%;
  width: 8px;
  z-index: 1;
  top: -1px;
}
.gantt_task_drag.task_left {
  left: -7px;
}
.gantt_task_drag.task_right {
  right: -7px;
}
.gantt_task_progress_drag {
  height: 8px;
  width: 8px;
  bottom: -4px;
  margin-left: -4px;
  background-position: bottom;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAYAAAB24g05AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6QzM0N0ZFRkM2ODc4MTFFNjhFNUREOTU2N0ZBMkI4ODUiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6QzM0N0ZFRkQ2ODc4MTFFNjhFNUREOTU2N0ZBMkI4ODUiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpDMzQ3RkVGQTY4NzgxMUU2OEU1REQ5NTY3RkEyQjg4NSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpDMzQ3RkVGQjY4NzgxMUU2OEU1REQ5NTY3RkEyQjg4NSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pkl1dsMAAADJSURBVHjapJK9DcIwEIXjKA2SSxqnZghar0Cb7EBBmRlYwF4Bam+QMgPQuYUeKVLwcc+QAmQIKJ/05J9772TpLIgoSyGEyHnZP4879oWkEQ3exSxYB601Qbw/4i7pTYSXrLaqKur7PqquaxRa1L42YFasU9M0FEKgEexxhxo8yQbMuiiKszGGPmGtJfZc4H1pwGyklFfnHE0BD7zIjAPYKqWGruvoV+Aty/KGbByE957+BRlkxWOS6b8wBf+VLM9mEl8wp8FdgAEAX8VEJCVY4FsAAAAASUVORK5CYII=);
  background-repeat: no-repeat;
  z-index: 1;
}
.gantt_task_progress_drag:hover {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAYAAAB24g05AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAABJElEQVQoz6WRsWqDUBSG/3uvXFHQoBwcMgtu1U6BQAn4BoUMTi7t5JBH0EHo1pdw6liwa8mQMVM6BzrHJUOgNZJws4XQEkjItx04/wfn/MAZiEhKKVdSyhURSVwDEfU1TfsJw1CFYag0Tfslov5FYdd1B5zzXRzHqq5rVde1iuNYcc53rusO/u6L08FxnHS9Xn8kScKzLIMQAkIIDIdD7Pd7Pp/PnxzH+W7bdvFP0Ov1XjabzetkMmHj8RiMsaOYMYYoikBEbDabPdq2rW+328+jwLKs967rnouiwGg0Onue7/sIggDT6fTBMIz7ruvemGmaC13X78qyhO/7F/1puVwiz3O0bfvFAKiqquB53lVNNU2DNE3BAVwdPs1w3AgDoG4RHADj3FVF5NgPuQAAAABJRU5ErkJggg==);
}
.gantt_link_tooltip {
  box-shadow: 3px 3px 3px #888888;
  background-color: #fff;
  border-left: 1px dotted #cecece;
  border-top: 1px dotted #cecece;
  font-family: Tahoma;
  font-size: 8pt;
  color: #444;
  padding: 6px;
  line-height: 20px;
}
.gantt_link_direction {
  height: 0px;
  border: 0px none #000;
  border-bottom-style: dashed;
  border-bottom-width: 2px;
  transform-origin: 0% 0%;
  -ms-transform-origin: 0% 0%;
  -webkit-transform-origin: 0% 0%;
  z-index: 2;
  margin-left: 1px;
  position: absolute;
}
.gantt_grid_data .gantt_row.gantt_selected,
.gantt_grid_data .gantt_row.odd.gantt_selected {
  background-color: #5868c5;
}
.gantt_task_row.gantt_selected {
  background-color: #5868c5;
}
.gantt_task_row.gantt_selected .gantt_task_cell {
  border-right-color: #3d4dad;
}
.gantt_task_line.gantt_selected {
  box-shadow: 0 0 5px #18EEFF;
}
.gantt_task_line.gantt_project.gantt_selected {
  box-shadow: 0 0 5px #74FFA9;
}
.gantt_task_line.gantt_milestone {
  visibility: hidden;
  background-color: #c06fce;
  border: 0px solid #6e2a7a;
  box-sizing: content-box;
  -moz-box-sizing: content-box;
}
.gantt_task_line.gantt_milestone div {
  visibility: visible;
}
.gantt_task_line.gantt_milestone .gantt_task_content {
  background: inherit;
  border: inherit;
  border-width: 1px;
  border-radius: inherit;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}
.gantt_task_line.gantt_task_inline_color {
  border-color: #999999;
}
.gantt_task_line.gantt_task_inline_color .gantt_task_progress {
  background-color: #363636;
  opacity: 0.2;
}
.gantt_task_line.gantt_task_inline_color.gantt_selected {
  box-shadow: 0 0 5px #999999;
}
.gantt_task_line.gantt_task_inline_color.gantt_project.gantt_selected {
  box-shadow: 0 0 5px #999999;
}
.gantt_task_link.gantt_link_inline_color:hover .gantt_line_wrapper div {
  box-shadow: 0 0 5px 0px #999999;
}
.gantt_critical_task {
  background-color: #e63030;
  border-color: #9d3a3a;
}
.gantt_critical_task .gantt_task_progress {
  background-color: rgba(0, 0, 0, 0.4);
}
.gantt_critical_link .gantt_line_wrapper > div {
  background-color: #e63030;
}
.gantt_critical_link .gantt_link_arrow {
  border-color: #e63030;
}
.gantt_row:focus,
.gantt_cell:focus,
.gantt_btn_set:focus,
.gantt_qi_big_icon:focus,
.gantt_popup_button:focus,
.gantt_grid_head_cell:focus {
  -moz-box-shadow: inset 0px 0px 1px 1px #4d90fe;
  -webkit-box-shadow: inset 0px 0px 1px 1px #4d90fe;
  box-shadow: inset 0px 0px 1px 1px #4d90fe;
}
.gantt_split_parent,
.gantt_split_subproject {
  opacity: 0.1;
  pointer-events: none;
}
.gantt_rollup_child .gantt_link_control,
.gantt_rollup_child:hover .gantt_link_control {
  display: none;
}
.gantt_unselectable,
.gantt_unselectable div {
  -webkit-user-select: none;
  -moz-user-select: none;
  -moz-user-select: -moz-none;
}
.gantt_cal_light {
  -webkit-tap-highlight-color: transparent;
  background: #fff;
  border-radius: 6px;
  font-family: "arial";
  border: 1px solid #c1c1c1;
  color: #262626;
  font-size: 14px;
  position: absolute;
  z-index: 10001;
  width: 550px;
  height: 250px;
  border-color: #000;
  -webkit-box-shadow: inset 0px 0px 0px 2px #000;
  -moz-box-shadow: inset 0px 0px 0px 2px #000;
  box-shadow: inset 0px 0px 0px 2px #000;
  padding: 2px;
}
.gantt_cal_light_wide {
  width: 650px;
}
.gantt_cal_light select {
  font-family: "arial";
  border: 1px solid #c1c1c1;
  font-size: 14px;
  padding: 2px;
  margin: 0px;
}
.gantt_cal_ltitle {
  padding: 7px 10px;
  overflow: hidden;
  white-space: nowrap;
  -webkit-border-top-left-radius: 6px;
  -webkit-border-bottom-left-radius: 0;
  -webkit-border-top-right-radius: 6px;
  -webkit-border-bottom-right-radius: 0;
  -moz-border-radius-topleft: 6px;
  -moz-border-radius-bottomleft: 0;
  -moz-border-radius-topright: 6px;
  -moz-border-radius-bottomright: 0;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 0;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 0;
}
.gantt_cal_ltitle span {
  white-space: nowrap;
}
.gantt_cal_lsection {
  color: #727272;
  font-weight: bold;
  padding: 12px 0px 5px 10px;
}
.gantt_cal_lsection .gantt_fullday {
  float: right;
  margin-right: 5px;
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  vertical-align: top;
  cursor: pointer;
}
.gantt_cal_lsection {
  font-size: 13px;
}
.gantt_cal_ltext {
  padding: 2px	10px;
  overflow: hidden;
}
.gantt_cal_ltext textarea {
  overflow-y: auto;
  overflow-x: hidden;
  font-family: "arial";
  font-size: 14px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid #c1c1c1;
  height: 100%;
  width: 100%;
  outline: none !important;
  resize: none;
}
.gantt_section_constraint [data-constraint-time-select] {
  margin-left: 20px;
}
.gantt_time {
  font-weight: bold;
}
.gantt_cal_light .gantt_title {
  padding-left: 10px;
}
.gantt_cal_larea {
  border: 1px solid #c1c1c1;
  border-left: none;
  border-right: none;
  background-color: #fff;
  overflow: hidden;
  height: 1px;
}
.gantt_btn_set {
  margin: 10px 7px 5px 10px;
  padding: 5px 15px 5px 10px;
  float: left;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  border-width: 2px;
  border-color: #c1c1c1;
  border-style: solid;
  height: 32px;
  font-weight: bold;
  background: #fff;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
}
.gantt_hidden {
  display: none;
}
.gantt_btn_set div {
  float: left;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
  background-repeat: no-repeat;
  vertical-align: middle;
}
.gantt_save_btn {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NTM5MTFCMzA2ODc4MTFFNjk0MEE5ODRFRjQ3MEY2MkQiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NTM5MTFCMzE2ODc4MTFFNjk0MEE5ODRFRjQ3MEY2MkQiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo1MzkxMUIyRTY4NzgxMUU2OTQwQTk4NEVGNDcwRjYyRCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo1MzkxMUIyRjY4NzgxMUU2OTQwQTk4NEVGNDcwRjYyRCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pnl+P+YAAADRSURBVHjaYvz//z8DJYCJgUJArgEyQHwIiPUYQF4gESsA8b3/ELCDVM1qQPwIqvkwEPORolkbiJ9DNe8BYm5wBAAJPahp8ng0GwLxa6jmrUDMCZMDEVugEg+BWB2LZnMgfg9Vsw6I2ZDlQQQPEO+FKngJtQ2mwBaIP0HllgExC7oFMAYHEG+AKgTZZg3ErkD8FSo2F4iZsXkPmQMyfQlUA0jjdyh7KhAz4gofdAFGqAYY6ManGYRZ0FIYKGNkA/EHIP4HxLWEkiTjkM1McAAQYAAwa97nPE+LsgAAAABJRU5ErkJggg==);
  margin-top: 2px;
  width: 21px;
}
.gantt_cancel_btn {
  margin-top: 2px;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NTc0QzgxQ0U2ODc4MTFFNkE5NTJDMTkyNTg2QUExRDAiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NTc0QzgxQ0Y2ODc4MTFFNkE5NTJDMTkyNTg2QUExRDAiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo1NzRDODFDQzY4NzgxMUU2QTk1MkMxOTI1ODZBQTFEMCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo1NzRDODFDRDY4NzgxMUU2QTk1MkMxOTI1ODZBQTFEMCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pk+05wsAAAFbSURBVHjajNM7LwRRGMbxNQYFSpXKNqwVEtFsIhIismQ/wOg3roVoNL6BhhWFy2p9A5dCKCjEJktiCQWJQkOJiEXi/ybPyJiwmTf5ZWbPnOedObNnqjzPi4WqHVkMo0Vjd9hDHlfByU7gvBYruMAs2lAnCY3ZtTU0hBtYeBvT+MQyUmiUlMbs2hh2/SauGixiEA/I4Dy0rBPcYkhP1osFTNkTJDGO8j9hqyYcKnyDdz1J0tELq9baKoXtRpfow7oyWWuQ1sStCOEBPAbmpq1BXD+KEcNWZzrG3djfFQ734ylwvUbHsqNNYtUdMWzVoeO9ox1mNRox7M+12rcGm/jSX3kUIdypuZbJW4MSNrRlW3FdIdyFHc21v73kb+U5HPtvFvPoQb3Y+RJO0YwDfRs/38IzRrCq7T2DAl6koDFXd85o5/76Gq3JpNaY05b9wKveSU5LmMCbH/oWYAA8HFpKXgW38QAAAABJRU5ErkJggg==);
  width: 20px;
}
.gantt_delete_btn {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NUFDNTg4MDI2ODc4MTFFNkI5QkY5OTIyMjkyMTFFNTkiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NUFDNTg4MDM2ODc4MTFFNkI5QkY5OTIyMjkyMTFFNTkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo1QUM1ODgwMDY4NzgxMUU2QjlCRjk5MjIyOTIxMUU1OSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo1QUM1ODgwMTY4NzgxMUU2QjlCRjk5MjIyOTIxMUU1OSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PuQSVckAAABGSURBVHjaYvj//z8DFlz/HxPUY1NLrGachjCCTQGaw0AeYGRioBCwwEyi1AAYINYrcAsp9sKoAaMGYDPgCBF6jiJzAAIMAPUxcjmgixXRAAAAAElFTkSuQmCC);
  margin-top: 2px;
  width: 20px;
}
.gantt_cal_cover {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 10000;
  top: 0px;
  left: 0px;
  background-color: black;
  opacity: 0.1;
  filter: progid:DXImageTransform.Microsoft.Alpha(opacity=10);
}
.gantt_custom_button {
  padding: 0px 3px 0px 3px;
  font-family: "arial";
  font-size: 14px;
  font-weight: normal;
  margin-right: 10px;
  margin-top: -5px;
  cursor: pointer;
  float: right;
  height: 21px;
  width: 90px;
  border: 1px solid #CECECE;
  text-align: center;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
}
.gantt_custom_button div {
  cursor: pointer;
  float: none;
  height: 21px;
  line-height: 21px;
  vertical-align: middle;
}
.gantt_custom_button div:first-child {
  display: none;
}
.gantt_cal_light_wide {
  width: 580px;
  padding: 2px 4px;
}
.gantt_cal_light_wide .gantt_cal_larea {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid #c1c1c1;
}
.gantt_cal_light_wide .gantt_cal_lsection {
  border: 0px;
  float: left;
  text-align: right;
  width: 80px;
  height: 20px;
  padding: 5px 10px 0px 0px;
}
.gantt_cal_light_wide .gantt_wrap_section {
  position: relative;
  padding: 10px 0;
  overflow: hidden;
  border-bottom: 1px solid #000;
}
.gantt_cal_light_wide .gantt_section_time {
  overflow: hidden;
  padding-top: 2px !important;
  padding-right: 0px;
  height: 20px !important;
}
.gantt_cal_light_wide .gantt_cal_ltext {
  padding-right: 0px;
}
.gantt_cal_light_wide .gantt_cal_larea {
  padding: 0 10px;
  width: 100%;
}
.gantt_cal_light_wide .gantt_section_time {
  background: transparent;
}
.gantt_cal_light_wide .gantt_cal_checkbox label {
  padding-left: 0px;
}
.gantt_cal_light_wide .gantt_cal_lsection .gantt_fullday {
  float: none;
  margin-right: 0px;
  font-weight: bold;
  cursor: pointer;
}
.gantt_cal_light_wide .gantt_custom_button {
  position: absolute;
  top: 0;
  right: 0;
  margin-top: 2px;
}
.gantt_cal_light_wide .gantt_repeat_right {
  margin-right: 55px;
}
.gantt_cal_light_wide.gantt_cal_light_full {
  width: 738px;
}
.gantt_cal_wide_checkbox input {
  margin-top: 8px;
  margin-left: 14px;
}
.gantt_cal_light input {
  font-size: 14px;
}
.gantt_section_time {
  background-color: white;
  white-space: nowrap;
  padding: 2px 10px 5px;
  padding-top: 2px !important;
}
.gantt_section_time .gantt_time_selects {
  float: left;
  height: 25px;
}
.gantt_section_time .gantt_time_selects select {
  height: 23px;
  padding: 2px;
  border: 1px solid #c1c1c1;
}
.gantt_duration {
  width: 100px;
  height: 23px;
  float: left;
  white-space: nowrap;
  margin-left: 20px;
  line-height: 23px;
}
.gantt_duration .gantt_duration_value,
.gantt_duration .gantt_duration_dec,
.gantt_duration .gantt_duration_inc {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  text-align: center;
  vertical-align: top;
  height: 100%;
  border: 1px solid #c1c1c1;
}
.gantt_duration .gantt_duration_value {
  width: 40px;
  padding: 3px 4px;
  border-left-width: 0;
  border-right-width: 0;
}
.gantt_duration .gantt_duration_value.gantt_duration_value_formatted {
  width: 70px;
}
.gantt_duration .gantt_duration_dec,
.gantt_duration .gantt_duration_inc {
  width: 20px;
  padding: 1px;
  padding-bottom: 1px;
  background: #fff;
}
.gantt_duration .gantt_duration_dec {
  -moz-border-top-left-radius: 4px;
  -moz-border-bottom-left-radius: 4px;
  -webkit-border-top-left-radius: 4px;
  -webkit-border-bottom-left-radius: 4px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.gantt_duration .gantt_duration_inc {
  margin-right: 4px;
  -moz-border-top-right-radius: 4px;
  -moz-border-bottom-right-radius: 4px;
  -webkit-border-top-right-radius: 4px;
  -webkit-border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.gantt_resources {
  max-height: 150px;
  height: auto;
  overflow-y: auto;
}
.gantt_resource_row {
  display: block;
  padding: 10px 0;
  border-bottom: 1px solid #000;
  cursor: pointer;
}
.gantt_resource_row input[type=checkbox]:not(:checked),
.gantt_resource_row input[type=checkbox]:not(:checked) ~ div {
  opacity: 0.5;
}
.gantt_resource_toggle {
  vertical-align: middle;
}
.gantt_resources_filter .gantt_resources_filter_input {
  padding: 1px 2px 1px 2px;
  box-sizing: border-box;
}
.gantt_resources_filter .switch_unsetted {
  vertical-align: middle;
}
.gantt_resource_cell {
  display: inline-block;
}
.gantt_resource_cell.gantt_resource_cell_checkbox {
  width: 24px;
  max-width: 24px;
  min-width: 24px;
  vertical-align: middle;
}
.gantt_resource_cell.gantt_resource_cell_label {
  width: 40%;
  max-width: 40%;
  vertical-align: middle;
}
.gantt_resource_cell.gantt_resource_cell_value {
  width: 30%;
  max-width: 30%;
  vertical-align: middle;
}
.gantt_resource_cell.gantt_resource_cell_value input,
.gantt_resource_cell.gantt_resource_cell_value select {
  width: 80%;
  vertical-align: middle;
  padding: 1px 2px 1px 2px;
  box-sizing: border-box;
}
.gantt_resource_cell.gantt_resource_cell_unit {
  width: 10%;
  max-width: 10%;
  vertical-align: middle;
}
.gantt_resource_early_value {
  opacity: 0.8;
  font-size: 0.9em;
}
/* Quick info */
.gantt_cal_quick_info {
  border: 1px solid #000;
  border-radius: 6px;
  position: absolute;
  z-index: 300;
  border-color: #000;
  -webkit-box-shadow: inset 0px 0px 0px 2px #000;
  -moz-box-shadow: inset 0px 0px 0px 2px #000;
  box-shadow: inset 0px 0px 0px 2px #000;
  padding: 2px;
  background-color: #fff;
  width: 300px;
  transition: left 0.5s ease, right 0.5s;
  -moz-transition: left 0.5s ease, right 0.5s;
  -webkit-transition: left 0.5s ease, right 0.5s;
  -o-transition: left 0.5s ease, right 0.5s;
}
.gantt_no_animate {
  transition: none;
  -moz-transition: none;
  -webkit-transition: none;
  -o-transition: none;
}
.gantt_cal_quick_info.gantt_qi_left .gantt_qi_big_icon {
  float: right;
}
.gantt_cal_qi_title {
  -webkit-border-top-left-radius: 6px;
  -webkit-border-bottom-left-radius: 0;
  -webkit-border-top-right-radius: 6px;
  -webkit-border-bottom-right-radius: 0;
  -moz-border-radius-topleft: 6px;
  -moz-border-radius-bottomleft: 0;
  -moz-border-radius-topright: 6px;
  -moz-border-radius-bottomright: 0;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 0;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 0;
  padding: 5px 0px 8px 12px;
  color: #000;
  background-color: #fff;
  border-bottom: 1px solid #000;
}
.gantt_cal_qi_tdate {
  font-size: 14px;
  font-weight: bold;
}
.gantt_cal_qi_tcontent {
  font-size: 14px;
}
.gantt_cal_qi_content {
  padding: 16px 8px;
  font-size: 14px;
  color: #000;
  overflow: hidden;
}
.gantt_cal_qi_controls {
  -webkit-border-top-left-radius: 0;
  -webkit-border-bottom-left-radius: 6px;
  -webkit-border-top-right-radius: 0;
  -webkit-border-bottom-right-radius: 6px;
  -moz-border-radius-topleft: 0;
  -moz-border-radius-bottomleft: 6px;
  -moz-border-radius-topright: 0;
  -moz-border-radius-bottomright: 6px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 6px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 6px;
  padding-left: 7px;
}
.gantt_cal_qi_controls .gantt_menu_icon {
  margin-top: 6px;
  background-repeat: no-repeat;
}
.gantt_cal_qi_controls .gantt_menu_icon.icon_edit {
  width: 20px;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAB3RJTUUH3QYFCjI5ZQj5bAAAAFNJREFUOMvt0zEOACAIA0DkwTymH8bJTRTKZGJXyaWEKPKTCQAH4Ls37cItcDUzsxHNDLZNhCq7Gt1wh9ErV7EjyGAhyGLphlnsClWuS32rn0czAV+vNGrM/LBtAAAAAElFTkSuQmCC);
}
.gantt_cal_qi_controls .gantt_menu_icon.icon_delete {
  width: 20px;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NUFDNTg4MDI2ODc4MTFFNkI5QkY5OTIyMjkyMTFFNTkiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NUFDNTg4MDM2ODc4MTFFNkI5QkY5OTIyMjkyMTFFNTkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo1QUM1ODgwMDY4NzgxMUU2QjlCRjk5MjIyOTIxMUU1OSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo1QUM1ODgwMTY4NzgxMUU2QjlCRjk5MjIyOTIxMUU1OSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PuQSVckAAABGSURBVHjaYvj//z8DFlz/HxPUY1NLrGachjCCTQGaw0AeYGRioBCwwEyi1AAYINYrcAsp9sKoAaMGYDPgCBF6jiJzAAIMAPUxcjmgixXRAAAAAElFTkSuQmCC);
}
.gantt_qi_big_icon {
  font-size: 13px;
  border-radius: 4px;
  font-weight: bold;
  background: #fff;
  margin: 5px 9px 8px 0px;
  min-width: 60px;
  line-height: 32px;
  vertical-align: middle;
  padding: 0px 10px 0px 5px;
  cursor: pointer;
  border: 1px solid #000;
}
.gantt_cal_qi_controls div {
  float: left;
  height: 32px;
  text-align: center;
  line-height: 32px;
}
.gantt_cal_quick_info.gantt_qi_hidden {
  display: none;
}
.gantt_tooltip {
  padding: 10px;
  position: absolute;
  z-index: 50;
  white-space: nowrap;
}
.gantt_resource_marker {
  position: absolute;
  text-align: center;
  font-size: 14px;
  color: #FFF;
}
.gantt_resource_marker_ok {
  background: rgba(78, 208, 134, 0.75);
}
.gantt_resource_marker_overtime {
  background: rgba(255, 134, 134, 0.69);
}
.gantt_histogram_label {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1;
  font-weight: bold;
  font-size: 14px;
}
.gantt_histogram_fill {
  background-color: rgba(41, 157, 180, 0.2);
  width: 100%;
  position: absolute;
  bottom: 0;
}
.gantt_histogram_hor_bar {
  height: 1px;
  position: absolute;
  background: #299DB4;
  margin-top: -1px;
  margin-left: -1px;
}
.gantt_histogram_vert_bar {
  width: 1px;
  position: absolute;
  background: #299DB4;
  margin-left: -1px;
}
.gantt_histogram_cell {
  position: absolute;
  text-align: center;
  font-size: 14px;
  color: #000000;
}
.gantt_marker {
  height: 100%;
  width: 2px;
  top: 0;
  position: absolute;
  text-align: center;
  background-color: rgba(255, 0, 0, 0.4);
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.gantt_marker .gantt_marker_content {
  padding: 5px;
  background: inherit;
  color: white;
  position: absolute;
  font-size: 12px;
  line-height: 12px;
  opacity: 0.8;
}
.gantt_marker_area {
  position: absolute;
  top: 0;
  left: 0;
}
.gantt_grid_editor_placeholder {
  position: absolute;
}
.gantt_grid_editor_placeholder > div,
.gantt_grid_editor_placeholder input,
.gantt_grid_editor_placeholder select {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
.gantt_row_placeholder div {
  opacity: 0.5;
}
.gantt_row_placeholder .gantt_file,
.gantt_row_placeholder .gantt_add {
  display: none;
}
.gantt_drag_marker.gantt_grid_dnd_marker {
  background-color: transparent;
  transition: all 0.1s ease ;
}
.gantt_grid_dnd_marker_line {
  height: 4px;
  width: 100%;
  background-color: #3498db;
}
.gantt_grid_dnd_marker_line::before {
  background: #fff;
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 3px solid #3498db;
  border-radius: 6px;
  content: "";
  line-height: 1px;
  display: block;
  position: absolute;
  margin-left: -11px;
  margin-top: -4px;
  pointer-events: none;
}
.gantt_grid_dnd_marker_folder {
  height: 100%;
  width: 100%;
  position: absolute;
  pointer-events: none;
  box-sizing: border-box;
  box-shadow: 0 0 0px 2px #3f98db inset;
  background: transparent;
}
.gantt_overlay_area {
  position: absolute;
  height: inherit;
  width: inherit;
  top: 0;
  left: 0;
  display: none;
}
.gantt_overlay {
  position: absolute;
  left: 0;
  top: 0;
  height: inherit;
  width: inherit;
}
.gantt_click_drag_rect {
  position: absolute;
  left: 0;
  top: 0;
  outline: 1px solid #3f98db;
  background-color: rgba(52, 152, 219, 0.3);
}
.gantt_timeline_move_available,
.gantt_timeline_move_available * {
  cursor: move;
}
.gantt_rtl .gantt_grid {
  text-align: right;
}
.gantt_rtl .gantt_row,
.gantt_rtl .gantt_cell {
  flex-direction: row-reverse;
}
.gantt_layout_content {
  width: 100%;
  overflow: auto;
  box-sizing: border-box;
}
.gantt_layout_cell {
  position: relative;
  box-sizing: border-box;
}
.gantt_layout_cell > .gantt_layout_header {
  background: #33aae8;
  color: white;
  font-size: 17px;
  padding: 5px 10px;
  box-sizing: border-box;
}
.gantt_layout_header.collapsed_x {
  background: #a9a9a9;
}
.gantt_layout_header.collapsed_x .gantt_header_arrow:before {
  content: "\21E7";
}
.gantt_layout_header.collapsed_y {
  background: #a9a9a9;
}
.gantt_layout_header.collapsed_y .gantt_header_arrow:before {
  content: "\21E9";
}
.gantt_layout_header {
  cursor: pointer;
}
.gantt_layout_header .gantt_header_arrow {
  float: right;
  text-align: right;
}
.gantt_layout_header .gantt_header_arrow:before {
  content: "\21E6";
}
.gantt_layout_header.vertical .gantt_header_arrow:before {
  content: "\21E7";
}
.gantt_layout_outer_scroll_vertical .gantt_layout_content {
  overflow-y: hidden;
}
.gantt_layout_outer_scroll_horizontal .gantt_layout_content {
  overflow-x: hidden;
}
.gantt_layout_x > .gantt_layout_cell {
  display: inline-block;
  vertical-align: top;
}
.gantt_layout_x {
  white-space: nowrap;
}
.gantt_resizing {
  opacity: 0.7;
  background: #f2f2f2;
}
.gantt_layout_cell_border_right.gantt_resizer {
  overflow: visible;
  border-right: 0;
}
.gantt_resizer {
  cursor: e-resize;
  position: relative;
}
.gantt_resizer_y {
  cursor: n-resize;
}
.gantt_resizer_stick {
  background: #33aae8;
  z-index: 9999;
  position: absolute;
  top: 0;
  width: 100%;
}
.gantt_resizer_x .gantt_resizer_x {
  position: absolute;
  width: 20px;
  height: 100%;
  margin-left: -10px;
  top: 0;
  left: 0;
  z-index: 1;
}
.gantt_resizer_y .gantt_resizer_y {
  position: absolute;
  height: 20px;
  width: 100%;
  top: -10px;
  left: 0;
  z-index: 1;
}
.gantt_resizer_error {
  background: indianred!important;
}
.gantt_noselect {
  -webkit-user-select: none;
  user-select: none;
}
.gantt_layout_cell_border_left {
  border-left: 1px solid #000;
}
.gantt_layout_cell_border_right {
  border-right: 1px solid #000;
}
.gantt_layout_cell_border_top {
  border-top: 1px solid #000;
}
.gantt_layout_cell_border_bottom {
  border-bottom: 1px solid #000;
}
.gantt_layout_cell_border_transparent {
  border-color: transparent;
}
.gantt_window {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 999999999;
  background: white;
}
.gantt_window_content {
  position: relative;
}
.gantt_window_content_header {
  background: #39c;
  color: #ffffff;
  height: 33px;
  padding: 10px 10px 0 10px;
  border-bottom: solid 2px #ffffff;
  position: relative;
}
.gantt_window_content_header_text {
  padding-left: 10%;
}
.gantt_window_content_header_buttons {
  position: absolute;
  top: 10px;
  right: 10px;
}
.gantt_window_content_header_buttons:hover {
  color: #000000;
  cursor: pointer;
}
.gantt_window_content_resizer {
  position: absolute;
  width: 15px;
  height: 15px;
  bottom: 0;
  line-height: 15px;
  right: -1px;
  text-align: center;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAMAAAAMCGV4AAAABlBMVEUAAAAAAAClZ7nPAAAAAXRSTlMAQObYZgAAABZJREFUeAFjIAUwUshlpJDLSIhLGAAACQ4AFk79JaMAAAAASUVORK5CYII=);
  cursor: nw-resize;
  z-index: 999;
}
.gantt_window_content_frame {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  z-index: 9999;
}
.gantt_window_drag {
  cursor: pointer!important;
}
.gantt_window_resizing {
  overflow: visible;
}
.gantt_window_resizing_body {
  overflow: hidden!important;
}
.gantt_window_modal {
  background: rgba(0, 0, 0, 0.1);
  z-index: 9999;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  position: fixed;
}
.gantt_container,
.gantt_cal_light,
.gantt_message_area,
.gantt_modal_box,
.gantt_cal_quick_info,
.gantt_tooltip {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.gantt_noselect {
  -moz-user-select: -moz-none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.gantt_noselect .gantt_grid_data .gantt_row:hover,
.gantt_noselect .gantt_grid_data .gantt_row.odd:hover {
  background-color: unset;
}
.gantt_drag_marker {
  position: absolute;
  top: -1000px;
  left: -1000px;
  font-family: "arial";
  font-size: 14px;
  z-index: 1;
  white-space: nowrap;
}
.gantt_drag_marker .gantt_tree_indent,
.gantt_drag_marker .gantt_tree_icon.gantt_blank,
.gantt_drag_marker .gantt_tree_icon.gantt_open,
.gantt_drag_marker .gantt_tree_icon.gantt_close {
  display: none;
}
.gantt_empty_state_wrapper {
  position: relative;
}
.gantt_empty_state {
  height: 100%;
  max-width: 500px;
  box-sizing: border-box;
  white-space: pre-line;
  overflow-wrap: break-word;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin: 0 auto;
}
.gantt_empty_state_image {
  background-image: url('data:image/png;base64,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');
  background-repeat: no-repeat;
  width: 100%;
  max-width: 500px;
  max-height: 500px;
  margin-left: auto;
  margin-right: auto;
  display: block;
  min-height: 0;
  height: 200px;
  background-size: contain;
  background-position-x: center;
  background-position-y: bottom;
  margin-top: 40px;
}
.gantt_empty_state_text {
  text-align: center;
}
.gantt_empty_state_text_link {
  color: #03A9F4;
  background-position: top right;
  background: none;
  opacity: 1;
  height: unset;
  cursor: pointer;
}
.gantt_drag_marker,
.gantt_drag_marker .gantt_row.odd {
  background-color: #fff;
}
.gantt_drag_marker .gantt_row {
  border-left: 1px solid #000000;
  border-top: 1px solid #000000;
}
.gantt_drag_marker .gantt_cell {
  border-color: #000000;
}
.gantt_row.gantt_over,
.gantt_task_row.gantt_over {
  background-color: #0070fe;
}
.gantt_row.gantt_transparent .gantt_cell {
  opacity: 0.7;
}
.gantt_task_row.gantt_transparent {
  background-color: #ffffff;
}
.gantt_popup_button.gantt_delete_button {
  font-weight: bold;
  border-style: solid;
  border-width: 2px;
  border-color: #000;
  background: #006600;
  color: #000;
  border-color: transparent;
  color: #fff;
}
.gantt_container_resize_watcher {
  background: transparent;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: -1;
  pointer-events: none;
  border: 0;
  box-sizing: border-box;
  opacity: 0;
}

