package net.bioknow.cdtms.studyClose;

import net.bioknow.mvc.ioc.AppStartFace;
import net.bioknow.passport.uiframe.FuncUtil;
import net.bioknow.uap.dbdatamng.function.FuncFactory;

import javax.servlet.ServletConfig;

public class EHAppStart implements AppStartFace{

    public void onStart(ServletConfig arg) throws Exception {


        FuncFactory.addTableFunc(new DTRFstudyClose());
        FuncFactory.addTableFunc(new DTRFstudyOpen());
        FuncUtil.addFunction("prjfunc", "studyClose", "项目关闭", (String)null, (String)null, "configdb.listcfg.do?xmlpath=/cdtms/studyclose/configform.xml");

        //ConfigDbUtil.registerCFGFile(DAOstudyCloseIntegrate.xmlpath);

    }

}
