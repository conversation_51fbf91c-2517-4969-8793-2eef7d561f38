<%@ page contentType="text/html;charset=utf-8" %>
<%@ page language="java" pageEncoding="utf-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="/webutil/tlib/bioknow-tlib.tld" prefix="bt" %>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<html>
<head>
    <link rel="stylesheet" type="text/css" href="/public/bootstrap/css/bootstrap.min.css?v=1733116427423">
    <link rel="stylesheet" type="text/css" href="/public/css/public.css?v=1733116427423">
<style type="text/css">
	input{width:200px;}
	select{width:200px;}
</style>
</head>
<body style="width:100%; font-size:12px; color:#222;">
	<div class="dragData">
		<bt:lang name="订单信息"/><hr/>
		<table class="table">
			<tr>
				<td><bt:lang name="客户单号："/></td><td><input type="text" class="ServerNumber inpt" /></td>
				<td><font color="red">* </font><bt:lang name="项目编号："/></td><td><input type="text" value="CAD-001" disabled="disabled" class="ProjectNumber inpt" /></td>
			</tr>
			<tr>
				<td><bt:lang name="货物类型："/></td>
				<td><select class="StockType inpt"></select></td>
			</tr>
			<tr>
				<td><bt:lang name="温度计类型："/></td>
				<td><select class="Temperature"></select></td>
				<td><bt:lang name="湿度计类型："/></td>
				<td><select class="Hygrometer"></select></td>
			</tr>
			<tr>
				<td><font color="red">* </font><bt:lang name="运输实效："/></td>
				<td><select class="Timeliness"></select></td>
				<td><font color="red">* </font><bt:lang name="运输温度："/></td>
				<td><select class="TransportTemperature"></select></td>
			</tr>
			<tr>
				<td><bt:lang name="是否投保："/></td>
				<td><select class="Insurance">
					<option value="1"></option>
					<option value="0" selected="selected">不投保</option>
				</select></td>
				<td><bt:lang name="投保金额："/></td><td><input type="text" class="InsuredAmount" /></td>
			</tr>
		</table>
	</div>
	<div class="senderData">
		发件人信息<hr/>
		<table class="table">
			<tr>
				<td><font color="red">* </font>发件城市：</td><td><select class="SenderCityId"></select></td>
			</tr>
			<tr>
				<td><font color="red">* </font>发件人姓名：</td><td><input type="text" class="SenderName" value="${transportData.sendername}" /></td>
				<td><font color="red">* </font>发件人电话：</td><td><input type="text" class="SenderPhone" value="${transportData.senderphone}" /></td>
			</tr>
			<tr>
				<td>发件人单位：</td><td><input type="text" class="SenderCorporation" value="${transportData.senderposition}" /></td>
				<td><font color="red">* </font>发件人地址：</td><td><input type="text" class="SenderAddress" value="${transportData.senderaddress}" /></td>
			</tr>
			<tr>
				<td>委托人邮箱：</td><td><input type="text" class="Mail" /></td>
				<td>备注：</td><td><input type="text" class="Remark" /></td>
			</tr>
			<tr>
				<td><font color="red">* </font>预约取件时间：</td><td><input type="text" class="PickUpTime"  onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" /></td>
<!-- 				<td>当前时间：</td><td><input type="text" class="Time" /></td> -->
			</tr>
		</table>
	</div>
	<div class="receiverData">
		收件人信息<hr/>
		<table class="table">
			<tr>
				<td><font color="red">* </font>收件城市：</td><td><select class="ReceiverCityId"></select></td>
			</tr>
			<tr>
				<td><font color="red">* </font>收件人姓名：</td><td><input type="text" class="ReceiverName" value="${transportData.receivername}" /></td>
				<td><font color="red">* </font>收件人电话：</td><td><input type="text" class="ReceiverPhone" value="${transportData.receiverphone}" /></td>
			</tr>
			<tr>
				<td>收件人单位：</td><td><input type="text" class="ReceiverCorporation" value="${transportData.receiverphone}" /></td>
				<td><font color="red">* </font>收件人地址：</td><td><input type="text" class="ReceiverAddress" value="${transportData.receiveraddress}" /></td>
			</tr>
		</table>
	</div>
	<input type="hidden" class="examine_records" value="${transportData.testrecordno}" />
	<input type="hidden" class="examine_id" value="${transportData.id}" />
	<div class="choiceDrag">
		样本信息<hr/>
<!-- 		<table class="choiceDrag_table table" style="border-collapse:separate; border-spacing:0px 10px;text-align:rigth;padding:4px 8px 4px 8px;"> -->
		<table class="choiceDrag_table table">
			<tr><td>检验记录</td><td>受试者信息</td><td>样本编号</td></tr>
			<c:forEach items="${sample }" var="sam">
				<tr>
					<td>${sam.testrecordno }</td>
					<td>${sam.subjectid }</td>
					<td>${sam.no }</td>
				</tr>
			</c:forEach>
		</table>
	</div>
	<button class="btn btn-sm btn-outline-secondary btn-radius" id="saveBtn"> 保存  </button>
<script type="text/javascript" src="/public/js/jquery-3.2.1.min.js?v=1733116427423"></script>
<script type="text/javascript" src="/public/bootstrap/js/bootstrap.js?v=1733116427423"></script>
<script type="text/javascript" src="/dbplug/portalmobile/js/md5.js?v=1733116427423"></script>
<script src="/webutil/js/date/WdatePicker.js?v=1733116427423" type="text/javascript"></script>
<script type="text/javascript">
$(function(){
	$(".table tr").each(function(){ 
		$(this).children("td:eq(0)").css("text-align","right");
		$(this).children("td:eq(2)").css("text-align","right");
	})
	$("#saveBtn").click(function(){
		if(isNull()!=""){alert(isNull());return;}
		var myData = {
				examine_id:$(".examine_id").val(),
				examine_records:$(".examine_records").val(),
				ServerNumber:$(".ServerNumber").val(),
				ProjectNumber:$(".ProjectNumber").val(),
				StockType:$(".StockType option:selected").val(),
				Temperature:$(".Temperature option:selected").val(),
				Hygrometer:$(".Hygrometer option:selected").val(),
				Timeliness:$(".Timeliness option:selected").val(),
				TransportTemperature:$(".TransportTemperature option:selected").val(),
				Insurance:$(".Insurance option:selected").val(),
				InsuredAmount:$(".InsuredAmount").val(),
				SenderCityId:$(".SenderCityId option:selected").val(),
				SenderName:$(".SenderName").val(),
				SenderPhone:$(".SenderPhone").val(),
				SenderCorporation:$(".SenderCorporation").val(),
				SenderAddress:$(".SenderAddress").val(),
				ReceiverCityId:$(".ReceiverCityId option:selected").val(),
				ReceiverName:$(".ReceiverName").val(),
				ReceiverPhone:$(".ReceiverPhone").val(),
				ReceiverCorporation:$(".ReceiverCorporation").val(),
				ReceiverAddress:$(".ReceiverAddress").val(),
				Mail:$(".Mail").val(),
				PickUpTime:$(".PickUpTime").val(),
				Remark:$(".Remark").val(),
				Time:$(".Time").val()
		};
		$.ajax( {
	        url: "/ashsh.saveOrder.do",
	        type: "POST",
	        data: myData,
	        success: function(msg){
	        	alert(msg);
	        }
	    });
	})
	
	$("input[type=radio]").click(function(){
		var cla = $(this).attr("class");
		$("."+cla+"").removeAttr("checked");
		$(this).attr("checked","checked");
	})
	$.ajax({ 
		url: "ashsh.obtainrData.do",
		type: "POST",
		data:{"type":"bbh"},
		success: function(data){
			var Json = JSON.parse(data);
			var Str = "";
			for(var key in Json.Body.StockType){
				if(key==3){Str+="<option value="+key+" selected='selected'>"+Json.Body.StockType[key]+"</option>";}else
				Str+="<option value="+key+">"+Json.Body.StockType[key]+"</option>";
			}
			$(".StockType").html(Str);Str = "";
			for(var key in Json.Body.Temperature){
				if(key==4){Str+="<option value="+key+" selected='selected'>"+Json.Body.Temperature[key]+"</option>";}else
				Str+="<option value="+key+">"+Json.Body.Temperature[key]+"</option>";
			}
			$(".Temperature").html(Str);Str = "";
			for(var key in Json.Body.Hygrometer){
				if(key==1){Str+="<option value="+key+" selected='selected'>"+Json.Body.Hygrometer[key]+"</option>";}else
				Str+="<option value="+key+">"+Json.Body.Hygrometer[key]+"</option>";
			}
			$(".Hygrometer").html(Str);Str="<option></option>";
			for(var key in Json.Body.Timeliness){
				Str+="<option value="+key+">"+Json.Body.Timeliness[key]+"</option>";
			}
			$(".Timeliness").html(Str);Str="<option></option>";
			for(var key in Json.Body.TransportTemperature){
				Str+="<option value="+key+">"+Json.Body.TransportTemperature[key]+"</option>";
			}
			$(".TransportTemperature").html(Str);Str="<option></option>";
			for(var key in Json.Body.City){
				Str+="<option value="+Json.Body.City[key].CityId+">"+Json.Body.City[key].CityName+"</option>";
			}
			$(".SenderCityId,.ReceiverCityId").html(Str);
		}
	});
})
function isNull(){
	var tips = "";
	if($(".ProjectNumber").val()==null || $(".ProjectNumber").val()==""){tips="项目编号为空！ \r";}
	if($(".Timeliness option:selected").val()==null || $(".Timeliness option:selected").val()==""){tips+="运输实效为空！ \r";}
	if($(".TransportTemperature option:selected").val()==null || $(".TransportTemperature option:selected").val()==""){tips+="运输温度为空！ \r";}
	if($(".SenderCityId option:selected").val()==null || $(".SenderCityId option:selected").val()==""){tips+="发件城市为空！ \r";}
	if($(".PickUpTime").val()==null || $(".PickUpTime").val()==""){tips+="预约取件时间为空！ \r";}
	if($(".ReceiverCityId option:selected").val()==null || $(".ReceiverCityId option:selected").val()==""){tips+="收件城市为空！ \r";}
	return tips;
}

</script>
</body>
</html>
