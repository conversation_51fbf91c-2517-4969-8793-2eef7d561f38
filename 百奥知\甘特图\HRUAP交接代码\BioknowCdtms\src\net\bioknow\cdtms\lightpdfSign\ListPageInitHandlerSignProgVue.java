package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.mvc.tools.Language;
import net.bioknow.services.entity.BeanListFieldParam;
import net.bioknow.services.uap.dbdatamng.face.FaceListPageInit;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.face.ListPageInitFace;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ListPageInitHandlerSignProgVue implements FaceListPageInit {


	@Override
	public boolean canUse(int i, String s, String tableid) {
		try {
			//获取当前用户是否有在线签字权限

			if (StringUtils.equals(tableid, "esign_instance")) {
				return true;
			}

		}
		catch (Exception e){
			Log.error("",e);
		}
		return false;
	}

	@Override
	public BeanListFieldParam initBeanlistFieldParam() {
		BeanListFieldParam bean = new BeanListFieldParam();
		//bean.setSlotName("cdtms_signprog");
		bean.setLabel("签字情况");
		bean.setWidth(100);
		bean.setProp("cdtms_signprog");
		bean.setTextHtml(true);
		return bean;
	}

	@Override
	public boolean onlyOneField() {
		return FaceListPageInit.super.onlyOneField();
	}

	@Override
	public Map<String, List<BeanListFieldParam>> initBeanlistFieldParams(String projectId, List listField) {
		return FaceListPageInit.super.initBeanlistFieldParams(projectId, listField);
	}

	@Override
	public void initValue(List list, List list1, String projectId, String s1) throws Exception {
		try {

			DAODataMng daoDataMng = new DAODataMng(projectId);


			Map currentUserMap = SessUtil.getSessInfo().getUser();
			List<Map<String, String>> listR = DAOLightpdfSignIntegrate.listRule(projectId, "1");
			String esignUrl = listR.get(0).get(DAOLightpdfSignIntegrate.esignUrl);
			for (Object dataObj : list1) {
				Map dataMap = (Map) dataObj;
				Long instanceId = (Long) dataMap.get("id");
				String instanceName = (String) dataMap.get("subject");
				int signerCount = daoDataMng.count("esign_signer", "obj.esign_instance_id=" + instanceId);
				int signerSignedCount = daoDataMng.count("esign_signer", "obj.esign_instance_id=" + instanceId + " and obj.status='2'");



				Map esignInstanceMap = daoDataMng.getRecord("esign_instance", instanceId);
				String signFlowId = (String) esignInstanceMap.get("sign_flow_id");
				String language = (String) esignInstanceMap.get("language");
				Log.info("language："+language);
				String signProgHtml="<a href=\"javascript:void(0);\" onclick=\"window.top.webmask_show('"+instanceName+"','/tableapp.list.do?tableid=esign_signer&refinfo=esign_instance_id__"+instanceId+"&simplelist=true&readonly=true',800,600);return false;\">"+signerSignedCount+"/"+signerCount+"</a>";

				List<Map> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + instanceId +" and obj.user_code='"+(StringUtils.isNotEmpty((String) currentUserMap.get("email"))?(String)currentUserMap.get("email"):(String) currentUserMap.get("loginid")) +"'", null, 1000);
//				List<Map> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + instanceId +" and obj.user_code='"+(String) currentUserMap.get("loginid")+"'", null, 1000);

				String gotoSignHtml="";

				String instancStatus = (String) esignInstanceMap.get("status");

				if (CollectionUtils.isNotEmpty(esignSignerList)) {


					Map esignSignerMap = esignSignerList.get(0);

					String status = (String) esignSignerMap.get("status");


					if (StringUtils.equals(status,"0") && StringUtils.equals(instancStatus,"1")){
						String redirectUrl = esignUrl + (StringUtils.equals(language,"en")?"/en":"") +"?task_id=" + signFlowId + "&email=" + new String(Base64.getEncoder().encode( String.valueOf(esignSignerMap.get("user_code")).getBytes("UTF-8"))) + "&verification_code=" + esignSignerMap.get("verification_code");
						gotoSignHtml = "&nbsp;&nbsp;<a target=\"_blank\" href=\""+redirectUrl+"\">去签署</a>";

					}

				}

				dataMap.put("cdtms_signprog",signProgHtml+gotoSignHtml);
			}
		} catch (Exception e) {
			Log.error("",e);
		}
	}

	@Override
	public String getFieldid() {
		return null;
	}

	@Override
	public Integer getIndex() {
		return null;
	}
}
