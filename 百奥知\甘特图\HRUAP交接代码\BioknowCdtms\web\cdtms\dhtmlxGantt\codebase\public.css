:root {
    --text-primary-color: #242526;
    --text-regular-color: #494A4D;
    --text-secondary-color: #797B80;
    --text-placeholder-color: #A1A7B3;
    --border-base-color: #DBDEE6;
    --task-project-color: #C0C4CC;
    --task-base-color: #C0C4CC;
    --task-doing-color: #80AAFF;
    --task-future-color: #CCD4FF;
    --task-completed-color: #73E6AD;
    --task-overdue-color: #FF9999;
    --task-milestone-color: #FF4000;
    --task-milestone-light-color: #FFDDCC;
    --gantt-link-color: #A1A7B3;
    --background-base-color: #EFF0F2;
    --background-dark-color: #A1A7B3;
    --theme-primary-color: #3377FF;
    --theme-primary-hover-color: #E6EEFF;
    --space-base: 4px;
    --space-mini: 8px;
    --space-small: 12px;
    --space-medium: 16px;
    --space-large: 24px;
    --border-radius: 2px;
    --font-size: 14px;
    --font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

* {
    margin: 0;
    padding: 0;
}

body, html {
    width: 100%;
    height: 100%;
}

body {
    color: var(--text-primary-color);
    font-size: var(--font-size);
    font-family: var(--font-family);
}

.flatpickr-input{
    cursor: pointer;
    height: 30px;
    width: 250px;
}



.gantt_slider {
    width: 530px;
    height: 20px;
    margin-left: 10px;
    display: inline-block;
}

.gantt_slider input {
    width: 34px;
    height: 18px;
    border: none;

}

.gantt_slider div:first-child, .gantt_slider .gantt_slider_value {
    display: inline-block;
    vertical-align: middle;
    line-height: 13px;
}

.gantt_slider .gantt_slider_value {
    font-size: 15px;
    color: black;
    margin: 5px 10px;

}



.gantt_cal_chosen,
.gantt_cal_chosen select{
    width: 546px;
}



.dhx_calendar_cont input {
    width: 96px;
    padding: 0;
    margin: 3px 10px 10px 10px;
    font-size: 11px;
    height: 17px;
    text-align: center;
    border: 1px solid #ccc;
    color: #646464;
}

.dhtmlxcalendar_dhx_skyblue, .dhtmlxcalendar_dhx_web, .dhtmlxcalendar_dhx_terrace {
    z-index: 999999 !important;
}

.baseline {
    position: absolute;
    z-index:0;
    background: var(--task-future-color);
    min-width: 2px;
}

.baseline-milestone {
    border-radius: 50%;
    transform: translateX(-50%);
}

/*.gantt_task_progress {*/
/*    text-align: left;*/
/*    padding-left: 10px;*/
/*    box-sizing: border-box;*/
/*    color: white;*/
/*    font-weight: bold;*/
/*}*/


#templateStoreModal,#initializeModal {
    position: fixed;
    z-index: 10;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: none;
}


.loader {
    position: relative;
    width: 2.5em;
    height: 2.5em;
    transform: rotate(165deg);
}
.loader:before, .loader:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    display: block;
    width: 0.5em;
    height: 0.5em;
    border-radius: 0.25em;
    transform: translate(-50%, -50%);
}
.loader:before {
    animation: before 2s infinite;
}
.loader:after {
    animation: after 2s infinite;
}

@keyframes before {
    0% {
        width: 0.5em;
        box-shadow: 1em -0.5em rgba(225, 20, 98, 0.75), -1em 0.5em rgba(111, 202, 220, 0.75);
    }
    35% {
        width: 2.5em;
        box-shadow: 0 -0.5em rgba(225, 20, 98, 0.75), 0 0.5em rgba(111, 202, 220, 0.75);
    }
    70% {
        width: 0.5em;
        box-shadow: -1em -0.5em rgba(225, 20, 98, 0.75), 1em 0.5em rgba(111, 202, 220, 0.75);
    }
    100% {
        box-shadow: 1em -0.5em rgba(225, 20, 98, 0.75), -1em 0.5em rgba(111, 202, 220, 0.75);
    }
}
@keyframes after {
    0% {
        height: 0.5em;
        box-shadow: 0.5em 1em rgba(61, 184, 143, 0.75), -0.5em -1em rgba(233, 169, 32, 0.75);
    }
    35% {
        height: 2.5em;
        box-shadow: 0.5em 0 rgba(61, 184, 143, 0.75), -0.5em 0 rgba(233, 169, 32, 0.75);
    }
    70% {
        height: 0.5em;
        box-shadow: 0.5em -1em rgba(61, 184, 143, 0.75), -0.5em 1em rgba(233, 169, 32, 0.75);
    }
    100% {
        box-shadow: 0.5em 1em rgba(61, 184, 143, 0.75), -0.5em -1em rgba(233, 169, 32, 0.75);
    }
}

.loader {
    position: absolute;
    top: calc(50% - 1.25em);
    left: calc(50% - 1.25em);
    z-index: 999;
}


.gantt_clone {
    width: 100%;
    height: 100%;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAAEgBckRAAAAAXNSR0IArs4c6QAAAHhlWElmTU0AKgAAAAgABAEaAAUAAAABAAAAPgEbAAUAAAABAAAARgEoAAMAAAABAAIAAIdpAAQAAAABAAAATgAAAAAAAACQAAAAAQAAAJAAAAABAAOgAQADAAAAAQABAACgAgAEAAAAAQAAADCgAwAEAAAAAQAAADAAAAAACbrUrQAAAAlwSFlzAAAWJQAAFiUBSVIk8AAAAjRJREFUaAXtWVFugzAMhWrHaHeRse+plbbDlmnbAbaDbOMeLAQIsXEcE5IVpFSqiBPb7/k5AdQWhfdT1a3xMWMzUEvdWH0PxgsNnAuldrRTdROfz/08ypLeHIjMWQ2M5rQw9Yljo8o43Rnb7djXO6xPAV0kJQRK5JTWIKOBPIBCR8nimFDWh+u5KMs3MjVJCSliAq35XtbH16eibT+0g7WobZS5D+ic0QIZrCblsuoM2whAIsDNN/AEFyTGFIAygaBNG3DvYaqhZVk6TRrh5LZtBdjTszFBCAIQDjqJa35EYAhAAEHA6CICVk6Lzw4AEBj7B6B7ICgduDCbIA7Agl3UKGZH/eYAKGYjoQK+m92v7skSAm17Kb5e3scQ30E7jo7iK3oTkG1TZhsCYOI8+CoA8SEGrKCqf1SS0ywRwQz4MBXiCubJQablBqxgjGcYjS766qtMOeEKQHwMIwN4VcwSeSWiz4E3DDkw5yFFDxobPk4FzMlPUYFdQEFXwGgKogVGjAq6x2r+ZAWSKcC/13GwVb38nY/Lx6/p38QolzWnbPk7JcVANufEou9DsqTQi7mbQkehJbwXrumAkElaN3cHqvpbQd+L4YWKOfMFdpDrgJy8k1X6BXcHMHagQjiNsdd2bEjEdcBgbXmQC7h1d3IHcgdWKrD7LSR/DqxUyhse+FzYQwca9U/mxSXAdjoQ+KTfQwdc4ut5eQcC9yiLHmHR3YF+3/3HbxoK43COUEtOcRMF/gCRsn/bS20ByQAAAABJRU5ErkJggg==);
    background-position: 50%;
    background-repeat: no-repeat;
    cursor: pointer;
    position: relative;
    -moz-opacity: 1;
    opacity: 1;
    background-size: 15px;
}

.main-container {
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;

    height: 100%;
    min-height: 141px;
    min-width: 124px;

    background-color: #fff;

    z-index: 4;
    overflow: hidden;
}

.gantt-legends {
    padding: var(--space-small);
    line-height: 1;
    text-align: right;
}
.gantt-legends_item {
    list-style: none;
    display: inline-block;
    margin-left: 24px;
}
.gantt-legends_item::before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 6px;
    margin-right: 6px;
    position: relative;
    top: 1px;
}
.gantt-legends_item[name=future]::before {
    background-color: var(--task-future-color);
}
.gantt-legends_item[name=doing]::before {
    background-color: var(--task-doing-color);
}
.gantt-legends_item[name=completed]::before {
    background-color: var(--task-completed-color);
}
.gantt-legends_item[name=overdue]::before {
    background-color: var(--task-overdue-color);
}
.gantt-legends_item[name=milestone]::before {
    background-image: url(/cdtms/dhtmlxGantt/codebase/images/milestone.png);
    background-size: 100% 100%;
    width: 14px;
    height: 14px;
}

.main-content {
    flex: auto;
    position: relative;
    background-color: #fff;
    max-height: 100%;
    overflow: hidden;
}

#gantt_here {
    width: 100%;
    height: 100%;

    background-color: #fff;

    overflow: auto;
}

.status_line {
    background-color: #0ca30a;
}

.gantt_grid_wbs {
    position: absolute;
}

.gantt_grid_scale {
    position: relative;
    z-index: 1;
}

.dnd_highlighter {
    position: absolute;
    height: 4px;
    width: 500px;
    background-color: #3498db;
}

.dnd_highlighter::before {
    background: transparent;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
    border: 3px solid #3498db;
    border-radius: 6px;
    content: "";
    line-height: 1px;
    display: block;
    position: absolute;
    margin-left: -11px;
    margin-top: -4px;
}

.gantt_drag_marker {
    opacity: 0.6;
}

.gantt_drag_marker.gantt_grid_resize_area {
    z-index: 1;
}

.gantt_parent_row {
    font-weight: bold;
}

.gantt_task_line div.gantt_side_content {
    bottom: 0;
}

.gantt-top-panel {
    position: relative;
    color: #fff;
    padding: 11px 16px;
    background: #3d3d3d;
}

.gantt-top-panel_btn {
    display: inline-block;
    color: #fff;
    padding: 7px 24px;
    text-decoration: none;
    border-radius: 20px;
    background: var(--theme-primary-color);

    position: absolute;
    right: 8px;
    top: 50%;
    margin-top: -16px;
}

.gantt-top-panel_btn:hover {
    background: #03a9f4;
}

.gantt-top-panel_btn:focus {
    outline: none;
}

.gantt-top-panel_btn:active {
    transform: translateY(1px);
    -webkit-transform: translateY(1px);
}

.status-control {
    font-size: 0;
}

.status-control .status {
    position: relative;
    width: 28px;
    height: 14px;
    margin-left: var(--space-mini);
    transition: all 0.4s ease;
    border-radius: 7px;
    background-color: var(--background-dark-color);
}

.status-control.checked .status {
    background-color: var(--theme-primary-color);
}

.dhx_checkbox_grip {
    position: absolute;
    top: 1px;
    left: 1px;
    width: 12px;
    height: 12px;
    transition: all 0.2s ease;
    border-radius: 7px;
    background-color: #fff;
    box-shadow: 0 3px 9px 0 rgba(0, 0, 0, 0.2);
}

.status-control.checked .dhx_checkbox_grip {
    left: 15px;
}

.dhx_checkbox_title {
    font-size: 14px;
}

.button-with-icon.active {
    background-color: #e5e5e5;
}

.disabled {
    opacity: 0.5;
}

.dhx_checkbox_title,
.status-control .status,
.dhx_checkbox {
    display: inline-block;
    vertical-align: middle;
}

.dhx_checkbox {
    font-size: var(--font-size);
    cursor: pointer;
    user-select: none;
    padding: 0 var(--space-base);
}

.dhx_checkbox_group {
    position: relative;
    white-space: nowrap;
    margin-right: var(--space-large);
}

.title {
    font-size: 16px;
}

.actions-container{
    position: relative;
    font-size: 0;
    line-height: 0;
    padding: 0 var(--space-small);
    user-select: none;
    /* border-bottom: 1px solid var(--border-base-color); */
}

.actions{
    position: relative;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-justify-content: space-between;
    justify-content: space-between;
}

.actions_row {
    height: 44px;
    flex: none;
    display: flex;
    align-items: center;
}

.actions_col{
    display: inline-block;
    vertical-align: middle;
    margin-left: var(--space-small);
}

.btn {
    vertical-align: middle;
    text-align: center;
    min-width: 50px;
    outline: none;
    border: 1px solid var(--border-base-color);
    border-radius: var(--border-radius);
    padding: 1px var(--space-mini) 0;
    line-height: 25px;
    text-align: center;
    text-transform: none;
    text-decoration: none;
    cursor: pointer;
    user-select: none;
    white-space: nowrap;
    background: #FFF;
}

.btn img {
    width: var(--font-size);
    height: var(--font-size);
    font-size: 0;
    line-height: 0;
    position: relative;
    top: 2px;
    margin-right: 2px;
}

.btn:disabled {
    cursor: not-allowed;
    color: inherit;
    opacity: .5;
}

.btn--text {
    border-color: transparent;
    padding-left: var(--space-base);
    padding-right: var(--space-base);
}

.btn + .btn {
    margin-left: var(--space-small);
}

.btn:not(:disabled):hover, .btn:not(:disabled):active{
    background-color: var(--background-base-color);
}

.btn.round-btn {
    border-radius: 28px;
}

.btn.plain-btn {
    border-color: var(--border-base-color);
    color: var(--text-primary-color);
}
.btn.plain-btn:not(.disabled):hover, .btn.plain-btn:not(.disabled):active{
    background-color: var(--background-base-color);
}

.scale-combo,.tasks-grouping{
    display: block;
    min-width: 60px;
    line-height: 26px;
    padding: 0 var(--space-small);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-base-color);
    background: url('/cdtms/dhtmlxGantt/codebase/images/arrow-down.png') calc(100% - 12px) / 10px no-repeat transparent;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
    outline: none;
}

.scale-combo:active,
.scale-combo:focus,
.tasks-grouping:active,
.tasks-grouping:focus
{
    border-color: var(--theme-primary-color);

    outline: none;
}

.icon-btn{
    display: block;

    color: #5f5f5f;
    font: 400 14px/32px "Roboto", Arial, sans-serif;
    text-align: center;
    text-decoration: none;

    padding: 0;

    cursor: pointer;
}

.icon-btn.disabled{
    opacity: 0.6;

    pointer-events: none;
}

.icon-btn img{
    position: relative;
    top: -1px;

    display: inline-block;
    vertical-align: middle;

    width: 14px;
    margin-right: 4px;
}

@media screen and (max-width: 1280px){
    .actions{
        padding: 4px 130px 10px 24px;
    }
}

@media screen and (max-width: 1150px){
    .actions{
        padding: 4px 120px 10px 15px;
    }

    .actions_col{
        margin-right: 10px;
    }
}

.owner-label{
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    display: inline-block;
    border: 1px solid #cccccc;
    border-radius: 25px;
    background: #e6e6e6;
    color: #6f6f6f;
    margin: 0 3px;
    font-weight: bold;
}

.fragment-mark {
    width: 12px;
    height: 12px;
    position: absolute;
    transform: translate(-6px, -6px);
    z-index: 10000;
    background-size: 100% 100%;
    border-radius: 6px;
}

.fragment-mark--1 {
    background-color: #FFBB99;
}
.fragment-mark--2 {
    background-color: #FF9966;
}
.fragment-mark--3 {
    background-color: #FF7733;
}
.fragment-mark--4 {
    background-color: #FF5500;
}
.fragment-mark--5 {
    background-color: #B33B00;

}

.multiple-task {
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-color: var(--task-completed-color);
}
