<package>
	<handler classname="net.bioknow.cdtms.schedule.WFInjectionHandlereUpdateSchedule"/>
	<handler classname="net.bioknow.cdtms.schedule.EHRFUI"/>
	<handler classname="net.bioknow.cdtms.schedule.EHDTAfterSave"/>
	<handler classname="net.bioknow.cdtms.schedule.EHDTAfterDelete"/>
	<handler classname="net.bioknow.cdtms.schedule.EHLangExtract"/>

<!--	<action name="/dhtmlxGantt" classname="net.bioknow.cdtms.schedule.ActionGanttIntegrate">-->
<!--		<goto name="show" path="/cdtms/dhtmlxGantt/Gantt.jsp"/>-->
<!--	</action>-->

	<action name="/scheduleGantt" classname="net.bioknow.cdtms.schedule.ActionGanttIntegrate">
		<goto name="show" path="/cdtms/dhtmlxGantt/gantt.jsp"/>
		<goto name="show2" path="/cdtms/dhtmlxGantt/schedulegantt.jsp"/>
	</action>
	<action name="/scheduleGanttjson" classname="net.bioknow.cdtms.schedule.ActionGanttIntegrateJson">
	</action>

<!--	<action name="/gantt" classname="net.bioknow.cdtms.schedule.ActionGanttIntegrate">-->
<!--		<goto name="show" path="/cdtms/dhtmlxGantt/gantt.jsp"/>-->
<!--		<goto name="show2" path="/cdtms/dhtmlxGantt/schedulegantt.jsp"/>-->
<!--	</action>-->

	<action name="/scheduler" classname="net.bioknow.cdtms.schedule.ActionSchedulerIntegrate">
		<goto name="show" path="/cdtms/dhtmlxScheduler/scheduler.jsp"/>
	</action>
	<resource folder="/i18n" file="zh,en"/>
</package>

