package net.bioknow.cdtms.schedule;


import net.bioknow.uap.entitymng.EntityUtil;
import net.bioknow.webutil.langutil.FaceLangExtract;
import net.bioknow.webutil.langutil.LangBean;
import net.bioknow.webutil.tools.Log;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class EHLangExtract implements FaceLangExtract {
    @Override
    public List<LangBean> extractLangBean(String projectId, String localeStr) {
        try {
            List<LangBean> list = new ArrayList<LangBean>();

            List<EntityScheduleTemplate> listEntity = EntityUtil.listEntity(projectId, "", "", EntityScheduleTemplate.class);
            if(listEntity.size()==0) return list;

            Set setCache = new HashSet();
            Set setStageCache = new HashSet();
            for(EntityScheduleTemplate tem : listEntity){
                String name = tem.schedulename;
                if(setCache.contains(name)) {
                    continue;
                }else {
                    setCache.add(name);
                }
                LangBean bean = new LangBean();
                bean.setCatagory("schedule");
                bean.setLocalestr(localeStr);
                bean.setTerm(name);
                bean.setId("schedule:name:"+name);
                bean.setNote("甘特图 ");
                list.add(bean);
                if(setStageCache.contains(tem.schedulestage)){
                    continue;
                }else{
                    setStageCache.add(tem.schedulestage);
                    LangBean bean2 = new LangBean();
                    bean2.setCatagory("schedule");
                    bean2.setLocalestr(localeStr);
                    bean2.setTerm(tem.schedulestage);
                    bean2.setId("schedule:name:"+tem.schedulestage);
                    bean2.setNote("甘特图 ");
                    list.add(bean2);
                }
            }
            return list;
        } catch (Exception e) {
            Log.error("",e);
        }
        return null;
    }
}
