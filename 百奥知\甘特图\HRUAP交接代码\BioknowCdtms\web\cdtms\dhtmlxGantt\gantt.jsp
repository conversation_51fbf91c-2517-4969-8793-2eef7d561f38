﻿<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=utf-8" %>
<%@ page language="java" pageEncoding="utf-8" %>
<%@ taglib uri="/webutil/tlib/bioknow-tlib.tld" prefix="bt" %>

<html>
<head>
    <title>进度管理</title>
    <link rel='stylesheet' href='/cdtms/dhtmlxGantt/dhtmlx/dhtmlx.css?v=8.0.1'/>
    <link href="/cdtms/dhtmlxGantt/codebase/skins/dhtmlxgantt_material.css" rel="stylesheet">
    <link href="/cdtms/dhtmlxGantt/codebase/public.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/cdtms/dhtmlxGantt/chosen/chosen.css?v=8.0.1">
    <link href="/cdtms/dhtmlxGantt/codebase/dhtmlxgantt_rewrite.css" rel="stylesheet">

    <script src='/cdtms/dhtmlxGantt/dhtmlx/dhtmlx.js?v=8.0.1'></script>
    <script src="/cdtms/dhtmlxGantt/codebase/dhtmlxgantt.js"></script>
    <script type="text/javascript" src="/public/js/jquery-3.2.1.min.js"></script>
    <script src="/cdtms/dhtmlxGantt/chosen/chosen.jquery.js?v=8.0.1"></script>
    <script src="/webutil/js/winopen.js" type="text/javascript"></script>

    <script>
        var studyId = '${studyId}';
        var currRole = '${currRoleName}';
        var transitions = {
            taskName: '<bt:lang name="任务名称"/>',
            executor: '<bt:lang name="执行人"/>',
            planstart: '<bt:lang name="计划开始日期"/>',
            duration: '<bt:lang name="计划时长"/>',
            startdate: '<bt:lang name="实际开始日期"/>',
            enddate: '<bt:lang name="实际结束日期"/>',
            copy: '<bt:lang name="复制"/>',
            year: '<bt:lang name="%Y年"/>',
            yearmonth: '<bt:lang name="%Y年%M月"/>',
        };

        var local = '${local}';
        try {
            parent.addwidth();
        } catch (error) {
            //parent.clickHideLeft();
        }
    </script>
</head>
<body>

<div id="templateStoreModal" style="display: none;">
    <form id="templateStoreForm">
        <div
            class="gantt_cal_light gantt_cal_light_wide"
            role="dialog" 
            style="
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                visibility: visible;
                height: 280px;
                /*margin: 250px auto;*/
            "
        >
            <div class="gantt_cal_ltitle" role="heading" style="cursor: pointer;text-align: center">
                <span class="gantt_title">存储为任务模板</span>
            </div>

            <div class="gantt_cal_larea" style="height: 210px;">
                <div class="gantt_wrap_section">
                    <div class="gantt_cal_lsection">
                        <label>名称</label>
                    </div>

                    <div class="gantt_cal_ltext" style="height:36px;">
                        <textarea id="templateName" required></textarea>
                    </div>
                </div>

                <div class="gantt_wrap_section">
                    <div class="gantt_cal_lsection">
                        <label>说明</label>
                    </div>

                    <div class="gantt_cal_ltext" style="height:80px;">
                        <textarea id="templateDesc"></textarea>
                    </div>
                </div>
            </div>

            <div
                id="templateStoreSave"
                role="button"
                aria-label="保存"
                tabindex="0"
                class="gantt_btn_set gantt_left_btn_set gantt_save_btn_set"
                style="float:right;"
            >
                <div>保存</div>
            </div>

            <div
                id="templateStoreCancel"
                role="button"
                aria-label="关闭"
                tabindex="0"
                class="gantt_btn_set gantt_left_btn_set gantt_cancel_btn_set"
                style="float:right;"
            >
                <div>关闭</div>
            </div>
        </div>
    </form>
</div>

<div id="loader" class="loader"></div>

<div id="initializeModal" style="display: none;">
    <div
        class="gantt_cal_light gantt_cal_light_wide"
        role="dialog"
        style="
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            visibility: visible;
            height: 280px;
            /*margin: 250px auto;*/
        "
    >
        <div class="gantt_cal_ltitle" role="heading" style="cursor: pointer;text-align:left">
            <span class="gantt_title">项目计划初始化</span>
        </div>

        <div class="gantt_cal_larea" style="height: 210px;">
            <div class="gantt_wrap_section">
                <div class="gantt_cal_lsection">
                    <label>项目</label>
                </div>

                <div class="gantt_cal_ltext" style="height: 23px;">
                    <select style="width:100%;" id="template"></select>
                </div>
            </div>

            <div class="gantt_wrap_section">
                <div class="gantt_cal_lsection">
                    <label>开始时间</label>
                </div>

                <div class="gantt_cal_ltext" style="height:80px;">
                    <input type="date" id="study_strat_date"/>
                </div>
            </div>
        </div>

        <div
            id="initializeSave"
            role="button"
            aria-label="确认"
            tabindex="0"
            class="gantt_btn_set gantt_left_btn_set gantt_save_btn_set"
            style="float:right;"
        >
            <div><bt:lang name="确认"/></div>
        </div>

        <div
            id="initializeCancel"
            role="button"
            aria-label="跳过"
            tabindex="0"
            class="gantt_btn_set gantt_left_btn_set gantt_cancel_btn_set"
            style="float:right;"
        >
            <div><bt:lang name="跳过"/></div>
        </div>
    </div>
</div>

<div class="main-container">
    <div class="actions-container">
        <div class="actions">
            <div class="actions_row">
                <h1 class="title"><bt:lang name="项目进度"/></h1>
            </div>

            <div class="actions_row">
                <c:if test="${not empty param.studyId}">
                    <div class="actions_col">
                        <button class="btn btn--text" data-action="undo" disabled>
                            <img src="/cdtms/dhtmlxGantt/codebase/images/undo.png"/>
                            <bt:lang name="撤销"/>
                        </button>
                    </div>
                </c:if>

                <c:if test="${not empty param.studyId}">
                    <div class="actions_col">
                        <button class="btn btn--text" data-action="redo" disabled>
                            <img src="/cdtms/dhtmlxGantt/codebase/images/redo.png"/>
                            <bt:lang name="恢复"/>
                        </button>
                    </div>
                </c:if>

                <div class="actions_col">
                    <button class="btn btn--text" data-action="reset" onclick="resetSchedule();">
                        <img src="/cdtms/dhtmlxGantt/codebase/images/reset-data.png"/>
                        <bt:lang name="重置数据"/>
                    </button>
                </div>
                
                <div class="actions_col">
                    <button class="btn btn--text" data-action="fullscreen">
                        <img src="/cdtms/dhtmlxGantt/codebase/images/open.png"/>
                        <bt:lang name="全屏"/>
                    </button>
                </div>

                <div class="actions_col">
                    <div class="dhx_checkbox">
                        <div id="simple-table" class="status-control">
                            <div class="dhx_checkbox_title"><bt:lang name="简化表格"/></div>

                            <div class="status">
                                <div class="dhx_checkbox_grip"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <%-- <c:if test="${not empty param.studyId}">
                    <div class="actions_col">
                        <div class="dhx_checkbox">
                            <div id="auto-scheduling" class="status-control">
                                <div class="dhx_checkbox_title">自动计划</div>

                                <div class="status">
                                    <div class="dhx_checkbox_grip"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </c:if> --%>
                <c:if test="${not empty param.studyId}">
                    <div class="actions_col">
                        <div class="dhx_checkbox">
                            <div id="critical-path" class="status-control">
                                <div class="dhx_checkbox_title"><bt:lang name="关键路径"/></div>

                                <div class="status">
                                    <div class="dhx_checkbox_grip"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </c:if>

                <div class="actions_col">
                    <div class="dhx_checkbox">
                        <div id="baseline" class="status-control">
                            <div class="dhx_checkbox_title"><bt:lang name="基线"/></div>

                            <div class="status">
                                <div class="dhx_checkbox_grip"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="actions_col">
                    <div class="dhx_checkbox">
                        <div id="zoom-to-fit" class="status-control">
                            <div class="dhx_checkbox_title"><bt:lang name="适应窗口"/></div>

                            <div class="status">
                                <div class="dhx_checkbox_grip"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="actions_col">
                    <div class="dhx_checkbox">
                        <div id="collapse" class="status-control">
                            <div class="dhx_checkbox_title"><bt:lang name="折叠"/></div>
                            
                            <div class="status">
                                <div class="dhx_checkbox_grip"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="actions_col">
                    <button class="btn" data-action="toStartTime"><bt:lang name="起始"/></button>
                </div>
                <div class="actions_col">
                    <button class="btn" data-action="today"><bt:lang name="今天"/></button>
                </div>

                <div class="actions_col">
                    <div class="scale-container">
                        <select class="scale-combo" id="scale_combo" placeholder="">
                            <option value="years"><bt:lang name="年"/></option>
                            <option value="quarters" selected><bt:lang name="季"/></option>
                            <option value="months"><bt:lang name="月"/></option>
                            <option value="weeks"><bt:lang name="周"/></option>
                            <option value="days"><bt:lang name="天"/></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div id="gantt_here"></div>
    </div>

    <ul class="gantt-legends">
        <li class="gantt-legends_item" name="future"><bt:lang name="计划"/></li>
        <li class="gantt-legends_item" name="doing"><bt:lang name="进行中"/></li>
        <li class="gantt-legends_item" name="completed"><bt:lang name="已完成"/></li>
        <%-- <li class="gantt-legends_item" name="overdue">延期</li> --%>
        <li class="gantt-legends_item" name="milestone"><bt:lang name="里程碑"/></li>
    </ul>
</div>

<script src="/cdtms/dhtmlxGantt/codebase/public.js"></script>
<script type="text/javascript">

    function saveConfirm() {
        $.ajax({
            url:"/scheduleGantt.reset.do?studyid="+studyId,
            type:"GET",
            success:function(data){
                if(data=="ok"){
                    //window.location.href="/scheduleGantt.initSchedulePage.do?studyId=" + studyId;
                    window.location.href="/uapvue/index.html#/cdtms_project_progress?studyId=" + studyId;

                }else{
                    alert(data);
                }
            }
        });
    }

    function cancel() {
        return false;
    }

    function resetSchedule(){
        var frmids = getframeid();
        var prop = {
            frmids: frmids,
            msg : "确定重置？",
            btns: [
                {label: '确定', funcName: 'saveConfirm', bgColor: 'default'},
                {label: '取消', funcName: 'cancel',  bgColor: 'dedault'}
            ]
        }
        window.top.webmask_show_confirm(prop);

    }
</script>

</body>

</html>
