<component name="ArtifactManager">
  <artifact type="exploded-war" name="Test:war exploded">
    <output-path>$PROJECT_DIR$/web</output-path>
    <root id="root">
      <element id="directory" name="WEB-INF">
        <element id="directory" name="classes">
          <element id="module-output" name="Test" />
        </element>
        <element id="directory" name="lib">
          <element id="library" level="project" name="BioknowLib" />
        </element>
      </element>
      <element id="javaee-facet-resources" facet="Test/web/Web" />
    </root>
  </artifact>
</component>