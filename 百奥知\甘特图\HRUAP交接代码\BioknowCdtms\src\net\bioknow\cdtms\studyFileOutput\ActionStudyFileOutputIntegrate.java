package net.bioknow.cdtms.studyFileOutput;



import net.bioknow.mvc.RootAction;
import net.bioknow.mvc.tools.WebPath;
import net.bioknow.passport.datamng.DAOPPData;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.schemaplug.attach.AttachDAO;
import net.bioknow.webutil.fileup.DAOFileup;
import net.bioknow.webutil.tools.FileUtil;
import net.bioknow.webutil.tools.Log;
import net.bioknow.webutil.tools.URLUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;
import java.util.regex.Pattern;


public class ActionStudyFileOutputIntegrate extends RootAction {


    public void download(HttpServletRequest request, HttpServletResponse response) {
        try {

            String user = request.getParameter("user");
            String pwd = request.getParameter("pwd");
            String projectId = "cdtmsen_val";
            String studyCode = request.getParameter("study");
            String type = request.getParameter("type");
            DAOPPData udao = new DAOPPData(projectId);
            if (!udao.pwdValid(user, pwd)) {
                response.getOutputStream().write("ERR".getBytes());
                return;
            }
            if (StringUtils.isEmpty(studyCode)) {
                response.getOutputStream().write("studyCode is Null".getBytes());
                return;

            }


            DAODataMng daoDataMng = new DAODataMng(projectId);


            List<Map> StudyList = daoDataMng.listRecord("xsht", "obj.studyid='" + studyCode + "'", null, 1);

            if (CollectionUtils.isEmpty(StudyList)) {
                response.getOutputStream().write("Study Not Found".getBytes());
                return;
            }

            Map StudyMap = StudyList.get(0);
            Long StudyId = (Long) StudyMap.get("id");

            String filePath2 = null;
            String tableId =null;
            String filedId = null;
            AttachDAO attachDAO = new AttachDAO(projectId);

            if (StringUtils.isEmpty(type) || StringUtils.equals(type, "als")) {
                tableId = "prod_report";
                filedId = "xmsjkdybg";

                List<Map> ProductionUseList = daoDataMng.listRecord(tableId, "obj.studyid=" + StudyId + " and obj.createtime>= TO_DATE('2023-12-01', 'YYYY-MM-DD')", "obj.report_date desc nulls last,obj.ecverdt desc nulls last", 1);

                if (CollectionUtils.isEmpty(ProductionUseList)) {
                    response.getOutputStream().write("Production Not Found Where Createtime >= 2023-12-01".getBytes());
                    return;
                }

                Map ProductionUseMap = ProductionUseList.get(0);

                String EDCProductionReportStr = (String) ProductionUseMap.get(filedId);

                if (StringUtils.isEmpty(EDCProductionReportStr)) {
                    response.getOutputStream().write("Production File Not Found".getBytes());
                    return;

                }

                File DCProductionReportFile = attachDAO.getFiles(EDCProductionReportStr, tableId)[0];
                String fileName = DCProductionReportFile.getName();
                int index = fileName.lastIndexOf(".");
                String extension = fileName.substring(index + 1);


                String filePath = WebPath.getRootPath() + DAOFileup.tempfolder + "/" + FilePattern.matcher(studyCode).replaceAll("_") + "_als." + extension;


                filePath2 = DAOFileup.tempfolder + "/" + URLUtil.urlEncodeUTF8(FilePattern.matcher(studyCode).replaceAll("_")) + "_als." + extension;
                File targetFile = new File(filePath);

                FileUtils.copyFile(DCProductionReportFile, targetFile);
            }

            if (StringUtils.equals(type, "dmrp")) {

                tableId = "edit_check_plan";
                filedId = "dvp_doc";
                List<Map> ProductionUseList = daoDataMng.listRecord(tableId, "obj.studyid=" + StudyId, "obj.dvp_v_date desc nulls last", 1);

                if (CollectionUtils.isEmpty(ProductionUseList)) {
                    response.getOutputStream().write("DMRP Not Found".getBytes());
                    return;
                }

                Map ProductionUseMap = ProductionUseList.get(0);

                String EDCProductionReportStr = (String) ProductionUseMap.get(filedId);

                if (StringUtils.isEmpty(EDCProductionReportStr)) {
                    response.getOutputStream().write("DMRP File Not Found".getBytes());
                    return;

                }

                File DCProductionReportFile = attachDAO.getFiles(EDCProductionReportStr, tableId)[0];
                String fileName = DCProductionReportFile.getName();
                int index = fileName.lastIndexOf(".");
                String extension = fileName.substring(index + 1);


                String filePath = WebPath.getRootPath() + DAOFileup.tempfolder + "/" + FilePattern.matcher(studyCode).replaceAll("_") + "_dmrp." + extension;


                filePath2 = DAOFileup.tempfolder + "/" + URLUtil.urlEncodeUTF8(FilePattern.matcher(studyCode).replaceAll("_")) + "_dmrp." + extension;
                File targetFile = new File(filePath);

                FileUtils.copyFile(DCProductionReportFile, targetFile);
            }

            this.redirectByUrl(request, response, filePath2);
        }  catch (Exception e) {
            Log.error("", e);
        }

    }

    private static Pattern FilePattern = Pattern.compile("[\\r\\n\\\\/:*?\"<>|]");


}