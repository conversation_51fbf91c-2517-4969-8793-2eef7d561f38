package net.bioknow.cdtms.extdatabind;

import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTRecordFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


public class DTRFexdatabindPreview extends DTRecordFuncAction {


	public boolean canUse(int auth, String tableid, Long recordid) {

		try {

			if (StringUtils.equals(tableid,"ext_data_bind")) {
				return true;
			}




		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}

	public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBean fpb) {
		try {


			String projectid = SessUtil.getSessInfo().getProjectid();
			DAODataMng daoDataMng = new DAODataMng(projectid);

			Map extDataBindMap = daoDataMng.getRecord("ext_data_bind",Long.valueOf( fpb.getRecordid()));
			String preview_url = (String) extDataBindMap.get("preview_url");



			response.sendRedirect(preview_url);

			return;

//			this.forwardByUri(request,response,"/extdatabind.qcajaxmenu.do?id="+fpb.getRecordid()+"&tableid="+fpb.getTableid()+"&refinfo="+fpb.getRefinfo());





		} catch (Exception e) {
			Log.error("",e);
		}
	}

	public FuncInfoBean getFIB(String tableid) {
		FuncInfoBean fib = new FuncInfoBean();

		fib.setName("遮盲Summary");
		fib.setType(FuncInfoBean.FUNCTYPE_OPENWINDOW);
		fib.setWinHeight(800);
		fib.setWinWidth(800);


		return fib;
	}

}
