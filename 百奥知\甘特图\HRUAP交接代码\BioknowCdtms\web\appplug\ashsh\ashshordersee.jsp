<%@ page contentType="text/html;charset=utf-8" %>
<%@ page language="java" pageEncoding="utf-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="/webutil/tlib/bioknow-tlib.tld" prefix="bt" %>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<html>
<head>
    <link rel="stylesheet" type="text/css" href="/public/bootstrap/css/bootstrap.min.css?v=1733116427423">
    <link rel="stylesheet" type="text/css" href="/public/css/public.css?v=1733116427423">
<style type="text/css">
	input{width:200px;}
	select{width:200px;}
</style>
</head>
<body style="width:100%; font-size:12px; color:#222;">
	<table class="table" style="text-align:center;">
		<tr>
			<td>物流订单号：</td><td class="WaybillId">${WaybillId}</td>
			<td>物流单编号：</td><td class="WaybillNumber">${WaybillNumber }</td>
		</tr>
		<tr>
			<td>操作时间：</td><td class="Date"></td>
			<td>订单类型：</td><td class="OperationType"></td>
		</tr>
		<tr>
			<td>操作城市：</td><td class="OperationCity"></td>
			<td>调度到货城市：</td><td class="StopCity"></td>
		</tr>
	</table>
	<div id="data" style="display: none;">${data }</div>
<script type="text/javascript" src="/public/js/jquery-3.2.1.min.js?v=1733116427423"></script>
<script type="text/javascript" src="/public/bootstrap/js/bootstrap.js?v=1733116427423"></script>
<script type="text/javascript" src="/dbplug/portalmobile/js/md5.js?v=1733116427423"></script>
<script src="/webutil/js/date/WdatePicker.js?v=1733116427423" type="text/javascript"></script>
<script type="text/javascript">
$(function(){
	var data = $("#data").html();
	var json = JSON.parse(data);
	console.log(json);
	for(var key in json[0]){
		if(key=="Date"){$(".Date").html(json[0][key]);}
		if(key=="OperationType"){$(".OperationType").html(json[0][key]);}
		if(key=="OperationCity"){$(".OperationCity").html(json[0][key]);}
		if(key=="StopCity"){$(".StopCity").html(json[0][key]);} 
	}
})
</script>
</body>
</html>
