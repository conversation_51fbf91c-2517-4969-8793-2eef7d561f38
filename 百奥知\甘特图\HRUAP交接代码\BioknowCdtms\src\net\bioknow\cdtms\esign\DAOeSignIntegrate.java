package net.bioknow.cdtms.esign;

import java.util.List;
import java.util.Map;

import net.bioknow.webplug.configdb.DAOConfigDB;

public class DAOeSignIntegrate {

    protected final static String xmlpath = "/cdtms/esign/configform.xml";

    protected final static String debug = "debug";

    protected final static String sn = "sn";
    protected final static String esignSecret = "esignSecret";
    protected final static String esignProject = "esignProject";
    protected final static String esignUrl = "esignUrl";
    protected final static String businessTypeCode = "businessTypeCode";
    protected final static String rebackUrl = "rebackUrl";
    protected final static String organizationCode = "organizationCode";


    public static List<Map<String, String>> listRule(String projectId) throws Exception {
        DAOConfigDB dao = new DAOConfigDB(projectId, DAOeSignIntegrate.xmlpath);
        return dao.list();
    }

    protected static List<Map<String, String>> listRule(String projectId, String sn_no) throws Exception {
        DAOConfigDB dao = new DAOConfigDB(projectId, DAOeSignIntegrate.xmlpath);

        return dao.listByTable(sn_no, sn);
    }

}
