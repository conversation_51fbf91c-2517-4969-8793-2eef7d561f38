package net.bioknow.cdtms.extdatabind;

import net.bioknow.dbplug.dataview3d.CNT_Dataview3d;
import net.bioknow.dbplug.dataview3d.DAODataview3d;
import net.bioknow.mvc.tools.Language;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbcore.schema.CNT_Schema;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.face.RecordViewFace;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public class RecordViewRelation implements RecordViewFace {

	private String projectId;
	private String tableid;

	private String recordid;
	private Map mapRecord;
	private Map mapR = null;

	public void show(HttpServletRequest req, HttpServletResponse res,String id) throws Exception {
		//Log.info(req.getContextPath());
		res.sendRedirect( "/extdatabind.loadBindRecord.do?id="+this.recordid+"&studyid="+mapRecord.get("studyid")+"&zq="+mapRecord.get("zq"));
	}
	
	public String getName() {
//		Language lang = Language.getInstance(this.getClass());
		return "遮盲记录";
	}

	public String getId() {
		return this.getClass().getName();
	}

	public boolean canUse() {
		try {
			if (!this.tableid.equals("outboard_data_manager")) {
				return false;
			}

			DAODataMng daoDataMng = new DAODataMng(this.projectId);

			Map userMap = SessUtil.getSessInfo().getUser();
			String userid = SessUtil.getSessInfo().getUserid();
			if (StringUtils.equals("1",userid)) {
				return true;

			}

			Long studyid = (Long) this.mapRecord.get("studyid");

			List StudyUserList = daoDataMng.listRecord("roles", "obj.studyid=" + studyid + " and obj.member=" + (String) userMap.get("id"), null, 1);
			if (CollectionUtils.isEmpty(StudyUserList)) {

				return false;

			}
			Map StudyUserMap = (Map) StudyUserList.get(0);

			String role = (String) StudyUserMap.get("limitnum");

			if (StringUtils.equals(role,"EDM")||StringUtils.equals(role,"EDM(QC)") || StringUtils.equals("1",userid)) {

				return true;

			}



		} catch (Exception e) {
			Log.error("",e);
		}
		return false;
	}
	public void setParam(String projectId, String tableid,String refinfo, Map mapV) {
		this.projectId = projectId;
		this.tableid = tableid;
		this.mapRecord = mapV;
		this.recordid = (mapV==null?"":String.valueOf(this.mapRecord.get(CNT_Schema.id)));
	}

}
