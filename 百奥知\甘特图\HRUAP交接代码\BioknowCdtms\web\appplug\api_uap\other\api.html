<!-- HTML for static distribution bundle build -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link href="/favicon.ico" rel="icon" type="image/svg+xml"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>Bioknow UAP</title>
    <link href="https://unpkg.com/swagger-ui@5.17.14/dist/swagger-ui.css" rel="stylesheet" type="text/css"/>
</head>

<body>
<div id="swagger-ui"></div>
<script charset="UTF-8" src="https://unpkg.com/swagger-ui@5.17.14/dist/swagger-ui-bundle.js"></script>
<script charset="UTF-8" src="https://unpkg.com/swagger-ui@5.17.14/dist/swagger-ui-standalone-preset.js"></script>
<script>
    const params = new URLSearchParams(window.location.search);
    const paramValue = params.get('path');
    window.onload = function () {
        window.ui = SwaggerUIBundle({
            url: paramValue,
            dom_id: '#swagger-ui',
            deepLinking: true,
            presets: [
                SwaggerUIBundle.presets.apis,
                SwaggerUIStandalonePreset
            ],
            plugins: [
                SwaggerUIBundle.plugins.DownloadUrl
            ],
            layout: "StandaloneLayout"
        });
    }
</script>

</body>
</html>
