<configform folder="config/dtplug" filename="configform_ashshwl.xml" name="生生物流"
			helppath="生生物流接口">
	
	<input type="select" size="20" name="发送邮件入口表" id="tid_tableid" list="true" dic="$tableidgeter"/>
	<input type="input" size="20" name="	运输记录状态" id="fid_transport_mode" list="true" description="需要单选字段"/>
	<input type="input" size="20" name="	生生物流订单号" id="fid_transport_ordernumber" list="true" description="需要字符字段"/>
	<input type="input" size="20" name="	检验记录" id="fid_sender_examineid" list="true" description="需要动态表单引用字段"/>
	<input type="input" size="20" name="	发件人姓名" id="fid_sender_name" list="true" description="需要字符字段"/>
	<input type="input" size="20" name="	发件人电话" id="fid_sender_phone" list="true" description="需要字符字段"/>
	<input type="input" size="20" name="	发件人单位" id="fid_sender_unit" list="true" description="需要字符"/>
	<input type="input" size="20" name="	发件人地址" id="fid_sender_address" list="true" description="需要长字符字段"/>
	<input type="input" size="20" name="	收件人姓名" id="fid_receiver_name" list="true" description="需要字符字段"/>
	<input type="input" size="20" name="	收件人电话" id="fid_receiver_phone" list="true" description="需要字符字段"/>
	<input type="input" size="20" name="	收件人单位" id="fid_receiver_unit" list="true" description="需要字符字段"/>
	<input type="input" size="20" name="	收件人地址" id="fid_receiver_address" list="true" description="需要长字符字段"/>
	
	<input type="select" size="20" name="样本信息表" id="tid_Sample" list="true" dic="$tableidgeter"/>
	<input type="input" size="20" name="　　检验记录" id="fid_Sample_examineid" list="true" description="需要动态引用字段"/>
	<input type="input" size="20" name="　　受试者信息" id="fid_Sample_subject" list="true" description="需要动态引用字段"/>
	<input type="input" size="20" name="　　样本编号" id="fid_Sample_coder" list="true" description="需要字符字段"/>
	
	<input type="select" size="20" name="检验记录表" id="tid_examine" list="true" dic="$tableidgeter"/>
	<input type="input" size="20" name="　　检验编号" id="fid_examine_no" list="true" description="需要自动编号字段"/>
	<input type="input" size="20" name="　　检验编号" id="fid_examine_mode" list="true" description="需要单选字段"/>
	<input type="select" size="20" name="受试者信息表" id="tid_subject" list="true" dic="$tableidgeter"/>
	<input type="input" size="20" name="　　受试者代码" id="fid_subject_id" list="true" description="需要字符字段"/>
	
	
</configform>